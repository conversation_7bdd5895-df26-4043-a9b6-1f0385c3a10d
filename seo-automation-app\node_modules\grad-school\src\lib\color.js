// https://stackoverflow.com/questions/9781218/how-to-change-node-jss-console-font-color

const reset = '\x1b[0m'
//cheaper than requiring chalk
const cli = {
  // green: str => '\x1b[32m' + str + reset,
  red: str => '\x1b[31m' + str + reset,
  // blue: str => '\x1b[34m' + str + reset,
  // magenta: str => '\x1b[35m' + str + reset,
  // cyan: str => '\x1b[36m' + str + reset,
  // yellow: str => '\x1b[33m' + str + reset,
  // black: str => '\x1b[30m' + str + reset,
  dim: str => '\x1b[2m' + str + reset,
}
export default cli
