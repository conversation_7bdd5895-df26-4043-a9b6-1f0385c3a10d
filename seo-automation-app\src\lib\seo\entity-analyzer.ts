
import { LanguageServiceClient } from '@google-cloud/language';

export interface Entity {
  name: string;
  type: string;
  salience: number;
}

export async function analyzeEntities(text: string): Promise<Entity[]> {
  if (!text) {
    return [];
  }

  const client = new LanguageServiceClient();

  const document = {
    content: text,
    type: 'PLAIN_TEXT' as const,
  };

  const [result] = await client.analyzeEntities({ document });

  if (!result.entities) {
    return [];
  }

  return result.entities.map(entity => ({
    name: entity.name || '',
    type: entity.type || '',
    salience: entity.salience || 0,
  }));
}
