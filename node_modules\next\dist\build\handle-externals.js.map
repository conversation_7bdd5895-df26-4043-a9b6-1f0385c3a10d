{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "sourcesContent": ["import type { WebpackLayerName } from '../lib/constants'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { ResolveOptions } from 'webpack'\nimport { defaultOverrides } from '../server/require-hook'\nimport { BARREL_OPTIMIZATION_PREFIX } from '../shared/lib/constants'\nimport path from '../shared/lib/isomorphic/path'\nimport {\n  NODE_BASE_ESM_RESOLVE_OPTIONS,\n  NODE_BASE_RESOLVE_OPTIONS,\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from './webpack-config'\nimport { isWebpackBundledLayer, shouldUseReactServerCondition } from './utils'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nconst reactPackagesRegex = /^(react|react-dom|react-server-dom-webpack)($|\\/)/\n\nconst pathSeparators = '[/\\\\\\\\]'\nconst optionalEsmPart = `((${pathSeparators}esm)?${pathSeparators})`\nconst externalFileEnd = '(\\\\.external(\\\\.js)?)$'\nconst nextDist = `next${pathSeparators}dist`\n\nconst externalPattern = new RegExp(\n  `${nextDist}${optionalEsmPart}.*${externalFileEnd}`\n)\n\nconst nodeModulesRegex = /node_modules[/\\\\].*\\.[mc]?js$/\n\nexport function isResourceInPackages(\n  resource: string,\n  packageNames?: string[],\n  packageDirMapping?: Map<string, string>\n): boolean {\n  if (!packageNames) return false\n  return packageNames.some((p: string) =>\n    packageDirMapping && packageDirMapping.has(p)\n      ? resource.startsWith(packageDirMapping.get(p)! + path.sep)\n      : resource.includes(\n          path.sep +\n            path.join('node_modules', p.replace(/\\//g, path.sep)) +\n            path.sep\n        )\n  )\n}\n\nexport async function resolveExternal(\n  dir: string,\n  esmExternalsConfig: NextConfigComplete['experimental']['esmExternals'],\n  context: string,\n  request: string,\n  isEsmRequested: boolean,\n  getResolve: (\n    options: ResolveOptions\n  ) => (\n    resolveContext: string,\n    resolveRequest: string\n  ) => Promise<[string | null, boolean]>,\n  isLocalCallback?: (res: string) => any,\n  baseResolveCheck = true,\n  esmResolveOptions: any = NODE_ESM_RESOLVE_OPTIONS,\n  nodeResolveOptions: any = NODE_RESOLVE_OPTIONS,\n  baseEsmResolveOptions: any = NODE_BASE_ESM_RESOLVE_OPTIONS,\n  baseResolveOptions: any = NODE_BASE_RESOLVE_OPTIONS\n) {\n  const esmExternals = !!esmExternalsConfig\n  const looseEsmExternals = esmExternalsConfig === 'loose'\n\n  let res: string | null = null\n  let isEsm: boolean = false\n\n  const preferEsmOptions =\n    esmExternals && isEsmRequested ? [true, false] : [false]\n\n  for (const preferEsm of preferEsmOptions) {\n    const resolveOptions = preferEsm ? esmResolveOptions : nodeResolveOptions\n\n    const resolve = getResolve(resolveOptions)\n\n    // Resolve the import with the webpack provided context, this\n    // ensures we're resolving the correct version when multiple\n    // exist.\n    try {\n      ;[res, isEsm] = await resolve(context, request)\n    } catch (err) {\n      res = null\n    }\n\n    if (!res) {\n      continue\n    }\n\n    // ESM externals can only be imported (and not required).\n    // Make an exception in loose mode.\n    if (!isEsmRequested && isEsm && !looseEsmExternals) {\n      continue\n    }\n\n    if (isLocalCallback) {\n      return { localRes: isLocalCallback(res) }\n    }\n\n    // Bundled Node.js code is relocated without its node_modules tree.\n    // This means we need to make sure its request resolves to the same\n    // package that'll be available at runtime. If it's not identical,\n    // we need to bundle the code (even if it _should_ be external).\n    if (baseResolveCheck) {\n      let baseRes: string | null\n      let baseIsEsm: boolean\n      try {\n        const baseResolve = getResolve(\n          isEsm ? baseEsmResolveOptions : baseResolveOptions\n        )\n        ;[baseRes, baseIsEsm] = await baseResolve(dir, request)\n      } catch (err) {\n        baseRes = null\n        baseIsEsm = false\n      }\n\n      // Same as above: if the package, when required from the root,\n      // would be different from what the real resolution would use, we\n      // cannot externalize it.\n      // if request is pointing to a symlink it could point to the same file,\n      // the resolver will resolve symlinks so this is handled\n      if (baseRes !== res || isEsm !== baseIsEsm) {\n        res = null\n        continue\n      }\n    }\n    break\n  }\n  return { res, isEsm }\n}\n\nexport function makeExternalHandler({\n  config,\n  optOutBundlingPackageRegex,\n  transpiledPackages,\n  dir,\n}: {\n  config: NextConfigComplete\n  optOutBundlingPackageRegex: RegExp\n  transpiledPackages: string[]\n  dir: string\n}) {\n  let resolvedExternalPackageDirs: Map<string, string>\n  const looseEsmExternals = config.experimental?.esmExternals === 'loose'\n\n  return async function handleExternals(\n    context: string,\n    request: string,\n    dependencyType: string,\n    layer: WebpackLayerName | null,\n    getResolve: (\n      options: any\n    ) => (\n      resolveContext: string,\n      resolveRequest: string\n    ) => Promise<[string | null, boolean]>\n  ) {\n    // We need to externalize internal requests for files intended to\n    // not be bundled.\n    const isLocal: boolean =\n      request.startsWith('.') ||\n      // Always check for unix-style path, as webpack sometimes\n      // normalizes as posix.\n      path.posix.isAbsolute(request) ||\n      // When on Windows, we also want to check for Windows-specific\n      // absolute paths.\n      (process.platform === 'win32' && path.win32.isAbsolute(request))\n\n    // make sure import \"next\" shows a warning when imported\n    // in pages/components\n    if (request === 'next') {\n      return `commonjs next/dist/lib/import-next-warning`\n    }\n\n    const isAppLayer = isWebpackBundledLayer(layer)\n\n    // Relative requires don't need custom resolution, because they\n    // are relative to requests we've already resolved here.\n    // Absolute requires (require('/foo')) are extremely uncommon, but\n    // also have no need for customization as they're already resolved.\n    if (!isLocal) {\n      if (/^next$/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      if (reactPackagesRegex.test(request) && !isAppLayer) {\n        return `commonjs ${request}`\n      }\n\n      const notExternalModules =\n        /^(?:private-next-pages\\/|next\\/(?:dist\\/pages\\/|(?:app|cache|document|link|form|head|image|legacy\\/image|constants|dynamic|script|navigation|headers|router|compat\\/router|server)$)|string-hash|private-next-rsc-action-validate|private-next-rsc-action-client-wrapper|private-next-rsc-server-reference|private-next-rsc-cache-wrapper|private-next-rsc-track-dynamic-import$)/\n      if (notExternalModules.test(request)) {\n        return\n      }\n    }\n\n    // @swc/helpers should not be external as it would\n    // require hoisting the package which we can't rely on\n    if (request.includes('@swc/helpers')) {\n      return\n    }\n\n    // BARREL_OPTIMIZATION_PREFIX is a special marker that tells Next.js to\n    // optimize the import by removing unused exports. This has to be compiled.\n    if (request.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n      return\n    }\n\n    // When in esm externals mode, and using import, we resolve with\n    // ESM resolving options.\n    // Also disable esm request when appDir is enabled\n    const isEsmRequested = dependencyType === 'esm'\n\n    // Don't bundle @vercel/og nodejs bundle for nodejs runtime.\n    // TODO-APP: bundle route.js with different layer that externals common node_module deps.\n    // Make sure @vercel/og is loaded as ESM for Node.js runtime\n    if (\n      shouldUseReactServerCondition(layer) &&\n      request === 'next/dist/compiled/@vercel/og/index.node.js'\n    ) {\n      return `module ${request}`\n    }\n\n    // Specific Next.js imports that should remain external\n    // TODO-APP: Investigate if we can remove this.\n    if (request.startsWith('next/dist/')) {\n      // Non external that needs to be transpiled\n      // Image loader needs to be transpiled\n      if (/^next[\\\\/]dist[\\\\/]shared[\\\\/]lib[\\\\/]image-loader/.test(request)) {\n        return\n      }\n\n      if (/^next[\\\\/]dist[\\\\/]compiled[\\\\/]next-server/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      if (\n        /^next[\\\\/]dist[\\\\/]shared[\\\\/](?!lib[\\\\/]router[\\\\/]router)/.test(\n          request\n        ) ||\n        /^next[\\\\/]dist[\\\\/]compiled[\\\\/].*\\.c?js$/.test(request)\n      ) {\n        return `commonjs ${request}`\n      }\n\n      if (\n        /^next[\\\\/]dist[\\\\/]esm[\\\\/]shared[\\\\/](?!lib[\\\\/]router[\\\\/]router)/.test(\n          request\n        ) ||\n        /^next[\\\\/]dist[\\\\/]compiled[\\\\/].*\\.mjs$/.test(request)\n      ) {\n        return `module ${request}`\n      }\n\n      return resolveNextExternal(request)\n    }\n\n    // TODO-APP: Let's avoid this resolve call as much as possible, and eventually get rid of it.\n    const resolveResult = await resolveExternal(\n      dir,\n      config.experimental.esmExternals,\n      context,\n      request,\n      isEsmRequested,\n      getResolve,\n      isLocal ? resolveNextExternal : undefined\n    )\n\n    if ('localRes' in resolveResult) {\n      return resolveResult.localRes\n    }\n\n    // Forcedly resolve the styled-jsx installed by next.js,\n    // since `resolveExternal` cannot find the styled-jsx dep with pnpm\n    if (request === 'styled-jsx/style') {\n      resolveResult.res = defaultOverrides['styled-jsx/style']\n    }\n\n    const { res, isEsm } = resolveResult\n\n    // If the request cannot be resolved we need to have\n    // webpack \"bundle\" it so it surfaces the not found error.\n    if (!res) {\n      return\n    }\n\n    const isOptOutBundling = optOutBundlingPackageRegex.test(res)\n    // Apply bundling rules to all app layers.\n    // Since handleExternals only handle the server layers, we don't need to exclude client here\n    if (!isOptOutBundling && isAppLayer) {\n      return\n    }\n\n    // ESM externals can only be imported (and not required).\n    // Make an exception in loose mode.\n    if (!isEsmRequested && isEsm && !looseEsmExternals && !isLocal) {\n      throw new Error(\n        `ESM packages (${request}) need to be imported. Use 'import' to reference the package instead. https://nextjs.org/docs/messages/import-esm-externals`\n      )\n    }\n\n    const externalType = isEsm ? 'module' : 'commonjs'\n\n    // Default pages have to be transpiled\n    if (\n      // This is the @babel/plugin-transform-runtime \"helpers: true\" option\n      /node_modules[/\\\\]@babel[/\\\\]runtime[/\\\\]/.test(res)\n    ) {\n      return\n    }\n\n    // Webpack itself has to be compiled because it doesn't always use module relative paths\n    if (\n      /node_modules[/\\\\]webpack/.test(res) ||\n      /node_modules[/\\\\]css-loader/.test(res)\n    ) {\n      return\n    }\n\n    // If a package should be transpiled by Next.js, we skip making it external.\n    // It doesn't matter what the extension is, as we'll transpile it anyway.\n    if (transpiledPackages && !resolvedExternalPackageDirs) {\n      resolvedExternalPackageDirs = new Map()\n      // We need to resolve all the external package dirs initially.\n      for (const pkg of transpiledPackages) {\n        const pkgRes = await resolveExternal(\n          dir,\n          config.experimental.esmExternals,\n          context,\n          pkg + '/package.json',\n          isEsmRequested,\n          getResolve,\n          isLocal ? resolveNextExternal : undefined\n        )\n        if (pkgRes.res) {\n          resolvedExternalPackageDirs.set(pkg, path.dirname(pkgRes.res))\n        }\n      }\n    }\n\n    const resolvedBundlingOptOutRes = resolveBundlingOptOutPackages({\n      resolvedRes: res,\n      config,\n      resolvedExternalPackageDirs,\n      isAppLayer,\n      externalType,\n      isOptOutBundling,\n      request,\n      transpiledPackages,\n    })\n    if (resolvedBundlingOptOutRes) {\n      return resolvedBundlingOptOutRes\n    }\n\n    // if here, we default to bundling the file\n    return\n  }\n}\n\nfunction resolveBundlingOptOutPackages({\n  resolvedRes,\n  config,\n  resolvedExternalPackageDirs,\n  isAppLayer,\n  externalType,\n  isOptOutBundling,\n  request,\n  transpiledPackages,\n}: {\n  resolvedRes: string\n  config: NextConfigComplete\n  resolvedExternalPackageDirs: Map<string, string>\n  isAppLayer: boolean\n  externalType: string\n  isOptOutBundling: boolean\n  request: string\n  transpiledPackages: string[]\n}) {\n  if (nodeModulesRegex.test(resolvedRes)) {\n    const shouldBundlePages =\n      !isAppLayer && config.bundlePagesRouterDependencies && !isOptOutBundling\n\n    const shouldBeBundled =\n      shouldBundlePages ||\n      isResourceInPackages(\n        resolvedRes,\n        transpiledPackages,\n        resolvedExternalPackageDirs\n      )\n\n    if (!shouldBeBundled) {\n      return `${externalType} ${request}` // Externalize if not bundled or opted out\n    }\n  }\n}\n\n/**\n * @param localRes the full path to the file\n * @returns the externalized path\n * @description returns an externalized path if the file is a Next.js file and ends with either `.shared-runtime.js` or `.external.js`\n * This is used to ensure that files used across the rendering runtime(s) and the user code are one and the same. The logic in this function\n * will rewrite the require to the correct bundle location depending on the layer at which the file is being used.\n */\nfunction resolveNextExternal(localRes: string) {\n  const isExternal = externalPattern.test(localRes)\n\n  // if the file ends with .external, we need to make it a commonjs require in all cases\n  // this is used mainly to share the async local storage across the routing, rendering and user layers.\n  if (isExternal) {\n    // it's important we return the path that starts with `next/dist/` here instead of the absolute path\n    // otherwise NFT will get tripped up\n    return `commonjs ${normalizePathSep(\n      localRes.replace(/.*?next[/\\\\]dist/, 'next/dist')\n    )}`\n  }\n}\n"], "names": ["isResourceInPackages", "makeExternalHandler", "resolveExternal", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolveOptions", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackageRegex", "transpiledPackages", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackBundledLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "shouldUseReactServerCondition", "resolveNextExternal", "resolveResult", "undefined", "defaultOverrides", "isOptOutBundling", "Error", "externalType", "Map", "pkg", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBundlePages", "bundlePagesRouterDependencies", "shouldBeBundled", "isExternal", "normalizePathSep"], "mappings": ";;;;;;;;;;;;;;;;IA2BgBA,oBAAoB;eAApBA;;IAyGAC,mBAAmB;eAAnBA;;IAxFMC,eAAe;eAAfA;;;6BAzCW;2BACU;6DAC1B;+BAMV;uBAC8D;kCACpC;;;;;;AACjC,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,GAAGF,WAAWF,gBAAgB,EAAE,EAAEC,iBAAiB;AAGrD,MAAMI,mBAAmB;AAElB,SAASV,qBACdW,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMI,aAAI,CAACC,GAAG,IACxDT,SAASU,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBP,EAAEQ,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAelB,gBACpBsB,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAACf;IACvB,MAAMgB,oBAAoBhB,uBAAuB;IAEjD,IAAIiB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBAAgBZ,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMiB,aAAaD,iBAAkB;QACxC,MAAME,iBAAiBD,YAAYb,oBAAoBE;QAEvD,MAAMa,UAAUlB,WAAWiB;QAE3B,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;;YACD,CAACJ,KAAKC,MAAM,GAAG,MAAMI,QAAQrB,SAASC;QACzC,EAAE,OAAOqB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACd,kBAAkBe,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEmB,UAAUnB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAImB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcvB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACY,SAASC,UAAU,GAAG,MAAMC,YAAY5B,KAAKG;YACjD,EAAE,OAAOqB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,uEAAuE;YACvE,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAAS1C,oBAAoB,EAClCoD,MAAM,EACNC,0BAA0B,EAC1BC,kBAAkB,EAClB/B,GAAG,EAMJ;QAE2B6B;IAD1B,IAAIG;IACJ,MAAMf,oBAAoBY,EAAAA,uBAAAA,OAAOI,YAAY,qBAAnBJ,qBAAqBb,YAAY,MAAK;IAEhE,OAAO,eAAekB,gBACpBhC,OAAe,EACfC,OAAe,EACfgC,cAAsB,EACtBC,KAA8B,EAC9B/B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAMgC,UACJlC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBE,aAAI,CAAC2C,KAAK,CAACC,UAAU,CAACpC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBqC,QAAQC,QAAQ,KAAK,WAAW9C,aAAI,CAAC+C,KAAK,CAACH,UAAU,CAACpC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMwC,aAAaC,IAAAA,4BAAqB,EAACR;QAEzC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,SAASQ,IAAI,CAAC1C,UAAU;gBAC1B,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IAAIxB,mBAAmBkE,IAAI,CAAC1C,YAAY,CAACwC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAExC,SAAS;YAC9B;YAEA,MAAM2C,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAAC1C,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQN,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIM,QAAQV,UAAU,CAACsD,qCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAM3C,iBAAiB+B,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACEa,IAAAA,oCAA6B,EAACZ,UAC9BjC,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,SAAS;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDoD,IAAI,CAAC1C,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8C0C,IAAI,CAAC1C,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IACE,8DAA8D0C,IAAI,CAChE1C,YAEF,4CAA4C0C,IAAI,CAAC1C,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IACE,sEAAsE0C,IAAI,CACxE1C,YAEF,2CAA2C0C,IAAI,CAAC1C,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,SAAS;YAC5B;YAEA,OAAO8C,oBAAoB9C;QAC7B;QAEA,6FAA6F;QAC7F,MAAM+C,gBAAgB,MAAMxE,gBAC1BsB,KACA6B,OAAOI,YAAY,CAACjB,YAAY,EAChCd,SACAC,SACAC,gBACAC,YACAgC,UAAUY,sBAAsBE;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAczB,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAItB,YAAY,oBAAoB;YAClC+C,cAAchC,GAAG,GAAGkC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAElC,GAAG,EAAEC,KAAK,EAAE,GAAG+B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAChC,KAAK;YACR;QACF;QAEA,MAAMmC,mBAAmBvB,2BAA2Be,IAAI,CAAC3B;QACzD,0CAA0C;QAC1C,4FAA4F;QAC5F,IAAI,CAACmC,oBAAoBV,YAAY;YACnC;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACvC,kBAAkBe,SAAS,CAACF,qBAAqB,CAACoB,SAAS;YAC9D,MAAM,qBAEL,CAFK,IAAIiB,MACR,CAAC,cAAc,EAAEnD,QAAQ,2HAA2H,CAAC,GADjJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMoD,eAAepC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2C0B,IAAI,CAAC3B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B2B,IAAI,CAAC3B,QAChC,8BAA8B2B,IAAI,CAAC3B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIa,sBAAsB,CAACC,6BAA6B;YACtDA,8BAA8B,IAAIwB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAO1B,mBAAoB;gBACpC,MAAM2B,SAAS,MAAMhF,gBACnBsB,KACA6B,OAAOI,YAAY,CAACjB,YAAY,EAChCd,SACAuD,MAAM,iBACNrD,gBACAC,YACAgC,UAAUY,sBAAsBE;gBAElC,IAAIO,OAAOxC,GAAG,EAAE;oBACdc,4BAA4B2B,GAAG,CAACF,KAAK9D,aAAI,CAACiE,OAAO,CAACF,OAAOxC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM2C,4BAA4BC,8BAA8B;YAC9DC,aAAa7C;YACbW;YACAG;YACAW;YACAY;YACAF;YACAlD;YACA4B;QACF;QACA,IAAI8B,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACXlC,MAAM,EACNG,2BAA2B,EAC3BW,UAAU,EACVY,YAAY,EACZF,gBAAgB,EAChBlD,OAAO,EACP4B,kBAAkB,EAUnB;IACC,IAAI7C,iBAAiB2D,IAAI,CAACkB,cAAc;QACtC,MAAMC,oBACJ,CAACrB,cAAcd,OAAOoC,6BAA6B,IAAI,CAACZ;QAE1D,MAAMa,kBACJF,qBACAxF,qBACEuF,aACAhC,oBACAC;QAGJ,IAAI,CAACkC,iBAAiB;YACpB,OAAO,GAAGX,aAAa,CAAC,EAAEpD,SAAS,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS8C,oBAAoBxB,QAAgB;IAC3C,MAAM0C,aAAanF,gBAAgB6D,IAAI,CAACpB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAI0C,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAEC,IAAAA,kCAAgB,EACjC3C,SAAS1B,OAAO,CAAC,oBAAoB,eACpC;IACL;AACF", "ignoreList": [0]}