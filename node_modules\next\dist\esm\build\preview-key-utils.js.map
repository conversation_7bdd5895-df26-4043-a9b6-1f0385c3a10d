{"version": 3, "sources": ["../../src/build/preview-key-utils.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\nimport crypto from 'crypto'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport { getStorageDirectory } from '../server/cache-dir'\n\nconst CONFIG_FILE = '.previewinfo'\nconst PREVIEW_ID = 'previewModeId'\nconst PREVIEW_SIGNING_KEY = 'previewModeSigningKey'\nconst PREVIEW_ENCRYPTION_KEY = 'previewModeEncryptionKey'\nconst PREVIEW_EXPIRE_AT = 'expireAt'\nconst EXPIRATION = 1000 * 60 * 60 * 24 * 14 // 14 days\n\nasync function writeCache(distDir: string, config: __ApiPreviewProps) {\n  const cacheBaseDir = getStorageDirectory(distDir)\n  if (!cacheBaseDir) return\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  if (!fs.existsSync(cacheBaseDir)) {\n    await fs.promises.mkdir(cacheBaseDir, { recursive: true })\n  }\n  await fs.promises.writeFile(\n    configPath,\n    JSON.stringify({\n      [PREVIEW_ID]: config.previewModeId,\n      [PREVIEW_SIGNING_KEY]: config.previewModeSigningKey,\n      [PREVIEW_ENCRYPTION_KEY]: config.previewModeEncryptionKey,\n      [PREVIEW_EXPIRE_AT]: Date.now() + EXPIRATION,\n    })\n  )\n}\n\nfunction generateConfig() {\n  return {\n    previewModeId: crypto.randomBytes(16).toString('hex'),\n    previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n    previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n  }\n}\n\n// This utility is used to get a key for the cache directory. If the\n// key is not present, it will generate a new one and store it in the\n// cache directory inside dist.\n// The key will also expire after a certain amount of time. Once it\n// expires, a new one will be generated.\nexport async function generatePreviewKeys({\n  distDir,\n  isBuild,\n}: {\n  distDir: string\n  isBuild: boolean\n}): Promise<__ApiPreviewProps> {\n  const cacheBaseDir = getStorageDirectory(distDir)\n\n  if (!cacheBaseDir) {\n    // There's no persistent storage available. We generate a new config.\n    // This also covers development time.\n    return generateConfig()\n  }\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  async function tryReadCachedConfig(): Promise<false | __ApiPreviewProps> {\n    if (!fs.existsSync(configPath)) return false\n    try {\n      const config = JSON.parse(await fs.promises.readFile(configPath, 'utf8'))\n      if (!config) return false\n      if (\n        typeof config[PREVIEW_ID] !== 'string' ||\n        typeof config[PREVIEW_ENCRYPTION_KEY] !== 'string' ||\n        typeof config[PREVIEW_SIGNING_KEY] !== 'string' ||\n        typeof config[PREVIEW_EXPIRE_AT] !== 'number'\n      ) {\n        return false\n      }\n      // For build time, we need to rotate the key if it's expired. Otherwise\n      // (next start) we have to keep the key as it is so the runtime key matches\n      // the build time key.\n      if (isBuild && config[PREVIEW_EXPIRE_AT] < Date.now()) {\n        return false\n      }\n\n      return {\n        previewModeId: config[PREVIEW_ID],\n        previewModeSigningKey: config[PREVIEW_SIGNING_KEY],\n        previewModeEncryptionKey: config[PREVIEW_ENCRYPTION_KEY],\n      }\n    } catch (e) {\n      // Broken config file. We should generate a new key and overwrite it.\n      return false\n    }\n  }\n  const maybeValidConfig = await tryReadCachedConfig()\n  if (maybeValidConfig !== false) {\n    return maybeValidConfig\n  }\n  const config = generateConfig()\n  await writeCache(distDir, config)\n\n  return config\n}\n"], "names": ["path", "fs", "crypto", "getStorageDirectory", "CONFIG_FILE", "PREVIEW_ID", "PREVIEW_SIGNING_KEY", "PREVIEW_ENCRYPTION_KEY", "PREVIEW_EXPIRE_AT", "EXPIRATION", "writeCache", "distDir", "config", "cacheBaseDir", "config<PERSON><PERSON>", "join", "existsSync", "promises", "mkdir", "recursive", "writeFile", "JSON", "stringify", "previewModeId", "previewModeSigningKey", "previewModeEncryptionKey", "Date", "now", "generateConfig", "randomBytes", "toString", "generatePreviewKeys", "isBuild", "tryReadCachedConfig", "parse", "readFile", "e", "maybeValidConfig"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,YAAY,SAAQ;AAE3B,SAASC,mBAAmB,QAAQ,sBAAqB;AAEzD,MAAMC,cAAc;AACpB,MAAMC,aAAa;AACnB,MAAMC,sBAAsB;AAC5B,MAAMC,yBAAyB;AAC/B,MAAMC,oBAAoB;AAC1B,MAAMC,aAAa,OAAO,KAAK,KAAK,KAAK,GAAG,UAAU;;AAEtD,eAAeC,WAAWC,OAAe,EAAEC,MAAyB;IAClE,MAAMC,eAAeV,oBAAoBQ;IACzC,IAAI,CAACE,cAAc;IAEnB,MAAMC,aAAad,KAAKe,IAAI,CAACF,cAAcT;IAC3C,IAAI,CAACH,GAAGe,UAAU,CAACH,eAAe;QAChC,MAAMZ,GAAGgB,QAAQ,CAACC,KAAK,CAACL,cAAc;YAAEM,WAAW;QAAK;IAC1D;IACA,MAAMlB,GAAGgB,QAAQ,CAACG,SAAS,CACzBN,YACAO,KAAKC,SAAS,CAAC;QACb,CAACjB,WAAW,EAAEO,OAAOW,aAAa;QAClC,CAACjB,oBAAoB,EAAEM,OAAOY,qBAAqB;QACnD,CAACjB,uBAAuB,EAAEK,OAAOa,wBAAwB;QACzD,CAACjB,kBAAkB,EAAEkB,KAAKC,GAAG,KAAKlB;IACpC;AAEJ;AAEA,SAASmB;IACP,OAAO;QACLL,eAAerB,OAAO2B,WAAW,CAAC,IAAIC,QAAQ,CAAC;QAC/CN,uBAAuBtB,OAAO2B,WAAW,CAAC,IAAIC,QAAQ,CAAC;QACvDL,0BAA0BvB,OAAO2B,WAAW,CAAC,IAAIC,QAAQ,CAAC;IAC5D;AACF;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,+BAA+B;AAC/B,mEAAmE;AACnE,wCAAwC;AACxC,OAAO,eAAeC,oBAAoB,EACxCpB,OAAO,EACPqB,OAAO,EAIR;IACC,MAAMnB,eAAeV,oBAAoBQ;IAEzC,IAAI,CAACE,cAAc;QACjB,qEAAqE;QACrE,qCAAqC;QACrC,OAAOe;IACT;IAEA,MAAMd,aAAad,KAAKe,IAAI,CAACF,cAAcT;IAC3C,eAAe6B;QACb,IAAI,CAAChC,GAAGe,UAAU,CAACF,aAAa,OAAO;QACvC,IAAI;YACF,MAAMF,SAASS,KAAKa,KAAK,CAAC,MAAMjC,GAAGgB,QAAQ,CAACkB,QAAQ,CAACrB,YAAY;YACjE,IAAI,CAACF,QAAQ,OAAO;YACpB,IACE,OAAOA,MAAM,CAACP,WAAW,KAAK,YAC9B,OAAOO,MAAM,CAACL,uBAAuB,KAAK,YAC1C,OAAOK,MAAM,CAACN,oBAAoB,KAAK,YACvC,OAAOM,MAAM,CAACJ,kBAAkB,KAAK,UACrC;gBACA,OAAO;YACT;YACA,uEAAuE;YACvE,2EAA2E;YAC3E,sBAAsB;YACtB,IAAIwB,WAAWpB,MAAM,CAACJ,kBAAkB,GAAGkB,KAAKC,GAAG,IAAI;gBACrD,OAAO;YACT;YAEA,OAAO;gBACLJ,eAAeX,MAAM,CAACP,WAAW;gBACjCmB,uBAAuBZ,MAAM,CAACN,oBAAoB;gBAClDmB,0BAA0Bb,MAAM,CAACL,uBAAuB;YAC1D;QACF,EAAE,OAAO6B,GAAG;YACV,qEAAqE;YACrE,OAAO;QACT;IACF;IACA,MAAMC,mBAAmB,MAAMJ;IAC/B,IAAII,qBAAqB,OAAO;QAC9B,OAAOA;IACT;IACA,MAAMzB,SAASgB;IACf,MAAMlB,WAAWC,SAASC;IAE1B,OAAOA;AACT", "ignoreList": [0]}