'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { UserMenu } from '@/components/layout/user-menu';
import { MobileNav } from '@/components/layout/mobile-nav';
import { Badge } from '@/components/ui/badge';
import { NotificationBell } from '@/components/ui/notification-system';
import { Menu, Search, Crown } from 'lucide-react';
import { useAuthStore } from '@/store/auth';
import { useNavigationStore } from '@/store/navigation';

export function Header() {
  const { user } = useAuthStore();
  const { mobileMenuOpen, toggleMobileMenu } = useNavigationStore();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden touch-target"
            onClick={toggleMobileMenu}
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
          
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="rounded-lg bg-primary p-2">
              <Crown className="h-4 w-4 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold">SEO Generator</span>
          </Link>
        </div>

        <nav className="hidden md:flex items-center space-x-6">
          <Link
            href="/dashboard"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Dashboard
          </Link>
          <Link
            href="/dashboard/content"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Content
          </Link>
          <Link
            href="/dashboard/projects"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Projects
          </Link>
          <Link
            href="/dashboard/analytics"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Analytics
          </Link>
        </nav>

        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              Free Plan
            </Badge>
          </div>
          
          <Button variant="ghost" size="sm" className="hidden md:flex">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
          
          <NotificationBell />
          
          <UserMenu />
        </div>
      </div>

      {mobileMenuOpen && (
        <MobileNav onClose={() => toggleMobileMenu()} />
      )}
    </header>
  );
}