[{"F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\layout.tsx": "1", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\page.tsx": "2", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\button.tsx": "3", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\index.ts": "4", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\__tests__\\button.test.tsx": "5", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\client.ts": "6", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\server.ts": "7", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\cn.ts": "8", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\config.ts": "9", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\env.ts": "10", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\middleware.ts": "11", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\login\\page.tsx": "12", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\register\\page.tsx": "13", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\reset-password\\confirm\\page.tsx": "14", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\reset-password\\page.tsx": "15", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\dashboard\\page.tsx": "16", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\settings\\profile\\page.tsx": "17", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\auth-form.tsx": "18", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\auth-guard.tsx": "19", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\index.ts": "20", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\protected-route.tsx": "21", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\alert.tsx": "22", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\badge.tsx": "23", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\card.tsx": "24", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\checkbox.tsx": "25", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\input.tsx": "26", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\label.tsx": "27", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\progress.tsx": "28", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\separator.tsx": "29", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\auth.ts": "30", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\providers\\auth-provider.tsx": "31", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\auth.ts": "32", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\health\\route.ts": "33", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\metrics\\route.ts": "34", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\hooks\\useRealtime.ts": "35", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\queries.ts": "36", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\schema.ts": "37", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\setup.ts": "38", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\__tests__\\setup.test.ts": "39", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\health-checks.ts": "40", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\audit-logging.ts": "41", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\rate-limiting.ts": "42", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\validation.ts": "43", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\admin.ts": "44", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\realtime.ts": "45", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\types\\database.ts": "46", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\analytics\\performance-dashboard.tsx": "47", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\analytics\\web-vitals.tsx": "48", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\analytics\\vercel.ts": "49", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\cache\\edge-cache.ts": "50", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\env\\validation.ts": "51", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\performance.ts": "52", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\layout.tsx": "53", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\breadcrumb.tsx": "54", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\footer.tsx": "55", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\header.tsx": "56", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\index.ts": "57", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\mobile-nav.tsx": "58", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\sidebar.tsx": "59", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\user-menu.tsx": "60", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\navigation.ts": "61", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\layout.tsx": "62", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\route-guard.tsx": "63", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\dialog.tsx": "64", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\dropdown-menu.tsx": "65", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\form.tsx": "66", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\loading.tsx": "67", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\tabs.tsx": "68", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\toast.tsx": "69", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\tooltip.tsx": "70", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\error.tsx": "71", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\global-error.tsx": "72", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\loading.tsx": "73", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\not-found.tsx": "74", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\error-boundary.tsx": "75", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\content\\page.tsx": "76", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\content\\content-generator-form.tsx": "77", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\accessibility.tsx": "78", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\notification-system.tsx": "79", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\progress-indicator.tsx": "80", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\analyze\\route.ts": "81", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\batch\\route.ts": "82", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\competitors\\route.ts": "83", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\health\\route.ts": "84", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\results\\[id]\\route.ts": "85", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\admin\\ErrorMonitoringDashboard.tsx": "86", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\ComponentErrorBoundary.tsx": "87", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\GlobalErrorBoundary.tsx": "88", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\RouteErrorBoundary.tsx": "89", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\forms\\FormErrorDisplay.tsx": "90", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\ai\\serp-analysis.service.ts": "91", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\ai\\types.ts": "92", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\api\\error-handler.ts": "93", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\auth\\middleware.ts": "94", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\cache\\serp-cache.ts": "95", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\__tests__\\queries.test.ts": "96", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\design-system\\documentation.ts": "97", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\design-system\\tokens.ts": "98", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\errors\\handler.ts": "99", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\errors\\types.ts": "100", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\logging\\log-aggregator.ts": "101", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\logging\\logger.ts": "102", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\mobile\\touch-optimization.ts": "103", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\alerts.ts": "104", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\sentry.ts": "105", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\serp-monitoring.ts": "106", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\service-monitor.ts": "107", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\performance\\device-optimization.ts": "108", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\csrf.ts": "109", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\rate-limiter.ts": "110", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\index.ts": "111", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\rate-limiter.ts": "112", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serp-analysis.service.ts": "113", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serpapi-client.ts": "114", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serper-client.ts": "115", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\unified-serp.service.ts": "116", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\__tests__\\serp-analysis.service.test.ts": "117", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\__tests__\\serper-client.test.ts": "118", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\external-apis-error-handler.ts": "119", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\openai-error-handler.ts": "120", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\stripe-error-handler.ts": "121", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\supabase-error-handler.ts": "122", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\__tests__\\auth.test.ts": "123", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\accessibility-testing.ts": "124", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\cross-browser-testing.ts": "125", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\layout-testing.ts": "126", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\responsive-testing.ts": "127", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\sanitizer.ts": "128", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\schemas.ts": "129", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\__tests__\\sanitizer.test.ts": "130", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\__tests__\\schemas.test.ts": "131", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\providers\\__tests__\\auth-provider.test.tsx": "132", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\__tests__\\auth.test.ts": "133", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\types\\serp.ts": "134", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\e2e\\auth-flow.e2e.test.ts": "135", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\integration\\external-apis.integration.test.ts": "136", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\lib\\auth\\auth-utils.test.ts": "137", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\matchers.ts": "138", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\mocks\\server.ts": "139", "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\setup.ts": "140"}, {"size": 1530, "mtime": 1752670543536, "results": "141", "hashOfConfig": "142"}, {"size": 3990, "mtime": 1752639151113, "results": "143", "hashOfConfig": "142"}, {"size": 1864, "mtime": 1752639737931, "results": "144", "hashOfConfig": "142"}, {"size": 1385, "mtime": 1752669755837, "results": "145", "hashOfConfig": "142"}, {"size": 2246, "mtime": 1752640517371, "results": "146", "hashOfConfig": "142"}, {"size": 934, "mtime": 1752659222689, "results": "147", "hashOfConfig": "142"}, {"size": 2227, "mtime": 1752659240915, "results": "148", "hashOfConfig": "142"}, {"size": 165, "mtime": 1752639725816, "results": "149", "hashOfConfig": "142"}, {"size": 1449, "mtime": 1752639855964, "results": "150", "hashOfConfig": "142"}, {"size": 1638, "mtime": 1752643157217, "results": "151", "hashOfConfig": "142"}, {"size": 3503, "mtime": 1752662182003, "results": "152", "hashOfConfig": "142"}, {"size": 5865, "mtime": 1752644981682, "results": "153", "hashOfConfig": "142"}, {"size": 7733, "mtime": 1752644994555, "results": "154", "hashOfConfig": "142"}, {"size": 5732, "mtime": 1752644522912, "results": "155", "hashOfConfig": "142"}, {"size": 5314, "mtime": 1752644494012, "results": "156", "hashOfConfig": "142"}, {"size": 9356, "mtime": 1752668668737, "results": "157", "hashOfConfig": "142"}, {"size": 10956, "mtime": 1752644631750, "results": "158", "hashOfConfig": "142"}, {"size": 5604, "mtime": 1752645005418, "results": "159", "hashOfConfig": "142"}, {"size": 1240, "mtime": 1752644711760, "results": "160", "hashOfConfig": "142"}, {"size": 173, "mtime": 1752644738551, "results": "161", "hashOfConfig": "142"}, {"size": 4118, "mtime": 1752669070001, "results": "162", "hashOfConfig": "142"}, {"size": 1585, "mtime": 1752644371752, "results": "163", "hashOfConfig": "142"}, {"size": 1129, "mtime": 1752644567734, "results": "164", "hashOfConfig": "142"}, {"size": 1850, "mtime": 1752644362005, "results": "165", "hashOfConfig": "142"}, {"size": 1057, "mtime": 1752644441734, "results": "166", "hashOfConfig": "142"}, {"size": 866, "mtime": 1752663546799, "results": "167", "hashOfConfig": "142"}, {"size": 711, "mtime": 1752644351573, "results": "168", "hashOfConfig": "142"}, {"size": 778, "mtime": 1752644574414, "results": "169", "hashOfConfig": "142"}, {"size": 757, "mtime": 1752644639709, "results": "170", "hashOfConfig": "142"}, {"size": 3382, "mtime": 1752644244334, "results": "171", "hashOfConfig": "142"}, {"size": 300, "mtime": 1752644661146, "results": "172", "hashOfConfig": "142"}, {"size": 3021, "mtime": 1752644264234, "results": "173", "hashOfConfig": "142"}, {"size": 4886, "mtime": 1752659653321, "results": "174", "hashOfConfig": "142"}, {"size": 7370, "mtime": 1752659683506, "results": "175", "hashOfConfig": "142"}, {"size": 9039, "mtime": 1752659355641, "results": "176", "hashOfConfig": "142"}, {"size": 11912, "mtime": 1752659071960, "results": "177", "hashOfConfig": "142"}, {"size": 7452, "mtime": 1752658315171, "results": "178", "hashOfConfig": "142"}, {"size": 7038, "mtime": 1752658349753, "results": "179", "hashOfConfig": "142"}, {"size": 3829, "mtime": 1752658372814, "results": "180", "hashOfConfig": "142"}, {"size": 15630, "mtime": 1752659627136, "results": "181", "hashOfConfig": "142"}, {"size": 11880, "mtime": 1752659544120, "results": "182", "hashOfConfig": "142"}, {"size": 11368, "mtime": 1752659491747, "results": "183", "hashOfConfig": "142"}, {"size": 10522, "mtime": 1752659443846, "results": "184", "hashOfConfig": "142"}, {"size": 8554, "mtime": 1752659165529, "results": "185", "hashOfConfig": "142"}, {"size": 11561, "mtime": 1752659315718, "results": "186", "hashOfConfig": "142"}, {"size": 12438, "mtime": 1752659211242, "results": "187", "hashOfConfig": "142"}, {"size": 9086, "mtime": 1752662464504, "results": "188", "hashOfConfig": "142"}, {"size": 2090, "mtime": 1752662815655, "results": "189", "hashOfConfig": "142"}, {"size": 2419, "mtime": 1752662348326, "results": "190", "hashOfConfig": "142"}, {"size": 5143, "mtime": 1752662395192, "results": "191", "hashOfConfig": "142"}, {"size": 4548, "mtime": 1752662426073, "results": "192", "hashOfConfig": "142"}, {"size": 8493, "mtime": 1752662224484, "results": "193", "hashOfConfig": "142"}, {"size": 836, "mtime": 1752669081480, "results": "194", "hashOfConfig": "142"}, {"size": 1077, "mtime": 1752668413587, "results": "195", "hashOfConfig": "142"}, {"size": 1479, "mtime": 1752668374781, "results": "196", "hashOfConfig": "142"}, {"size": 3818, "mtime": 1752670566525, "results": "197", "hashOfConfig": "142"}, {"size": 231, "mtime": 1752668418459, "results": "198", "hashOfConfig": "142"}, {"size": 3788, "mtime": 1752668911243, "results": "199", "hashOfConfig": "142"}, {"size": 4094, "mtime": 1752668868467, "results": "200", "hashOfConfig": "142"}, {"size": 7452, "mtime": 1752670167726, "results": "201", "hashOfConfig": "142"}, {"size": 1669, "mtime": 1752668818732, "results": "202", "hashOfConfig": "142"}, {"size": 465, "mtime": 1752669100725, "results": "203", "hashOfConfig": "142"}, {"size": 1609, "mtime": 1752669095096, "results": "204", "hashOfConfig": "142"}, {"size": 4596, "mtime": 1752669349691, "results": "205", "hashOfConfig": "142"}, {"size": 6674, "mtime": 1752669402861, "results": "206", "hashOfConfig": "142"}, {"size": 4556, "mtime": 1752669497558, "results": "207", "hashOfConfig": "142"}, {"size": 4436, "mtime": 1752669430768, "results": "208", "hashOfConfig": "142"}, {"size": 3586, "mtime": 1752669474427, "results": "209", "hashOfConfig": "142"}, {"size": 5078, "mtime": 1752669455915, "results": "210", "hashOfConfig": "142"}, {"size": 4136, "mtime": 1752669373234, "results": "211", "hashOfConfig": "142"}, {"size": 2156, "mtime": 1752669702149, "results": "212", "hashOfConfig": "142"}, {"size": 1446, "mtime": 1752669713163, "results": "213", "hashOfConfig": "142"}, {"size": 152, "mtime": 1752669718594, "results": "214", "hashOfConfig": "142"}, {"size": 1345, "mtime": 1752669728551, "results": "215", "hashOfConfig": "142"}, {"size": 5075, "mtime": 1752669690686, "results": "216", "hashOfConfig": "142"}, {"size": 7009, "mtime": 1752670263554, "results": "217", "hashOfConfig": "142"}, {"size": 15927, "mtime": 1752670232563, "results": "218", "hashOfConfig": "142"}, {"size": 13871, "mtime": 1752670315657, "results": "219", "hashOfConfig": "142"}, {"size": 10249, "mtime": 1752670086945, "results": "220", "hashOfConfig": "142"}, {"size": 8073, "mtime": 1752670042627, "results": "221", "hashOfConfig": "142"}, {"size": 4645, "mtime": 1752676229520, "results": "222", "hashOfConfig": "142"}, {"size": 6754, "mtime": 1752676476700, "results": "223", "hashOfConfig": "142"}, {"size": 4998, "mtime": 1752676441097, "results": "224", "hashOfConfig": "142"}, {"size": 1975, "mtime": 1752676492069, "results": "225", "hashOfConfig": "142"}, {"size": 3728, "mtime": 1752676411804, "results": "226", "hashOfConfig": "142"}, {"size": 14144, "mtime": 1752673846522, "results": "227", "hashOfConfig": "142"}, {"size": 10689, "mtime": 1752671763042, "results": "228", "hashOfConfig": "142"}, {"size": 9368, "mtime": 1752671288973, "results": "229", "hashOfConfig": "142"}, {"size": 8238, "mtime": 1752671327696, "results": "230", "hashOfConfig": "142"}, {"size": 10456, "mtime": 1752673783042, "results": "231", "hashOfConfig": "142"}, {"size": 9339, "mtime": 1752674907288, "results": "232", "hashOfConfig": "142"}, {"size": 3532, "mtime": 1752674862583, "results": "233", "hashOfConfig": "142"}, {"size": 11717, "mtime": 1752671385284, "results": "234", "hashOfConfig": "142"}, {"size": 1708, "mtime": 1752676507449, "results": "235", "hashOfConfig": "142"}, {"size": 6079, "mtime": 1752676156987, "results": "236", "hashOfConfig": "142"}, {"size": 35293, "mtime": 1752672828490, "results": "237", "hashOfConfig": "142"}, {"size": 16192, "mtime": 1752674934926, "results": "238", "hashOfConfig": "142"}, {"size": 7325, "mtime": 1752674189214, "results": "239", "hashOfConfig": "142"}, {"size": 11269, "mtime": 1752671082171, "results": "240", "hashOfConfig": "142"}, {"size": 6275, "mtime": 1752671034457, "results": "241", "hashOfConfig": "142"}, {"size": 14907, "mtime": 1752671969255, "results": "242", "hashOfConfig": "142"}, {"size": 9129, "mtime": 1752671126946, "results": "243", "hashOfConfig": "142"}, {"size": 17779, "mtime": 1752674456949, "results": "244", "hashOfConfig": "142"}, {"size": 18171, "mtime": 1752672058457, "results": "245", "hashOfConfig": "142"}, {"size": 10617, "mtime": 1752671544779, "results": "246", "hashOfConfig": "142"}, {"size": 6482, "mtime": 1752676542831, "results": "247", "hashOfConfig": "142"}, {"size": 9427, "mtime": 1752671888397, "results": "248", "hashOfConfig": "142"}, {"size": 17217, "mtime": 1752674757301, "results": "249", "hashOfConfig": "142"}, {"size": 9063, "mtime": 1752671595966, "results": "250", "hashOfConfig": "142"}, {"size": 10501, "mtime": 1752671648341, "results": "251", "hashOfConfig": "142"}, {"size": 1014, "mtime": 1752676664532, "results": "252", "hashOfConfig": "142"}, {"size": 3808, "mtime": 1752675964147, "results": "253", "hashOfConfig": "142"}, {"size": 6882, "mtime": 1752675903756, "results": "254", "hashOfConfig": "142"}, {"size": 5110, "mtime": 1752675997499, "results": "255", "hashOfConfig": "142"}, {"size": 3732, "mtime": 1752675759327, "results": "256", "hashOfConfig": "142"}, {"size": 7319, "mtime": 1752676040992, "results": "257", "hashOfConfig": "142"}, {"size": 7432, "mtime": 1752676641645, "results": "258", "hashOfConfig": "142"}, {"size": 4654, "mtime": 1752676961998, "results": "259", "hashOfConfig": "142"}, {"size": 13064, "mtime": 1752673723791, "results": "260", "hashOfConfig": "142"}, {"size": 13461, "mtime": 1752673663756, "results": "261", "hashOfConfig": "142"}, {"size": 12355, "mtime": 1752673597678, "results": "262", "hashOfConfig": "142"}, {"size": 10773, "mtime": 1752673539684, "results": "263", "hashOfConfig": "142"}, {"size": 15969, "mtime": 1752672502657, "results": "264", "hashOfConfig": "142"}, {"size": 14620, "mtime": 1752674269782, "results": "265", "hashOfConfig": "142"}, {"size": 15334, "mtime": 1752674353161, "results": "266", "hashOfConfig": "142"}, {"size": 17751, "mtime": 1752674846029, "results": "267", "hashOfConfig": "142"}, {"size": 14885, "mtime": 1752674061799, "results": "268", "hashOfConfig": "142"}, {"size": 10210, "mtime": 1752671239069, "results": "269", "hashOfConfig": "142"}, {"size": 10195, "mtime": 1752671183934, "results": "270", "hashOfConfig": "142"}, {"size": 28812, "mtime": 1752673284626, "results": "271", "hashOfConfig": "142"}, {"size": 32128, "mtime": 1752673427250, "results": "272", "hashOfConfig": "142"}, {"size": 9827, "mtime": 1752672624092, "results": "273", "hashOfConfig": "142"}, {"size": 13764, "mtime": 1752672580387, "results": "274", "hashOfConfig": "142"}, {"size": 2320, "mtime": 1752675936915, "results": "275", "hashOfConfig": "142"}, {"size": 12573, "mtime": 1752675764405, "results": "276", "hashOfConfig": "142"}, {"size": 12325, "mtime": 1752675578188, "results": "277", "hashOfConfig": "142"}, {"size": 12040, "mtime": 1752675504639, "results": "278", "hashOfConfig": "142"}, {"size": 9186, "mtime": 1752675379806, "results": "279", "hashOfConfig": "142"}, {"size": 8634, "mtime": 1752675436162, "results": "280", "hashOfConfig": "142"}, {"size": 6772, "mtime": 1752675327287, "results": "281", "hashOfConfig": "142"}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ltwe5n", {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 31, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 30, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\layout.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\page.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\button.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\index.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\__tests__\\button.test.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\client.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\server.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\cn.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\config.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\utils\\env.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\middleware.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\login\\page.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\register\\page.tsx", ["702", "703"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\reset-password\\confirm\\page.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\reset-password\\page.tsx", ["704", "705", "706"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\dashboard\\page.tsx", ["707", "708"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\settings\\profile\\page.tsx", ["709", "710"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\auth-form.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\auth-guard.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\index.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\protected-route.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\alert.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\badge.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\card.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\checkbox.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\input.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\label.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\progress.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\separator.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\auth.ts", ["711", "712"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\providers\\auth-provider.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\auth.ts", ["713"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\health\\route.ts", ["714", "715", "716", "717"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\metrics\\route.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\hooks\\useRealtime.ts", ["718", "719"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\queries.ts", ["720"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\schema.ts", ["721", "722", "723", "724", "725", "726", "727", "728", "729", "730"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\setup.ts", ["731", "732", "733", "734", "735", "736", "737", "738"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\__tests__\\setup.test.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\health-checks.ts", ["739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\audit-logging.ts", ["750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\rate-limiting.ts", ["766", "767"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\validation.ts", ["768", "769", "770"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\admin.ts", ["771", "772"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\realtime.ts", ["773", "774", "775", "776", "777"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\types\\database.ts", ["778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\analytics\\performance-dashboard.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\analytics\\web-vitals.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\analytics\\vercel.ts", ["809", "810", "811"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\cache\\edge-cache.ts", ["812", "813", "814"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\env\\validation.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\performance.ts", ["815"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\layout.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\breadcrumb.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\footer.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\header.tsx", ["816"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\index.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\mobile-nav.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\sidebar.tsx", ["817"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\layout\\user-menu.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\navigation.ts", ["818"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(auth)\\layout.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\auth\\route-guard.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\dialog.tsx", ["819"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\dropdown-menu.tsx", ["820", "821"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\form.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\loading.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\tabs.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\toast.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\tooltip.tsx", ["822"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\error.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\global-error.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\loading.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\not-found.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\error-boundary.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\(dashboard)\\content\\page.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\content\\content-generator-form.tsx", ["823", "824"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\accessibility.tsx", ["825", "826", "827", "828", "829", "830", "831"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\notification-system.tsx", ["832", "833", "834", "835"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\ui\\progress-indicator.tsx", ["836"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\analyze\\route.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\batch\\route.ts", ["837", "838", "839", "840"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\competitors\\route.ts", ["841", "842"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\health\\route.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\app\\api\\serp\\results\\[id]\\route.ts", ["843"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\admin\\ErrorMonitoringDashboard.tsx", ["844", "845", "846", "847", "848", "849", "850"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\ComponentErrorBoundary.tsx", ["851", "852"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\GlobalErrorBoundary.tsx", ["853", "854", "855"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\error-boundaries\\RouteErrorBoundary.tsx", ["856", "857"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\components\\forms\\FormErrorDisplay.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\ai\\serp-analysis.service.ts", ["858", "859", "860", "861"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\ai\\types.ts", ["862"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\api\\error-handler.ts", ["863", "864", "865", "866", "867"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\auth\\middleware.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\cache\\serp-cache.ts", ["868", "869", "870", "871"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\database\\__tests__\\queries.test.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\design-system\\documentation.ts", ["872", "873", "874"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\design-system\\tokens.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\errors\\handler.ts", ["875"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\errors\\types.ts", ["876"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\logging\\log-aggregator.ts", ["877", "878", "879", "880", "881", "882", "883", "884", "885"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\logging\\logger.ts", ["886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\mobile\\touch-optimization.ts", ["903"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\alerts.ts", ["904", "905", "906", "907", "908", "909", "910", "911", "912", "913"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\sentry.ts", ["914", "915", "916", "917", "918", "919", "920"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\serp-monitoring.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\monitoring\\service-monitor.ts", ["921", "922", "923", "924", "925", "926", "927"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\performance\\device-optimization.ts", ["928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\csrf.ts", ["940"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\security\\rate-limiter.ts", ["941", "942"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\index.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\rate-limiter.ts", ["943", "944"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serp-analysis.service.ts", ["945"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serpapi-client.ts", ["946", "947"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\serper-client.ts", ["948"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\unified-serp.service.ts", ["949", "950", "951", "952"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\__tests__\\serp-analysis.service.test.ts", ["953"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\serp\\__tests__\\serper-client.test.ts", ["954", "955", "956", "957", "958", "959", "960", "961"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\external-apis-error-handler.ts", ["962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\openai-error-handler.ts", ["979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\stripe-error-handler.ts", ["993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\services\\supabase-error-handler.ts", ["1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\supabase\\__tests__\\auth.test.ts", ["1035", "1036"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\accessibility-testing.ts", ["1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\cross-browser-testing.ts", ["1046"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\layout-testing.ts", ["1047"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\testing\\responsive-testing.ts", ["1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\sanitizer.ts", ["1057", "1058", "1059", "1060", "1061"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\schemas.ts", ["1062"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\__tests__\\sanitizer.test.ts", ["1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\lib\\validation\\__tests__\\schemas.test.ts", ["1095", "1096", "1097", "1098"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\providers\\__tests__\\auth-provider.test.tsx", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\store\\__tests__\\auth.test.ts", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\types\\serp.ts", ["1109"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\e2e\\auth-flow.e2e.test.ts", [], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\integration\\external-apis.integration.test.ts", ["1110", "1111", "1112", "1113"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\lib\\auth\\auth-utils.test.ts", ["1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121", "1122"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\matchers.ts", ["1123", "1124", "1125", "1126", "1127"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\mocks\\server.ts", ["1128"], [], "F:\\Claude-Code-Setup\\15 July seo automation app\\seo-automation-app\\src\\__tests__\\setup.ts", ["1129"], [], {"ruleId": "1130", "severity": 1, "message": "1131", "line": 4, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 4, "endColumn": 19}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 81, "column": 17, "nodeType": "1135", "messageId": "1136", "suggestions": "1137"}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 61, "column": 17, "nodeType": "1135", "messageId": "1136", "suggestions": "1138"}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 70, "column": 21, "nodeType": "1135", "messageId": "1136", "suggestions": "1139"}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 97, "column": 44, "nodeType": "1135", "messageId": "1136", "suggestions": "1140"}, {"ruleId": "1130", "severity": 1, "message": "1141", "line": 11, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 11, "endColumn": 13}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 189, "column": 16, "nodeType": "1135", "messageId": "1136", "suggestions": "1142"}, {"ruleId": "1130", "severity": 1, "message": "1143", "line": 12, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 12, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1144", "line": 13, "column": 25, "nodeType": null, "messageId": "1132", "endLine": 13, "endColumn": 29}, {"ruleId": "1130", "severity": 1, "message": "1145", "line": 2, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 2, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1146", "line": 2, "column": 42, "nodeType": null, "messageId": "1132", "endLine": 2, "endColumn": 49}, {"ruleId": "1130", "severity": 1, "message": "1147", "line": 36, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 36, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1148", "line": 7, "column": 24, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1149", "line": 7, "column": 39, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 51}, {"ruleId": "1130", "severity": 1, "message": "1150", "line": 7, "column": 53, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 69}, {"ruleId": "1130", "severity": 1, "message": "1151", "line": 8, "column": 38, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 51}, {"ruleId": "1130", "severity": 1, "message": "1152", "line": 14, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 14, "endColumn": 18}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 222, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 222, "endColumn": 15, "suggestions": "1157"}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 469, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 469, "endColumn": 19}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 31, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 31, "endColumn": 31, "suggestions": "1159"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 50, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 50, "endColumn": 31, "suggestions": "1160"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 27, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 30, "suggestions": "1161"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 64, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 64, "endColumn": 40, "suggestions": "1162"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 78, "column": 19, "nodeType": "1155", "messageId": "1156", "endLine": 78, "endColumn": 22, "suggestions": "1163"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 79, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 79, "endColumn": 28, "suggestions": "1164"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 80, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 80, "endColumn": 28, "suggestions": "1165"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 81, "column": 17, "nodeType": "1155", "messageId": "1156", "endLine": 81, "endColumn": 20, "suggestions": "1166"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 82, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 82, "endColumn": 36, "suggestions": "1167"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 94, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 94, "endColumn": 31, "suggestions": "1168"}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 23, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 23, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 29, "column": 12, "nodeType": null, "messageId": "1132", "endLine": 29, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1170", "line": 37, "column": 54, "nodeType": null, "messageId": "1132", "endLine": 37, "endColumn": 57}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 42, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 42, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 137, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 137, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1171", "line": 182, "column": 9, "nodeType": null, "messageId": "1132", "endLine": 182, "endColumn": 16}, {"ruleId": "1130", "severity": 1, "message": "1172", "line": 208, "column": 9, "nodeType": null, "messageId": "1132", "endLine": 208, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1173", "line": 218, "column": 9, "nodeType": null, "messageId": "1132", "endLine": 218, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1174", "line": 7, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1175", "line": 8, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 23}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 31, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 31, "endColumn": 31, "suggestions": "1176"}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 83, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 83, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 176, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 176, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 185, "column": 14, "nodeType": null, "messageId": "1132", "endLine": 185, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 225, "column": 14, "nodeType": null, "messageId": "1132", "endLine": 225, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 240, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 240, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 256, "column": 14, "nodeType": null, "messageId": "1132", "endLine": 256, "endColumn": 19}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 466, "column": 32, "nodeType": "1155", "messageId": "1156", "endLine": 466, "endColumn": 35, "suggestions": "1177"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 467, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 467, "endColumn": 38, "suggestions": "1178"}, {"ruleId": "1130", "severity": 1, "message": "1175", "line": 7, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 23}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 81, "column": 29, "nodeType": "1155", "messageId": "1156", "endLine": 81, "endColumn": 32, "suggestions": "1179"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 149, "column": 39, "nodeType": "1155", "messageId": "1156", "endLine": 149, "endColumn": 42, "suggestions": "1180"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 213, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 213, "endColumn": 34, "suggestions": "1181"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 239, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 239, "endColumn": 34, "suggestions": "1182"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 265, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 265, "endColumn": 34, "suggestions": "1183"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 289, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 289, "endColumn": 34, "suggestions": "1184"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 311, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 311, "endColumn": 34, "suggestions": "1185"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 334, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 334, "endColumn": 31, "suggestions": "1186"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 337, "column": 49, "nodeType": "1155", "messageId": "1156", "endLine": 337, "endColumn": 52, "suggestions": "1187"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 343, "column": 19, "nodeType": "1155", "messageId": "1156", "endLine": 343, "endColumn": 22, "suggestions": "1188"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 381, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 381, "endColumn": 38, "suggestions": "1189"}, {"ruleId": "1130", "severity": 1, "message": "1190", "line": 451, "column": 5, "nodeType": null, "messageId": "1132", "endLine": 451, "endColumn": 13}, {"ruleId": "1130", "severity": 1, "message": "1191", "line": 452, "column": 5, "nodeType": null, "messageId": "1132", "endLine": 452, "endColumn": 11}, {"ruleId": "1130", "severity": 1, "message": "1190", "line": 476, "column": 5, "nodeType": null, "messageId": "1132", "endLine": 476, "endColumn": 13}, {"ruleId": "1130", "severity": 1, "message": "1191", "line": 477, "column": 5, "nodeType": null, "messageId": "1132", "endLine": 477, "endColumn": 11}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 344, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 344, "endColumn": 31, "suggestions": "1192"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 347, "column": 49, "nodeType": "1155", "messageId": "1156", "endLine": 347, "endColumn": 52, "suggestions": "1193"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 238, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 238, "endColumn": 28, "suggestions": "1194"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 238, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 238, "endColumn": 34, "suggestions": "1195"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 248, "column": 24, "nodeType": "1155", "messageId": "1156", "endLine": 248, "endColumn": 27, "suggestions": "1196"}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 250, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 250, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1158", "line": 279, "column": 19, "nodeType": null, "messageId": "1132", "endLine": 279, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1197", "line": 9, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 7}, {"ruleId": "1130", "severity": 1, "message": "1198", "line": 13, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 13, "endColumn": 16}, {"ruleId": "1130", "severity": 1, "message": "1199", "line": 21, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 21, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1200", "line": 24, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 24, "endColumn": 27}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 373, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 373, "endColumn": 17, "suggestions": "1201"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 60, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 60, "endColumn": 39, "suggestions": "1202"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 74, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 74, "endColumn": 40, "suggestions": "1203"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 88, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 88, "endColumn": 40, "suggestions": "1204"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 107, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 107, "endColumn": 39, "suggestions": "1205"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 123, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 123, "endColumn": 40, "suggestions": "1206"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 139, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 139, "endColumn": 40, "suggestions": "1207"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 151, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 151, "endColumn": 38, "suggestions": "1208"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 153, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 153, "endColumn": 48, "suggestions": "1209"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 163, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 163, "endColumn": 38, "suggestions": "1210"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 165, "column": 46, "nodeType": "1155", "messageId": "1156", "endLine": 165, "endColumn": 49, "suggestions": "1211"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 175, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 175, "endColumn": 39, "suggestions": "1212"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 177, "column": 46, "nodeType": "1155", "messageId": "1156", "endLine": 177, "endColumn": 49, "suggestions": "1213"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 191, "column": 27, "nodeType": "1155", "messageId": "1156", "endLine": 191, "endColumn": 30, "suggestions": "1214"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 192, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 192, "endColumn": 36, "suggestions": "1215"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 193, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 193, "endColumn": 36, "suggestions": "1216"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 194, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 194, "endColumn": 28, "suggestions": "1217"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 195, "column": 41, "nodeType": "1155", "messageId": "1156", "endLine": 195, "endColumn": 44, "suggestions": "1218"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 208, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 208, "endColumn": 31, "suggestions": "1219"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 209, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 209, "endColumn": 37, "suggestions": "1220"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 210, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 210, "endColumn": 37, "suggestions": "1221"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 211, "column": 26, "nodeType": "1155", "messageId": "1156", "endLine": 211, "endColumn": 29, "suggestions": "1222"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 212, "column": 42, "nodeType": "1155", "messageId": "1156", "endLine": 212, "endColumn": 45, "suggestions": "1223"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 225, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 225, "endColumn": 31, "suggestions": "1224"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 226, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 226, "endColumn": 37, "suggestions": "1225"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 227, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 227, "endColumn": 37, "suggestions": "1226"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 228, "column": 26, "nodeType": "1155", "messageId": "1156", "endLine": 228, "endColumn": 29, "suggestions": "1227"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 229, "column": 42, "nodeType": "1155", "messageId": "1156", "endLine": 229, "endColumn": 45, "suggestions": "1228"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 241, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 241, "endColumn": 39, "suggestions": "1229"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 253, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 253, "endColumn": 40, "suggestions": "1230"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 265, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 265, "endColumn": 40, "suggestions": "1231"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 388, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 388, "endColumn": 38, "suggestions": "1232"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 5, "column": 75, "nodeType": "1155", "messageId": "1156", "endLine": 5, "endColumn": 78, "suggestions": "1233"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 40, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 43, "suggestions": "1234"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 92, "column": 68, "nodeType": "1155", "messageId": "1156", "endLine": 92, "endColumn": 71, "suggestions": "1235"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 82, "column": 41, "nodeType": "1155", "messageId": "1156", "endLine": 82, "endColumn": 44, "suggestions": "1236"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 106, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 106, "endColumn": 48, "suggestions": "1237"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 119, "column": 26, "nodeType": "1155", "messageId": "1156", "endLine": 119, "endColumn": 29, "suggestions": "1238"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1239", "line": 1, "column": 70}, {"ruleId": "1130", "severity": 1, "message": "1240", "line": 14, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 14, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1241", "line": 36, "column": 5, "nodeType": null, "messageId": "1132", "endLine": 36, "endColumn": 18}, {"ruleId": "1130", "severity": 1, "message": "1147", "line": 22, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 22, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1242", "line": 43, "column": 83, "nodeType": null, "messageId": "1132", "endLine": 43, "endColumn": 86}, {"ruleId": "1130", "severity": 1, "message": "1243", "line": 5, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 5, "endColumn": 21}, {"ruleId": "1244", "severity": 1, "message": "1245", "line": 131, "column": 6, "nodeType": "1246", "endLine": 131, "endColumn": 27, "suggestions": "1247"}, {"ruleId": "1244", "severity": 1, "message": "1245", "line": 95, "column": 6, "nodeType": "1246", "endLine": 95, "endColumn": 23, "suggestions": "1248"}, {"ruleId": "1130", "severity": 1, "message": "1249", "line": 19, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 19, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1250", "line": 28, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 28, "endColumn": 11}, {"ruleId": "1130", "severity": 1, "message": "1251", "line": 4, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 4, "endColumn": 12}, {"ruleId": "1130", "severity": 1, "message": "1252", "line": 11, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 11, "endColumn": 6}, {"ruleId": "1130", "severity": 1, "message": "1253", "line": 14, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 14, "endColumn": 10}, {"ruleId": "1130", "severity": 1, "message": "1254", "line": 17, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 17, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1250", "line": 21, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 21, "endColumn": 11}, {"ruleId": "1130", "severity": 1, "message": "1255", "line": 22, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 22, "endColumn": 10}, {"ruleId": "1130", "severity": 1, "message": "1256", "line": 44, "column": 55, "nodeType": null, "messageId": "1132", "endLine": 44, "endColumn": 64}, {"ruleId": "1130", "severity": 1, "message": "1257", "line": 5, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 5, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1258", "line": 5, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 5, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1250", "line": 15, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 15, "endColumn": 11}, {"ruleId": "1130", "severity": 1, "message": "1259", "line": 16, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 16, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1260", "line": 39, "column": 21, "nodeType": null, "messageId": "1132", "endLine": 39, "endColumn": 33}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 94, "column": 16, "nodeType": "1155", "messageId": "1156", "endLine": 94, "endColumn": 19, "suggestions": "1261"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 95, "column": 17, "nodeType": "1155", "messageId": "1156", "endLine": 95, "endColumn": 20, "suggestions": "1262"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 96, "column": 13, "nodeType": "1155", "messageId": "1156", "endLine": 96, "endColumn": 16, "suggestions": "1263"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 98, "column": 18, "nodeType": "1155", "messageId": "1156", "endLine": 98, "endColumn": 21, "suggestions": "1264"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 69, "column": 64, "nodeType": "1155", "messageId": "1156", "endLine": 69, "endColumn": 67, "suggestions": "1265"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 79, "column": 44, "nodeType": "1155", "messageId": "1156", "endLine": 79, "endColumn": 47, "suggestions": "1266"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 111, "column": 42, "nodeType": "1155", "messageId": "1156", "endLine": 111, "endColumn": 45, "suggestions": "1267"}, {"ruleId": "1130", "severity": 1, "message": "1268", "line": 11, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 11, "endColumn": 13}, {"ruleId": "1130", "severity": 1, "message": "1269", "line": 18, "column": 3, "nodeType": null, "messageId": "1132", "endLine": 18, "endColumn": 9}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 40, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 40, "endColumn": 31, "suggestions": "1270"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 48, "column": 52, "nodeType": "1155", "messageId": "1156", "endLine": 48, "endColumn": 55, "suggestions": "1271"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 49, "column": 40, "nodeType": "1155", "messageId": "1156", "endLine": 49, "endColumn": 43, "suggestions": "1272"}, {"ruleId": "1244", "severity": 1, "message": "1273", "line": 57, "column": 6, "nodeType": "1246", "endLine": 57, "endColumn": 17, "suggestions": "1274"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 209, "column": 61, "nodeType": "1155", "messageId": "1156", "endLine": 209, "endColumn": 64, "suggestions": "1275"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 332, "column": 68, "nodeType": "1155", "messageId": "1156", "endLine": 332, "endColumn": 71, "suggestions": "1276"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 350, "column": 62, "nodeType": "1155", "messageId": "1156", "endLine": 350, "endColumn": 65, "suggestions": "1277"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 273, "column": 59, "nodeType": "1155", "messageId": "1156", "endLine": 273, "endColumn": 62, "suggestions": "1278"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 288, "column": 61, "nodeType": "1155", "messageId": "1156", "endLine": 288, "endColumn": 64, "suggestions": "1279"}, {"ruleId": "1130", "severity": 1, "message": "1280", "line": 289, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 289, "endColumn": 18}, {"ruleId": "1133", "severity": 2, "message": "1134", "line": 147, "column": 56, "nodeType": "1135", "messageId": "1136", "suggestions": "1281"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 262, "column": 64, "nodeType": "1155", "messageId": "1156", "endLine": 262, "endColumn": 67, "suggestions": "1282"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 61, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 64, "suggestions": "1283"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 85, "column": 78, "nodeType": "1155", "messageId": "1156", "endLine": 85, "endColumn": 81, "suggestions": "1284"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 111, "column": 51, "nodeType": "1155", "messageId": "1156", "endLine": 111, "endColumn": 54, "suggestions": "1285"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 157, "column": 61, "nodeType": "1155", "messageId": "1156", "endLine": 157, "endColumn": 64, "suggestions": "1286"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 133, "column": 13, "nodeType": "1155", "messageId": "1156", "endLine": 133, "endColumn": 16, "suggestions": "1287"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 21, "column": 10, "nodeType": "1155", "messageId": "1156", "endLine": 21, "endColumn": 13, "suggestions": "1288"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 26, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 26, "endColumn": 31, "suggestions": "1289"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 167, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 167, "endColumn": 15, "suggestions": "1290"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 410, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 410, "endColumn": 48, "suggestions": "1291"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 413, "column": 44, "nodeType": "1155", "messageId": "1156", "endLine": 413, "endColumn": 47, "suggestions": "1292"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 12, "column": 47, "nodeType": "1155", "messageId": "1156", "endLine": 12, "endColumn": 50, "suggestions": "1293"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 13, "column": 21, "nodeType": "1155", "messageId": "1156", "endLine": 13, "endColumn": 24, "suggestions": "1294"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 164, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 164, "endColumn": 40, "suggestions": "1295"}, {"ruleId": "1130", "severity": 1, "message": "1296", "line": 170, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 170, "endColumn": 14}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 25, "column": 18, "nodeType": "1155", "messageId": "1156", "endLine": 25, "endColumn": 21, "suggestions": "1297"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 33, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 33, "endColumn": 28, "suggestions": "1298"}, {"ruleId": "1130", "severity": 1, "message": "1299", "line": 222, "column": 24, "nodeType": null, "messageId": "1132", "endLine": 222, "endColumn": 34}, {"ruleId": "1130", "severity": 1, "message": "1300", "line": 168, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 168, "endColumn": 15}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 28, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 28, "endColumn": 38, "suggestions": "1301"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 79, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 79, "endColumn": 39, "suggestions": "1302"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 80, "column": 16, "nodeType": "1155", "messageId": "1156", "endLine": 80, "endColumn": 19, "suggestions": "1303"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 80, "column": 87, "nodeType": "1155", "messageId": "1156", "endLine": 80, "endColumn": 90, "suggestions": "1304"}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 114, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 114, "endColumn": 17}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 121, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 121, "endColumn": 39, "suggestions": "1306"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 122, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 122, "endColumn": 36, "suggestions": "1307"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 123, "column": 29, "nodeType": "1155", "messageId": "1156", "endLine": 123, "endColumn": 32, "suggestions": "1308"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 201, "column": 19, "nodeType": "1155", "messageId": "1156", "endLine": 201, "endColumn": 22, "suggestions": "1309"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 201, "column": 32, "nodeType": "1155", "messageId": "1156", "endLine": 201, "endColumn": 35, "suggestions": "1310"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 23, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 23, "endColumn": 31, "suggestions": "1311"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 65, "column": 58, "nodeType": "1155", "messageId": "1156", "endLine": 65, "endColumn": 61, "suggestions": "1312"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 72, "column": 57, "nodeType": "1155", "messageId": "1156", "endLine": 72, "endColumn": 60, "suggestions": "1313"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 79, "column": 57, "nodeType": "1155", "messageId": "1156", "endLine": 79, "endColumn": 60, "suggestions": "1314"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 86, "column": 58, "nodeType": "1155", "messageId": "1156", "endLine": 86, "endColumn": 61, "suggestions": "1315"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 93, "column": 74, "nodeType": "1155", "messageId": "1156", "endLine": 93, "endColumn": 77, "suggestions": "1316"}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 148, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 148, "endColumn": 17}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 156, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 156, "endColumn": 39, "suggestions": "1317"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 157, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 157, "endColumn": 36, "suggestions": "1318"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 158, "column": 29, "nodeType": "1155", "messageId": "1156", "endLine": 158, "endColumn": 32, "suggestions": "1319"}, {"ruleId": "1130", "severity": 1, "message": "1320", "line": 214, "column": 15, "nodeType": null, "messageId": "1132", "endLine": 214, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 246, "column": 40, "nodeType": "1155", "messageId": "1156", "endLine": 246, "endColumn": 43, "suggestions": "1321"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 251, "column": 93, "nodeType": "1155", "messageId": "1156", "endLine": 251, "endColumn": 96, "suggestions": "1322"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 262, "column": 65, "nodeType": "1155", "messageId": "1156", "endLine": 262, "endColumn": 68, "suggestions": "1323"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 278, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 278, "endColumn": 33, "suggestions": "1324"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 294, "column": 65, "nodeType": "1155", "messageId": "1156", "endLine": 294, "endColumn": 68, "suggestions": "1325"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 317, "column": 117, "nodeType": "1155", "messageId": "1156", "endLine": 317, "endColumn": 120, "suggestions": "1326"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 350, "column": 66, "nodeType": "1155", "messageId": "1156", "endLine": 350, "endColumn": 69, "suggestions": "1327"}, {"ruleId": "1130", "severity": 1, "message": "1328", "line": 9, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1329", "line": 9, "column": 28, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1330", "line": 9, "column": 39, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 52}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 33, "column": 27, "nodeType": "1155", "messageId": "1156", "endLine": 33, "endColumn": 30, "suggestions": "1331"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 45, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 45, "endColumn": 31, "suggestions": "1332"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 269, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 269, "endColumn": 36, "suggestions": "1333"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 328, "column": 86, "nodeType": "1155", "messageId": "1156", "endLine": 328, "endColumn": 89, "suggestions": "1334"}, {"ruleId": "1130", "severity": 1, "message": "1335", "line": 547, "column": 30, "nodeType": null, "messageId": "1132", "endLine": 547, "endColumn": 47}, {"ruleId": "1130", "severity": 1, "message": "1335", "line": 556, "column": 40, "nodeType": null, "messageId": "1132", "endLine": 556, "endColumn": 57}, {"ruleId": "1130", "severity": 1, "message": "1335", "line": 580, "column": 49, "nodeType": null, "messageId": "1132", "endLine": 580, "endColumn": 66}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 252, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 252, "endColumn": 33, "suggestions": "1336"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 279, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 279, "endColumn": 33, "suggestions": "1337"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 306, "column": 20, "nodeType": "1155", "messageId": "1156", "endLine": 306, "endColumn": 23, "suggestions": "1338"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 330, "column": 27, "nodeType": "1155", "messageId": "1156", "endLine": 330, "endColumn": 30, "suggestions": "1339"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 397, "column": 69, "nodeType": "1155", "messageId": "1156", "endLine": 397, "endColumn": 72, "suggestions": "1340"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 400, "column": 104, "nodeType": "1155", "messageId": "1156", "endLine": 400, "endColumn": 107, "suggestions": "1341"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 406, "column": 125, "nodeType": "1155", "messageId": "1156", "endLine": 406, "endColumn": 128, "suggestions": "1342"}, {"ruleId": "1130", "severity": 1, "message": "1328", "line": 8, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1329", "line": 8, "column": 28, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1330", "line": 8, "column": 39, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 52}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 16, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 16, "endColumn": 31, "suggestions": "1343"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 24, "column": 10, "nodeType": "1155", "messageId": "1156", "endLine": 24, "endColumn": 13, "suggestions": "1344"}, {"ruleId": "1130", "severity": 1, "message": "1345", "line": 59, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 59, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1346", "line": 67, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 67, "endColumn": 21}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 133, "column": 38, "nodeType": "1155", "messageId": "1156", "endLine": 133, "endColumn": 41, "suggestions": "1347"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 133, "column": 71, "nodeType": "1155", "messageId": "1156", "endLine": 133, "endColumn": 74, "suggestions": "1348"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 133, "column": 107, "nodeType": "1155", "messageId": "1156", "endLine": 133, "endColumn": 110, "suggestions": "1349"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 156, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 156, "endColumn": 39, "suggestions": "1350"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 255, "column": 58, "nodeType": "1155", "messageId": "1156", "endLine": 255, "endColumn": 61, "suggestions": "1351"}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 267, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 267, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 282, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 282, "endColumn": 17}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 290, "column": 26, "nodeType": "1155", "messageId": "1156", "endLine": 290, "endColumn": 29, "suggestions": "1352"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 291, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 291, "endColumn": 38, "suggestions": "1353"}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 303, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 303, "endColumn": 17}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 310, "column": 42, "nodeType": "1155", "messageId": "1156", "endLine": 310, "endColumn": 45, "suggestions": "1354"}, {"ruleId": "1130", "severity": 1, "message": "1305", "line": 323, "column": 16, "nodeType": null, "messageId": "1132", "endLine": 323, "endColumn": 17}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1355", "line": 309, "column": 25}, {"ruleId": "1130", "severity": 1, "message": "1356", "line": 8, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1357", "line": 61, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 61, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 22, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 25, "suggestions": "1358"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 64, "column": 20, "nodeType": "1155", "messageId": "1156", "endLine": 64, "endColumn": 23, "suggestions": "1359"}, {"ruleId": "1130", "severity": 1, "message": "1360", "line": 2, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 2, "endColumn": 11}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 81, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 81, "endColumn": 39, "suggestions": "1361"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 126, "column": 66, "nodeType": "1155", "messageId": "1156", "endLine": 126, "endColumn": 69, "suggestions": "1362"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 71, "column": 42, "nodeType": "1155", "messageId": "1156", "endLine": 71, "endColumn": 45, "suggestions": "1363"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 74, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 74, "endColumn": 36, "suggestions": "1364"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 95, "column": 44, "nodeType": "1155", "messageId": "1156", "endLine": 95, "endColumn": 47, "suggestions": "1365"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 116, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 116, "endColumn": 48, "suggestions": "1366"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 135, "column": 10, "nodeType": "1155", "messageId": "1156", "endLine": 135, "endColumn": 13, "suggestions": "1367"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 15, "column": 10, "nodeType": "1155", "messageId": "1156", "endLine": 15, "endColumn": 13, "suggestions": "1368"}, {"ruleId": "1130", "severity": 1, "message": "1369", "line": 8, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 19}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 65, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 65, "endColumn": 15, "suggestions": "1370"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 73, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 73, "endColumn": 15, "suggestions": "1371"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 91, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 91, "endColumn": 15, "suggestions": "1372"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 106, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 106, "endColumn": 15, "suggestions": "1373"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 118, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 118, "endColumn": 15, "suggestions": "1374"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 145, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 145, "endColumn": 15, "suggestions": "1375"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 161, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 161, "endColumn": 15, "suggestions": "1376"}, {"ruleId": "1130", "severity": 1, "message": "1329", "line": 6, "column": 28, "nodeType": null, "messageId": "1132", "endLine": 6, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1330", "line": 6, "column": 39, "nodeType": null, "messageId": "1132", "endLine": 6, "endColumn": 52}, {"ruleId": "1130", "severity": 1, "message": "1377", "line": 7, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 7, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 55, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 55, "endColumn": 33, "suggestions": "1378"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 138, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 138, "endColumn": 33, "suggestions": "1379"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 139, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 139, "endColumn": 17, "suggestions": "1380"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 209, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 209, "endColumn": 33, "suggestions": "1381"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 259, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 259, "endColumn": 33, "suggestions": "1382"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 260, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 260, "endColumn": 17, "suggestions": "1383"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 347, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 347, "endColumn": 33, "suggestions": "1384"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 348, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 348, "endColumn": 17, "suggestions": "1385"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 417, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 417, "endColumn": 33, "suggestions": "1386"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 418, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 418, "endColumn": 17, "suggestions": "1387"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 473, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 473, "endColumn": 33, "suggestions": "1388"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 474, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 474, "endColumn": 17, "suggestions": "1389"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 494, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 494, "endColumn": 33, "suggestions": "1390"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 495, "column": 14, "nodeType": "1155", "messageId": "1156", "endLine": 495, "endColumn": 17, "suggestions": "1391"}, {"ruleId": "1130", "severity": 1, "message": "1377", "line": 8, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 60, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 60, "endColumn": 33, "suggestions": "1392"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 20, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 23, "suggestions": "1393"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 135, "column": 32, "nodeType": "1155", "messageId": "1156", "endLine": 135, "endColumn": 35, "suggestions": "1394"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 272, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 272, "endColumn": 39, "suggestions": "1395"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 272, "column": 89, "nodeType": "1155", "messageId": "1156", "endLine": 272, "endColumn": 92, "suggestions": "1396"}, {"ruleId": "1130", "severity": 1, "message": "1397", "line": 299, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 299, "endColumn": 20}, {"ruleId": "1130", "severity": 1, "message": "1398", "line": 300, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 300, "endColumn": 19}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 358, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 358, "endColumn": 31, "suggestions": "1399"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 378, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 378, "endColumn": 33, "suggestions": "1400"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 397, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 397, "endColumn": 33, "suggestions": "1401"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 416, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 416, "endColumn": 33, "suggestions": "1402"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 434, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 434, "endColumn": 33, "suggestions": "1403"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 449, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 449, "endColumn": 48, "suggestions": "1404"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 55, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 55, "endColumn": 33, "suggestions": "1405"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 57, "column": 20, "nodeType": "1155", "messageId": "1156", "endLine": 57, "endColumn": 23, "suggestions": "1406"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 150, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 150, "endColumn": 39, "suggestions": "1407"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 150, "column": 89, "nodeType": "1155", "messageId": "1156", "endLine": 150, "endColumn": 92, "suggestions": "1408"}, {"ruleId": "1130", "severity": 1, "message": "1397", "line": 178, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 178, "endColumn": 20}, {"ruleId": "1130", "severity": 1, "message": "1398", "line": 179, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 179, "endColumn": 19}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 323, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 323, "endColumn": 31, "suggestions": "1409"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 344, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 344, "endColumn": 33, "suggestions": "1410"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 358, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 358, "endColumn": 33, "suggestions": "1411"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 372, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 372, "endColumn": 33, "suggestions": "1412"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 386, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 386, "endColumn": 33, "suggestions": "1413"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 401, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 401, "endColumn": 33, "suggestions": "1414"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 415, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 415, "endColumn": 33, "suggestions": "1415"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 429, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 429, "endColumn": 33, "suggestions": "1416"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 443, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 443, "endColumn": 33, "suggestions": "1417"}, {"ruleId": "1130", "severity": 1, "message": "1377", "line": 8, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 8, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 57, "column": 55, "nodeType": "1155", "messageId": "1156", "endLine": 57, "endColumn": 58, "suggestions": "1418"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 59, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 59, "endColumn": 33, "suggestions": "1419"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 61, "column": 20, "nodeType": "1155", "messageId": "1156", "endLine": 61, "endColumn": 23, "suggestions": "1420"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 132, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 132, "endColumn": 38, "suggestions": "1421"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 163, "column": 38, "nodeType": "1155", "messageId": "1156", "endLine": 163, "endColumn": 41, "suggestions": "1422"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 163, "column": 91, "nodeType": "1155", "messageId": "1156", "endLine": 163, "endColumn": 94, "suggestions": "1423"}, {"ruleId": "1130", "severity": 1, "message": "1397", "line": 181, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 181, "endColumn": 16}, {"ruleId": "1130", "severity": 1, "message": "1398", "line": 182, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 182, "endColumn": 15}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 200, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 200, "endColumn": 33, "suggestions": "1424"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 214, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 214, "endColumn": 39, "suggestions": "1425"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 228, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 228, "endColumn": 36, "suggestions": "1426"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 260, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 260, "endColumn": 56, "suggestions": "1427"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 262, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 262, "endColumn": 31, "suggestions": "1428"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 281, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 281, "endColumn": 33, "suggestions": "1429"}, {"ruleId": "1430", "severity": 2, "message": "1431", "line": 285, "column": 13, "nodeType": "1432", "messageId": "1433", "endLine": 285, "endColumn": 25, "fix": "1434"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 298, "column": 11, "nodeType": "1155", "messageId": "1156", "endLine": 298, "endColumn": 14, "suggestions": "1435"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 299, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 299, "endColumn": 33, "suggestions": "1436"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 313, "column": 11, "nodeType": "1155", "messageId": "1156", "endLine": 313, "endColumn": 14, "suggestions": "1437"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 314, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 314, "endColumn": 31, "suggestions": "1438"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 315, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 315, "endColumn": 33, "suggestions": "1439"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 335, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 335, "endColumn": 31, "suggestions": "1440"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 336, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 336, "endColumn": 33, "suggestions": "1441"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 356, "column": 11, "nodeType": "1155", "messageId": "1156", "endLine": 356, "endColumn": 14, "suggestions": "1442"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 358, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 358, "endColumn": 33, "suggestions": "1443"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 375, "column": 29, "nodeType": "1155", "messageId": "1156", "endLine": 375, "endColumn": 32, "suggestions": "1444"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 376, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 376, "endColumn": 33, "suggestions": "1445"}, {"ruleId": "1130", "severity": 1, "message": "1446", "line": 462, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 462, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1446", "line": 480, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 480, "endColumn": 25}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 71, "column": 16, "nodeType": "1155", "messageId": "1156", "endLine": 71, "endColumn": 19, "suggestions": "1447"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 96, "column": 11, "nodeType": "1155", "messageId": "1156", "endLine": 96, "endColumn": 14, "suggestions": "1448"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 110, "column": 51, "nodeType": "1155", "messageId": "1156", "endLine": 110, "endColumn": 54, "suggestions": "1449"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 111, "column": 29, "nodeType": "1155", "messageId": "1156", "endLine": 111, "endColumn": 32, "suggestions": "1450"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 125, "column": 27, "nodeType": "1155", "messageId": "1156", "endLine": 125, "endColumn": 30, "suggestions": "1451"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 163, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 163, "endColumn": 38, "suggestions": "1452"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 250, "column": 38, "nodeType": "1155", "messageId": "1156", "endLine": 250, "endColumn": 41, "suggestions": "1453"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 312, "column": 33, "nodeType": "1155", "messageId": "1156", "endLine": 312, "endColumn": 36, "suggestions": "1454"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 352, "column": 45, "nodeType": "1155", "messageId": "1156", "endLine": 352, "endColumn": 48, "suggestions": "1455"}, {"ruleId": "1130", "severity": 1, "message": "1456", "line": 241, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 241, "endColumn": 20}, {"ruleId": "1130", "severity": 1, "message": "1457", "line": 306, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 306, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 163, "column": 17, "nodeType": "1155", "messageId": "1156", "endLine": 163, "endColumn": 20, "suggestions": "1458"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 166, "column": 21, "nodeType": "1155", "messageId": "1156", "endLine": 166, "endColumn": 24, "suggestions": "1459"}, {"ruleId": "1430", "severity": 2, "message": "1460", "line": 311, "column": 13, "nodeType": "1432", "messageId": "1433", "endLine": 311, "endColumn": 30, "fix": "1461"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 311, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 311, "endColumn": 28, "suggestions": "1462"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 315, "column": 28, "nodeType": "1155", "messageId": "1156", "endLine": 315, "endColumn": 31, "suggestions": "1463"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 317, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 317, "endColumn": 40, "suggestions": "1464"}, {"ruleId": "1130", "severity": 1, "message": "1465", "line": 495, "column": 26, "nodeType": null, "messageId": "1132", "endLine": 495, "endColumn": 34}, {"ruleId": "1130", "severity": 1, "message": "1465", "line": 505, "column": 26, "nodeType": null, "messageId": "1132", "endLine": 505, "endColumn": 34}, {"ruleId": "1130", "severity": 1, "message": "1465", "line": 513, "column": 26, "nodeType": null, "messageId": "1132", "endLine": 513, "endColumn": 34}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 271, "column": 25, "nodeType": "1155", "messageId": "1156", "endLine": 271, "endColumn": 28, "suggestions": "1466"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 273, "column": 21, "nodeType": "1155", "messageId": "1156", "endLine": 273, "endColumn": 24, "suggestions": "1467"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 278, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 278, "endColumn": 40, "suggestions": "1468"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 325, "column": 46, "nodeType": "1155", "messageId": "1156", "endLine": 325, "endColumn": 49, "suggestions": "1469"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 348, "column": 12, "nodeType": "1155", "messageId": "1156", "endLine": 348, "endColumn": 15, "suggestions": "1470"}, {"ruleId": "1130", "severity": 1, "message": "1471", "line": 10, "column": 7, "nodeType": null, "messageId": "1132", "endLine": 10, "endColumn": 18}, {"ruleId": "1130", "severity": 1, "message": "1472", "line": 2, "column": 10, "nodeType": null, "messageId": "1132", "endLine": 2, "endColumn": 22}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 56, "column": 50, "nodeType": "1155", "messageId": "1156", "endLine": 56, "endColumn": 53, "suggestions": "1473"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 57, "column": 55, "nodeType": "1155", "messageId": "1156", "endLine": 57, "endColumn": 58, "suggestions": "1474"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 61, "column": 49, "nodeType": "1155", "messageId": "1156", "endLine": 61, "endColumn": 52, "suggestions": "1475"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 62, "column": 48, "nodeType": "1155", "messageId": "1156", "endLine": 62, "endColumn": 51, "suggestions": "1476"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 63, "column": 48, "nodeType": "1155", "messageId": "1156", "endLine": 63, "endColumn": 51, "suggestions": "1477"}, {"ruleId": "1130", "severity": 1, "message": "1478", "line": 88, "column": 13, "nodeType": null, "messageId": "1132", "endLine": 88, "endColumn": 30}, {"ruleId": "1479", "severity": 2, "message": "1480", "line": 88, "column": 33, "nodeType": "1481", "messageId": "1482", "endLine": 88, "endColumn": 64}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 186, "column": 51, "nodeType": "1155", "messageId": "1156", "endLine": 186, "endColumn": 54, "suggestions": "1483"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 187, "column": 50, "nodeType": "1155", "messageId": "1156", "endLine": 187, "endColumn": 53, "suggestions": "1484"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 236, "column": 49, "nodeType": "1155", "messageId": "1156", "endLine": 236, "endColumn": 52, "suggestions": "1485"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 237, "column": 48, "nodeType": "1155", "messageId": "1156", "endLine": 237, "endColumn": 51, "suggestions": "1486"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 280, "column": 54, "nodeType": "1155", "messageId": "1156", "endLine": 280, "endColumn": 57, "suggestions": "1487"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 281, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 281, "endColumn": 56, "suggestions": "1488"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 318, "column": 54, "nodeType": "1155", "messageId": "1156", "endLine": 318, "endColumn": 57, "suggestions": "1489"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 319, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 319, "endColumn": 56, "suggestions": "1490"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 356, "column": 57, "nodeType": "1155", "messageId": "1156", "endLine": 356, "endColumn": 60, "suggestions": "1491"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 357, "column": 56, "nodeType": "1155", "messageId": "1156", "endLine": 357, "endColumn": 59, "suggestions": "1492"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 382, "column": 57, "nodeType": "1155", "messageId": "1156", "endLine": 382, "endColumn": 60, "suggestions": "1493"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 383, "column": 56, "nodeType": "1155", "messageId": "1156", "endLine": 383, "endColumn": 59, "suggestions": "1494"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 437, "column": 52, "nodeType": "1155", "messageId": "1156", "endLine": 437, "endColumn": 55, "suggestions": "1495"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 438, "column": 57, "nodeType": "1155", "messageId": "1156", "endLine": 438, "endColumn": 60, "suggestions": "1496"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 439, "column": 56, "nodeType": "1155", "messageId": "1156", "endLine": 439, "endColumn": 59, "suggestions": "1497"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 471, "column": 75, "nodeType": "1155", "messageId": "1156", "endLine": 471, "endColumn": 78, "suggestions": "1498"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 483, "column": 51, "nodeType": "1155", "messageId": "1156", "endLine": 483, "endColumn": 54, "suggestions": "1499"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 484, "column": 56, "nodeType": "1155", "messageId": "1156", "endLine": 484, "endColumn": 59, "suggestions": "1500"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 485, "column": 55, "nodeType": "1155", "messageId": "1156", "endLine": 485, "endColumn": 58, "suggestions": "1501"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 516, "column": 50, "nodeType": "1155", "messageId": "1156", "endLine": 516, "endColumn": 53, "suggestions": "1502"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 517, "column": 49, "nodeType": "1155", "messageId": "1156", "endLine": 517, "endColumn": 52, "suggestions": "1503"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 625, "column": 18, "nodeType": "1155", "messageId": "1156", "endLine": 625, "endColumn": 21, "suggestions": "1504"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 634, "column": 19, "nodeType": "1155", "messageId": "1156", "endLine": 634, "endColumn": 22, "suggestions": "1505"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 701, "column": 23, "nodeType": "1155", "messageId": "1156", "endLine": 701, "endColumn": 26, "suggestions": "1506"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 435, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 435, "endColumn": 38, "suggestions": "1507"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 629, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 629, "endColumn": 40, "suggestions": "1508"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 876, "column": 30, "nodeType": "1155", "messageId": "1156", "endLine": 876, "endColumn": 33, "suggestions": "1509"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 1022, "column": 31, "nodeType": "1155", "messageId": "1156", "endLine": 1022, "endColumn": 34, "suggestions": "1510"}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 75, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 75, "endColumn": 23}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 86, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 86, "endColumn": 37, "suggestions": "1511"}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 166, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 166, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 188, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 188, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 232, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 232, "endColumn": 23}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 351, "column": 35, "nodeType": "1155", "messageId": "1156", "endLine": 351, "endColumn": 38, "suggestions": "1512"}, {"ruleId": "1130", "severity": 1, "message": "1513", "line": 357, "column": 60, "nodeType": null, "messageId": "1132", "endLine": 357, "endColumn": 68}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 381, "column": 37, "nodeType": "1155", "messageId": "1156", "endLine": 381, "endColumn": 40, "suggestions": "1514"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 413, "column": 34, "nodeType": "1155", "messageId": "1156", "endLine": 413, "endColumn": 37, "suggestions": "1515"}, {"ruleId": "1130", "severity": 1, "message": "1169", "line": 465, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 465, "endColumn": 23}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 70, "column": 13, "nodeType": "1155", "messageId": "1156", "endLine": 70, "endColumn": 16, "suggestions": "1516"}, {"ruleId": "1130", "severity": 1, "message": "1517", "line": 9, "column": 18, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1518", "line": 9, "column": 32, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 46}, {"ruleId": "1130", "severity": 1, "message": "1519", "line": 9, "column": 48, "nodeType": null, "messageId": "1132", "endLine": 9, "endColumn": 60}, {"ruleId": "1130", "severity": 1, "message": "1520", "line": 332, "column": 76, "nodeType": null, "messageId": "1132", "endLine": 332, "endColumn": 79}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 51, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 51, "endColumn": 39, "suggestions": "1521"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 52, "column": 41, "nodeType": "1155", "messageId": "1156", "endLine": 52, "endColumn": 44, "suggestions": "1522"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 214, "column": 54, "nodeType": "1155", "messageId": "1156", "endLine": 214, "endColumn": 57, "suggestions": "1523"}, {"ruleId": "1479", "severity": 2, "message": "1480", "line": 249, "column": 18, "nodeType": "1481", "messageId": "1482", "endLine": 249, "endColumn": 50}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 252, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 252, "endColumn": 56, "suggestions": "1524"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 261, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 261, "endColumn": 56, "suggestions": "1525"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 272, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 272, "endColumn": 56, "suggestions": "1526"}, {"ruleId": "1479", "severity": 2, "message": "1480", "line": 284, "column": 18, "nodeType": "1481", "messageId": "1482", "endLine": 284, "endColumn": 50}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 287, "column": 53, "nodeType": "1155", "messageId": "1156", "endLine": 287, "endColumn": 56, "suggestions": "1527"}, {"ruleId": "1528", "severity": 2, "message": "1529", "line": 9, "column": 3, "nodeType": "1530", "messageId": "1531", "endLine": 22, "endColumn": 4}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 77, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 77, "endColumn": 39, "suggestions": "1532"}, {"ruleId": "1130", "severity": 1, "message": "1533", "line": 79, "column": 11, "nodeType": null, "messageId": "1132", "endLine": 79, "endColumn": 25}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 219, "column": 36, "nodeType": "1155", "messageId": "1156", "endLine": 219, "endColumn": 39, "suggestions": "1534"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 283, "column": 41, "nodeType": "1155", "messageId": "1156", "endLine": 283, "endColumn": 44, "suggestions": "1535"}, {"ruleId": "1153", "severity": 2, "message": "1154", "line": 314, "column": 56, "nodeType": "1155", "messageId": "1156", "endLine": 314, "endColumn": 59, "suggestions": "1536"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "1537", "line": 171, "column": 16}, "@typescript-eslint/no-unused-vars", "'useRouter' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1538", "1539", "1540", "1541"], ["1542", "1543", "1544", "1545"], ["1546", "1547", "1548", "1549"], ["1550", "1551", "1552", "1553"], "'CreditCard' is defined but never used.", ["1554", "1555", "1556", "1557"], "'Separator' is defined but never used.", "'Mail' is defined but never used.", "'AuthError' is defined but never used.", "'Session' is defined but never used.", "'get' is defined but never used.", "'healthMonitor' is defined but never used.", "'queryTracker' is defined but never used.", "'maintenanceUtils' is defined but never used.", "'AuditSeverity' is defined but never used.", "'realtimeManager' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1558", "1559"], "'data' is assigned a value but never used.", ["1560", "1561"], ["1562", "1563"], ["1564", "1565"], ["1566", "1567"], ["1568", "1569"], ["1570", "1571"], ["1572", "1573"], ["1574", "1575"], ["1576", "1577"], ["1578", "1579"], "'error' is defined but never used.", "'sql' is defined but never used.", "'indexes' is assigned a value but never used.", "'triggerFunction' is assigned a value but never used.", "'triggers' is assigned a value but never used.", "'maintenanceQueries' is defined but never used.", "'Database' is defined but never used.", ["1580", "1581"], ["1582", "1583"], ["1584", "1585"], ["1586", "1587"], ["1588", "1589"], ["1590", "1591"], ["1592", "1593"], ["1594", "1595"], ["1596", "1597"], ["1598", "1599"], ["1600", "1601"], ["1602", "1603"], ["1604", "1605"], ["1606", "1607"], "'fromDate' is defined but never used.", "'toDate' is defined but never used.", ["1608", "1609"], ["1610", "1611"], ["1612", "1613"], ["1614", "1615"], ["1616", "1617"], "'User' is defined but never used.", "'RealtimeEvent' is defined but never used.", "'activeSubscriptions' is assigned a value but never used.", "'subscriptionCleanups' is assigned a value but never used.", ["1618", "1619"], ["1620", "1621"], ["1622", "1623"], ["1624", "1625"], ["1626", "1627"], ["1628", "1629"], ["1630", "1631"], ["1632", "1633"], ["1634", "1635"], ["1636", "1637"], ["1638", "1639"], ["1640", "1641"], ["1642", "1643"], ["1644", "1645"], ["1646", "1647"], ["1648", "1649"], ["1650", "1651"], ["1652", "1653"], ["1654", "1655"], ["1656", "1657"], ["1658", "1659"], ["1660", "1661"], ["1662", "1663"], ["1664", "1665"], ["1666", "1667"], ["1668", "1669"], ["1670", "1671"], ["1672", "1673"], ["1674", "1675"], ["1676", "1677"], ["1678", "1679"], ["1680", "1681"], ["1682", "1683"], ["1684", "1685"], ["1686", "1687"], ["1688", "1689"], ["1690", "1691"], ["1692", "1693"], "Parsing error: Invalid character.", "'user' is assigned a value but never used.", "'activeSection' is assigned a value but never used.", "'ref' is defined but never used.", "'ChevronDown' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'updatePosition'. Either include it or remove the dependency array.", "ArrayExpression", ["1694"], ["1695"], "'FormMessage' is defined but never used.", "'Settings' is defined but never used.", "'cn' is defined but never used.", "'Eye' is defined but never used.", "'VolumeX' is defined but never used.", "'MousePointer' is defined but never used.", "'Monitor' is defined but never used.", "'className' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'MoreHorizontal' is defined but never used.", "'setIsVisible' is assigned a value but never used.", ["1696", "1697"], ["1698", "1699"], ["1700", "1701"], ["1702", "1703"], ["1704", "1705"], ["1706", "1707"], ["1708", "1709"], "'TrendingUp' is defined but never used.", "'Filter' is defined but never used.", ["1710", "1711"], ["1712", "1713"], ["1714", "1715"], "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["1716"], ["1717", "1718"], ["1719", "1720"], ["1721", "1722"], ["1723", "1724"], ["1725", "1726"], "'error' is assigned a value but never used.", ["1727", "1728", "1729", "1730"], ["1731", "1732"], ["1733", "1734"], ["1735", "1736"], ["1737", "1738"], ["1739", "1740"], ["1741", "1742"], ["1743", "1744"], ["1745", "1746"], ["1747", "1748"], ["1749", "1750"], ["1751", "1752"], ["1753", "1754"], ["1755", "1756"], ["1757", "1758"], "'now' is assigned a value but never used.", ["1759", "1760"], ["1761", "1762"], "'lineHeight' is assigned a value but never used.", "'name' is assigned a value but never used.", ["1763", "1764"], ["1765", "1766"], ["1767", "1768"], ["1769", "1770"], "'e' is defined but never used.", ["1771", "1772"], ["1773", "1774"], ["1775", "1776"], ["1777", "1778"], ["1779", "1780"], ["1781", "1782"], ["1783", "1784"], ["1785", "1786"], ["1787", "1788"], ["1789", "1790"], ["1791", "1792"], ["1793", "1794"], ["1795", "1796"], ["1797", "1798"], "'payload' is assigned a value but never used.", ["1799", "1800"], ["1801", "1802"], ["1803", "1804"], ["1805", "1806"], ["1807", "1808"], ["1809", "1810"], ["1811", "1812"], "'ApplicationError' is defined but never used.", "'ErrorType' is defined but never used.", "'ErrorSeverity' is defined but never used.", ["1813", "1814"], ["1815", "1816"], ["1817", "1818"], ["1819", "1820"], "'timeWindowMinutes' is defined but never used.", ["1821", "1822"], ["1823", "1824"], ["1825", "1826"], ["1827", "1828"], ["1829", "1830"], ["1831", "1832"], ["1833", "1834"], ["1835", "1836"], ["1837", "1838"], "'expectedStatus' is assigned a value but never used.", "'response' is assigned a value but never used.", ["1839", "1840"], ["1841", "1842"], ["1843", "1844"], ["1845", "1846"], ["1847", "1848"], ["1849", "1850"], ["1851", "1852"], ["1853", "1854"], "Parsing error: ';' expected.", "'SecurityError' is defined but never used.", "'windowStart' is assigned a value but never used.", ["1855", "1856"], ["1857", "1858"], "'z' is defined but never used.", ["1859", "1860"], ["1861", "1862"], ["1863", "1864"], ["1865", "1866"], ["1867", "1868"], ["1869", "1870"], ["1871", "1872"], ["1873", "1874"], "'serperClient' is assigned a value but never used.", ["1875", "1876"], ["1877", "1878"], ["1879", "1880"], ["1881", "1882"], ["1883", "1884"], ["1885", "1886"], ["1887", "1888"], "'errorHandler' is defined but never used.", ["1889", "1890"], ["1891", "1892"], ["1893", "1894"], ["1895", "1896"], ["1897", "1898"], ["1899", "1900"], ["1901", "1902"], ["1903", "1904"], ["1905", "1906"], ["1907", "1908"], ["1909", "1910"], ["1911", "1912"], ["1913", "1914"], ["1915", "1916"], ["1917", "1918"], ["1919", "1920"], ["1921", "1922"], ["1923", "1924"], ["1925", "1926"], "'errorType' is assigned a value but never used.", "'severity' is assigned a value but never used.", ["1927", "1928"], ["1929", "1930"], ["1931", "1932"], ["1933", "1934"], ["1935", "1936"], ["1937", "1938"], ["1939", "1940"], ["1941", "1942"], ["1943", "1944"], ["1945", "1946"], ["1947", "1948"], ["1949", "1950"], ["1951", "1952"], ["1953", "1954"], ["1955", "1956"], ["1957", "1958"], ["1959", "1960"], ["1961", "1962"], ["1963", "1964"], ["1965", "1966"], ["1967", "1968"], ["1969", "1970"], ["1971", "1972"], ["1973", "1974"], ["1975", "1976"], ["1977", "1978"], ["1979", "1980"], ["1981", "1982"], ["1983", "1984"], ["1985", "1986"], ["1987", "1988"], "prefer-const", "'queryBuilder' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1989", "text": "1990"}, ["1991", "1992"], ["1993", "1994"], ["1995", "1996"], ["1997", "1998"], ["1999", "2000"], ["2001", "2002"], ["2003", "2004"], ["2005", "2006"], ["2007", "2008"], ["2009", "2010"], ["2011", "2012"], "'subscription' is assigned a value but never used.", ["2013", "2014"], ["2015", "2016"], ["2017", "2018"], ["2019", "2020"], ["2021", "2022"], ["2023", "2024"], ["2025", "2026"], ["2027", "2028"], ["2029", "2030"], "'startTime' is assigned a value but never used.", "'totalIssues' is assigned a value but never used.", ["2031", "2032"], ["2033", "2034"], "'clsEntries' is never reassigned. Use 'const' instead.", {"range": "2035", "text": "2036"}, ["2037", "2038"], ["2039", "2040"], ["2041", "2042"], "'viewport' is defined but never used.", ["2043", "2044"], ["2045", "2046"], ["2047", "2048"], ["2049", "2050"], ["2051", "2052"], "'PHONE_REGEX' is assigned a value but never used.", "'testFixtures' is defined but never used.", ["2053", "2054"], ["2055", "2056"], ["2057", "2058"], ["2059", "2060"], ["2061", "2062"], "'originalDOMPurify' is assigned a value but never used.", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["2063", "2064"], ["2065", "2066"], ["2067", "2068"], ["2069", "2070"], ["2071", "2072"], ["2073", "2074"], ["2075", "2076"], ["2077", "2078"], ["2079", "2080"], ["2081", "2082"], ["2083", "2084"], ["2085", "2086"], ["2087", "2088"], ["2089", "2090"], ["2091", "2092"], ["2093", "2094"], ["2095", "2096"], ["2097", "2098"], ["2099", "2100"], ["2101", "2102"], ["2103", "2104"], ["2105", "2106"], ["2107", "2108"], ["2109", "2110"], ["2111", "2112"], ["2113", "2114"], ["2115", "2116"], ["2117", "2118"], ["2119", "2120"], ["2121", "2122"], "'callback' is defined but never used.", ["2123", "2124"], ["2125", "2126"], ["2127", "2128"], "'mockApiError' is defined but never used.", "'mockApiSuccess' is defined but never used.", "'mockApiDelay' is defined but never used.", "'ctx' is defined but never used.", ["2129", "2130"], ["2131", "2132"], ["2133", "2134"], ["2135", "2136"], ["2137", "2138"], ["2139", "2140"], ["2141", "2142"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["2143", "2144"], "'optionalFields' is assigned a value but never used.", ["2145", "2146"], ["2147", "2148"], ["2149", "2150"], "Parsing error: '>' expected.", {"messageId": "2151", "data": "2152", "fix": "2153", "desc": "2154"}, {"messageId": "2151", "data": "2155", "fix": "2156", "desc": "2157"}, {"messageId": "2151", "data": "2158", "fix": "2159", "desc": "2160"}, {"messageId": "2151", "data": "2161", "fix": "2162", "desc": "2163"}, {"messageId": "2151", "data": "2164", "fix": "2165", "desc": "2154"}, {"messageId": "2151", "data": "2166", "fix": "2167", "desc": "2157"}, {"messageId": "2151", "data": "2168", "fix": "2169", "desc": "2160"}, {"messageId": "2151", "data": "2170", "fix": "2171", "desc": "2163"}, {"messageId": "2151", "data": "2172", "fix": "2173", "desc": "2154"}, {"messageId": "2151", "data": "2174", "fix": "2175", "desc": "2157"}, {"messageId": "2151", "data": "2176", "fix": "2177", "desc": "2160"}, {"messageId": "2151", "data": "2178", "fix": "2179", "desc": "2163"}, {"messageId": "2151", "data": "2180", "fix": "2181", "desc": "2154"}, {"messageId": "2151", "data": "2182", "fix": "2183", "desc": "2157"}, {"messageId": "2151", "data": "2184", "fix": "2185", "desc": "2160"}, {"messageId": "2151", "data": "2186", "fix": "2187", "desc": "2163"}, {"messageId": "2151", "data": "2188", "fix": "2189", "desc": "2154"}, {"messageId": "2151", "data": "2190", "fix": "2191", "desc": "2157"}, {"messageId": "2151", "data": "2192", "fix": "2193", "desc": "2160"}, {"messageId": "2151", "data": "2194", "fix": "2195", "desc": "2163"}, {"messageId": "2196", "fix": "2197", "desc": "2198"}, {"messageId": "2199", "fix": "2200", "desc": "2201"}, {"messageId": "2196", "fix": "2202", "desc": "2198"}, {"messageId": "2199", "fix": "2203", "desc": "2201"}, {"messageId": "2196", "fix": "2204", "desc": "2198"}, {"messageId": "2199", "fix": "2205", "desc": "2201"}, {"messageId": "2196", "fix": "2206", "desc": "2198"}, {"messageId": "2199", "fix": "2207", "desc": "2201"}, {"messageId": "2196", "fix": "2208", "desc": "2198"}, {"messageId": "2199", "fix": "2209", "desc": "2201"}, {"messageId": "2196", "fix": "2210", "desc": "2198"}, {"messageId": "2199", "fix": "2211", "desc": "2201"}, {"messageId": "2196", "fix": "2212", "desc": "2198"}, {"messageId": "2199", "fix": "2213", "desc": "2201"}, {"messageId": "2196", "fix": "2214", "desc": "2198"}, {"messageId": "2199", "fix": "2215", "desc": "2201"}, {"messageId": "2196", "fix": "2216", "desc": "2198"}, {"messageId": "2199", "fix": "2217", "desc": "2201"}, {"messageId": "2196", "fix": "2218", "desc": "2198"}, {"messageId": "2199", "fix": "2219", "desc": "2201"}, {"messageId": "2196", "fix": "2220", "desc": "2198"}, {"messageId": "2199", "fix": "2221", "desc": "2201"}, {"messageId": "2196", "fix": "2222", "desc": "2198"}, {"messageId": "2199", "fix": "2223", "desc": "2201"}, {"messageId": "2196", "fix": "2224", "desc": "2198"}, {"messageId": "2199", "fix": "2225", "desc": "2201"}, {"messageId": "2196", "fix": "2226", "desc": "2198"}, {"messageId": "2199", "fix": "2227", "desc": "2201"}, {"messageId": "2196", "fix": "2228", "desc": "2198"}, {"messageId": "2199", "fix": "2229", "desc": "2201"}, {"messageId": "2196", "fix": "2230", "desc": "2198"}, {"messageId": "2199", "fix": "2231", "desc": "2201"}, {"messageId": "2196", "fix": "2232", "desc": "2198"}, {"messageId": "2199", "fix": "2233", "desc": "2201"}, {"messageId": "2196", "fix": "2234", "desc": "2198"}, {"messageId": "2199", "fix": "2235", "desc": "2201"}, {"messageId": "2196", "fix": "2236", "desc": "2198"}, {"messageId": "2199", "fix": "2237", "desc": "2201"}, {"messageId": "2196", "fix": "2238", "desc": "2198"}, {"messageId": "2199", "fix": "2239", "desc": "2201"}, {"messageId": "2196", "fix": "2240", "desc": "2198"}, {"messageId": "2199", "fix": "2241", "desc": "2201"}, {"messageId": "2196", "fix": "2242", "desc": "2198"}, {"messageId": "2199", "fix": "2243", "desc": "2201"}, {"messageId": "2196", "fix": "2244", "desc": "2198"}, {"messageId": "2199", "fix": "2245", "desc": "2201"}, {"messageId": "2196", "fix": "2246", "desc": "2198"}, {"messageId": "2199", "fix": "2247", "desc": "2201"}, {"messageId": "2196", "fix": "2248", "desc": "2198"}, {"messageId": "2199", "fix": "2249", "desc": "2201"}, {"messageId": "2196", "fix": "2250", "desc": "2198"}, {"messageId": "2199", "fix": "2251", "desc": "2201"}, {"messageId": "2196", "fix": "2252", "desc": "2198"}, {"messageId": "2199", "fix": "2253", "desc": "2201"}, {"messageId": "2196", "fix": "2254", "desc": "2198"}, {"messageId": "2199", "fix": "2255", "desc": "2201"}, {"messageId": "2196", "fix": "2256", "desc": "2198"}, {"messageId": "2199", "fix": "2257", "desc": "2201"}, {"messageId": "2196", "fix": "2258", "desc": "2198"}, {"messageId": "2199", "fix": "2259", "desc": "2201"}, {"messageId": "2196", "fix": "2260", "desc": "2198"}, {"messageId": "2199", "fix": "2261", "desc": "2201"}, {"messageId": "2196", "fix": "2262", "desc": "2198"}, {"messageId": "2199", "fix": "2263", "desc": "2201"}, {"messageId": "2196", "fix": "2264", "desc": "2198"}, {"messageId": "2199", "fix": "2265", "desc": "2201"}, {"messageId": "2196", "fix": "2266", "desc": "2198"}, {"messageId": "2199", "fix": "2267", "desc": "2201"}, {"messageId": "2196", "fix": "2268", "desc": "2198"}, {"messageId": "2199", "fix": "2269", "desc": "2201"}, {"messageId": "2196", "fix": "2270", "desc": "2198"}, {"messageId": "2199", "fix": "2271", "desc": "2201"}, {"messageId": "2196", "fix": "2272", "desc": "2198"}, {"messageId": "2199", "fix": "2273", "desc": "2201"}, {"messageId": "2196", "fix": "2274", "desc": "2198"}, {"messageId": "2199", "fix": "2275", "desc": "2201"}, {"messageId": "2196", "fix": "2276", "desc": "2198"}, {"messageId": "2199", "fix": "2277", "desc": "2201"}, {"messageId": "2196", "fix": "2278", "desc": "2198"}, {"messageId": "2199", "fix": "2279", "desc": "2201"}, {"messageId": "2196", "fix": "2280", "desc": "2198"}, {"messageId": "2199", "fix": "2281", "desc": "2201"}, {"messageId": "2196", "fix": "2282", "desc": "2198"}, {"messageId": "2199", "fix": "2283", "desc": "2201"}, {"messageId": "2196", "fix": "2284", "desc": "2198"}, {"messageId": "2199", "fix": "2285", "desc": "2201"}, {"messageId": "2196", "fix": "2286", "desc": "2198"}, {"messageId": "2199", "fix": "2287", "desc": "2201"}, {"messageId": "2196", "fix": "2288", "desc": "2198"}, {"messageId": "2199", "fix": "2289", "desc": "2201"}, {"messageId": "2196", "fix": "2290", "desc": "2198"}, {"messageId": "2199", "fix": "2291", "desc": "2201"}, {"messageId": "2196", "fix": "2292", "desc": "2198"}, {"messageId": "2199", "fix": "2293", "desc": "2201"}, {"messageId": "2196", "fix": "2294", "desc": "2198"}, {"messageId": "2199", "fix": "2295", "desc": "2201"}, {"messageId": "2196", "fix": "2296", "desc": "2198"}, {"messageId": "2199", "fix": "2297", "desc": "2201"}, {"messageId": "2196", "fix": "2298", "desc": "2198"}, {"messageId": "2199", "fix": "2299", "desc": "2201"}, {"messageId": "2196", "fix": "2300", "desc": "2198"}, {"messageId": "2199", "fix": "2301", "desc": "2201"}, {"messageId": "2196", "fix": "2302", "desc": "2198"}, {"messageId": "2199", "fix": "2303", "desc": "2201"}, {"messageId": "2196", "fix": "2304", "desc": "2198"}, {"messageId": "2199", "fix": "2305", "desc": "2201"}, {"messageId": "2196", "fix": "2306", "desc": "2198"}, {"messageId": "2199", "fix": "2307", "desc": "2201"}, {"messageId": "2196", "fix": "2308", "desc": "2198"}, {"messageId": "2199", "fix": "2309", "desc": "2201"}, {"messageId": "2196", "fix": "2310", "desc": "2198"}, {"messageId": "2199", "fix": "2311", "desc": "2201"}, {"messageId": "2196", "fix": "2312", "desc": "2198"}, {"messageId": "2199", "fix": "2313", "desc": "2201"}, {"messageId": "2196", "fix": "2314", "desc": "2198"}, {"messageId": "2199", "fix": "2315", "desc": "2201"}, {"messageId": "2196", "fix": "2316", "desc": "2198"}, {"messageId": "2199", "fix": "2317", "desc": "2201"}, {"messageId": "2196", "fix": "2318", "desc": "2198"}, {"messageId": "2199", "fix": "2319", "desc": "2201"}, {"messageId": "2196", "fix": "2320", "desc": "2198"}, {"messageId": "2199", "fix": "2321", "desc": "2201"}, {"messageId": "2196", "fix": "2322", "desc": "2198"}, {"messageId": "2199", "fix": "2323", "desc": "2201"}, {"messageId": "2196", "fix": "2324", "desc": "2198"}, {"messageId": "2199", "fix": "2325", "desc": "2201"}, {"messageId": "2196", "fix": "2326", "desc": "2198"}, {"messageId": "2199", "fix": "2327", "desc": "2201"}, {"messageId": "2196", "fix": "2328", "desc": "2198"}, {"messageId": "2199", "fix": "2329", "desc": "2201"}, {"messageId": "2196", "fix": "2330", "desc": "2198"}, {"messageId": "2199", "fix": "2331", "desc": "2201"}, {"messageId": "2196", "fix": "2332", "desc": "2198"}, {"messageId": "2199", "fix": "2333", "desc": "2201"}, {"messageId": "2196", "fix": "2334", "desc": "2198"}, {"messageId": "2199", "fix": "2335", "desc": "2201"}, {"desc": "2336", "fix": "2337"}, {"desc": "2338", "fix": "2339"}, {"messageId": "2196", "fix": "2340", "desc": "2198"}, {"messageId": "2199", "fix": "2341", "desc": "2201"}, {"messageId": "2196", "fix": "2342", "desc": "2198"}, {"messageId": "2199", "fix": "2343", "desc": "2201"}, {"messageId": "2196", "fix": "2344", "desc": "2198"}, {"messageId": "2199", "fix": "2345", "desc": "2201"}, {"messageId": "2196", "fix": "2346", "desc": "2198"}, {"messageId": "2199", "fix": "2347", "desc": "2201"}, {"messageId": "2196", "fix": "2348", "desc": "2198"}, {"messageId": "2199", "fix": "2349", "desc": "2201"}, {"messageId": "2196", "fix": "2350", "desc": "2198"}, {"messageId": "2199", "fix": "2351", "desc": "2201"}, {"messageId": "2196", "fix": "2352", "desc": "2198"}, {"messageId": "2199", "fix": "2353", "desc": "2201"}, {"messageId": "2196", "fix": "2354", "desc": "2198"}, {"messageId": "2199", "fix": "2355", "desc": "2201"}, {"messageId": "2196", "fix": "2356", "desc": "2198"}, {"messageId": "2199", "fix": "2357", "desc": "2201"}, {"messageId": "2196", "fix": "2358", "desc": "2198"}, {"messageId": "2199", "fix": "2359", "desc": "2201"}, {"desc": "2360", "fix": "2361"}, {"messageId": "2196", "fix": "2362", "desc": "2198"}, {"messageId": "2199", "fix": "2363", "desc": "2201"}, {"messageId": "2196", "fix": "2364", "desc": "2198"}, {"messageId": "2199", "fix": "2365", "desc": "2201"}, {"messageId": "2196", "fix": "2366", "desc": "2198"}, {"messageId": "2199", "fix": "2367", "desc": "2201"}, {"messageId": "2196", "fix": "2368", "desc": "2198"}, {"messageId": "2199", "fix": "2369", "desc": "2201"}, {"messageId": "2196", "fix": "2370", "desc": "2198"}, {"messageId": "2199", "fix": "2371", "desc": "2201"}, {"messageId": "2151", "data": "2372", "fix": "2373", "desc": "2154"}, {"messageId": "2151", "data": "2374", "fix": "2375", "desc": "2157"}, {"messageId": "2151", "data": "2376", "fix": "2377", "desc": "2160"}, {"messageId": "2151", "data": "2378", "fix": "2379", "desc": "2163"}, {"messageId": "2196", "fix": "2380", "desc": "2198"}, {"messageId": "2199", "fix": "2381", "desc": "2201"}, {"messageId": "2196", "fix": "2382", "desc": "2198"}, {"messageId": "2199", "fix": "2383", "desc": "2201"}, {"messageId": "2196", "fix": "2384", "desc": "2198"}, {"messageId": "2199", "fix": "2385", "desc": "2201"}, {"messageId": "2196", "fix": "2386", "desc": "2198"}, {"messageId": "2199", "fix": "2387", "desc": "2201"}, {"messageId": "2196", "fix": "2388", "desc": "2198"}, {"messageId": "2199", "fix": "2389", "desc": "2201"}, {"messageId": "2196", "fix": "2390", "desc": "2198"}, {"messageId": "2199", "fix": "2391", "desc": "2201"}, {"messageId": "2196", "fix": "2392", "desc": "2198"}, {"messageId": "2199", "fix": "2393", "desc": "2201"}, {"messageId": "2196", "fix": "2394", "desc": "2198"}, {"messageId": "2199", "fix": "2395", "desc": "2201"}, {"messageId": "2196", "fix": "2396", "desc": "2198"}, {"messageId": "2199", "fix": "2397", "desc": "2201"}, {"messageId": "2196", "fix": "2398", "desc": "2198"}, {"messageId": "2199", "fix": "2399", "desc": "2201"}, {"messageId": "2196", "fix": "2400", "desc": "2198"}, {"messageId": "2199", "fix": "2401", "desc": "2201"}, {"messageId": "2196", "fix": "2402", "desc": "2198"}, {"messageId": "2199", "fix": "2403", "desc": "2201"}, {"messageId": "2196", "fix": "2404", "desc": "2198"}, {"messageId": "2199", "fix": "2405", "desc": "2201"}, {"messageId": "2196", "fix": "2406", "desc": "2198"}, {"messageId": "2199", "fix": "2407", "desc": "2201"}, {"messageId": "2196", "fix": "2408", "desc": "2198"}, {"messageId": "2199", "fix": "2409", "desc": "2201"}, {"messageId": "2196", "fix": "2410", "desc": "2198"}, {"messageId": "2199", "fix": "2411", "desc": "2201"}, {"messageId": "2196", "fix": "2412", "desc": "2198"}, {"messageId": "2199", "fix": "2413", "desc": "2201"}, {"messageId": "2196", "fix": "2414", "desc": "2198"}, {"messageId": "2199", "fix": "2415", "desc": "2201"}, {"messageId": "2196", "fix": "2416", "desc": "2198"}, {"messageId": "2199", "fix": "2417", "desc": "2201"}, {"messageId": "2196", "fix": "2418", "desc": "2198"}, {"messageId": "2199", "fix": "2419", "desc": "2201"}, {"messageId": "2196", "fix": "2420", "desc": "2198"}, {"messageId": "2199", "fix": "2421", "desc": "2201"}, {"messageId": "2196", "fix": "2422", "desc": "2198"}, {"messageId": "2199", "fix": "2423", "desc": "2201"}, {"messageId": "2196", "fix": "2424", "desc": "2198"}, {"messageId": "2199", "fix": "2425", "desc": "2201"}, {"messageId": "2196", "fix": "2426", "desc": "2198"}, {"messageId": "2199", "fix": "2427", "desc": "2201"}, {"messageId": "2196", "fix": "2428", "desc": "2198"}, {"messageId": "2199", "fix": "2429", "desc": "2201"}, {"messageId": "2196", "fix": "2430", "desc": "2198"}, {"messageId": "2199", "fix": "2431", "desc": "2201"}, {"messageId": "2196", "fix": "2432", "desc": "2198"}, {"messageId": "2199", "fix": "2433", "desc": "2201"}, {"messageId": "2196", "fix": "2434", "desc": "2198"}, {"messageId": "2199", "fix": "2435", "desc": "2201"}, {"messageId": "2196", "fix": "2436", "desc": "2198"}, {"messageId": "2199", "fix": "2437", "desc": "2201"}, {"messageId": "2196", "fix": "2438", "desc": "2198"}, {"messageId": "2199", "fix": "2439", "desc": "2201"}, {"messageId": "2196", "fix": "2440", "desc": "2198"}, {"messageId": "2199", "fix": "2441", "desc": "2201"}, {"messageId": "2196", "fix": "2442", "desc": "2198"}, {"messageId": "2199", "fix": "2443", "desc": "2201"}, {"messageId": "2196", "fix": "2444", "desc": "2198"}, {"messageId": "2199", "fix": "2445", "desc": "2201"}, {"messageId": "2196", "fix": "2446", "desc": "2198"}, {"messageId": "2199", "fix": "2447", "desc": "2201"}, {"messageId": "2196", "fix": "2448", "desc": "2198"}, {"messageId": "2199", "fix": "2449", "desc": "2201"}, {"messageId": "2196", "fix": "2450", "desc": "2198"}, {"messageId": "2199", "fix": "2451", "desc": "2201"}, {"messageId": "2196", "fix": "2452", "desc": "2198"}, {"messageId": "2199", "fix": "2453", "desc": "2201"}, {"messageId": "2196", "fix": "2454", "desc": "2198"}, {"messageId": "2199", "fix": "2455", "desc": "2201"}, {"messageId": "2196", "fix": "2456", "desc": "2198"}, {"messageId": "2199", "fix": "2457", "desc": "2201"}, {"messageId": "2196", "fix": "2458", "desc": "2198"}, {"messageId": "2199", "fix": "2459", "desc": "2201"}, {"messageId": "2196", "fix": "2460", "desc": "2198"}, {"messageId": "2199", "fix": "2461", "desc": "2201"}, {"messageId": "2196", "fix": "2462", "desc": "2198"}, {"messageId": "2199", "fix": "2463", "desc": "2201"}, {"messageId": "2196", "fix": "2464", "desc": "2198"}, {"messageId": "2199", "fix": "2465", "desc": "2201"}, {"messageId": "2196", "fix": "2466", "desc": "2198"}, {"messageId": "2199", "fix": "2467", "desc": "2201"}, {"messageId": "2196", "fix": "2468", "desc": "2198"}, {"messageId": "2199", "fix": "2469", "desc": "2201"}, {"messageId": "2196", "fix": "2470", "desc": "2198"}, {"messageId": "2199", "fix": "2471", "desc": "2201"}, {"messageId": "2196", "fix": "2472", "desc": "2198"}, {"messageId": "2199", "fix": "2473", "desc": "2201"}, {"messageId": "2196", "fix": "2474", "desc": "2198"}, {"messageId": "2199", "fix": "2475", "desc": "2201"}, {"messageId": "2196", "fix": "2476", "desc": "2198"}, {"messageId": "2199", "fix": "2477", "desc": "2201"}, {"messageId": "2196", "fix": "2478", "desc": "2198"}, {"messageId": "2199", "fix": "2479", "desc": "2201"}, {"messageId": "2196", "fix": "2480", "desc": "2198"}, {"messageId": "2199", "fix": "2481", "desc": "2201"}, {"messageId": "2196", "fix": "2482", "desc": "2198"}, {"messageId": "2199", "fix": "2483", "desc": "2201"}, {"messageId": "2196", "fix": "2484", "desc": "2198"}, {"messageId": "2199", "fix": "2485", "desc": "2201"}, {"messageId": "2196", "fix": "2486", "desc": "2198"}, {"messageId": "2199", "fix": "2487", "desc": "2201"}, {"messageId": "2196", "fix": "2488", "desc": "2198"}, {"messageId": "2199", "fix": "2489", "desc": "2201"}, {"messageId": "2196", "fix": "2490", "desc": "2198"}, {"messageId": "2199", "fix": "2491", "desc": "2201"}, {"messageId": "2196", "fix": "2492", "desc": "2198"}, {"messageId": "2199", "fix": "2493", "desc": "2201"}, {"messageId": "2196", "fix": "2494", "desc": "2198"}, {"messageId": "2199", "fix": "2495", "desc": "2201"}, {"messageId": "2196", "fix": "2496", "desc": "2198"}, {"messageId": "2199", "fix": "2497", "desc": "2201"}, {"messageId": "2196", "fix": "2498", "desc": "2198"}, {"messageId": "2199", "fix": "2499", "desc": "2201"}, {"messageId": "2196", "fix": "2500", "desc": "2198"}, {"messageId": "2199", "fix": "2501", "desc": "2201"}, {"messageId": "2196", "fix": "2502", "desc": "2198"}, {"messageId": "2199", "fix": "2503", "desc": "2201"}, {"messageId": "2196", "fix": "2504", "desc": "2198"}, {"messageId": "2199", "fix": "2505", "desc": "2201"}, {"messageId": "2196", "fix": "2506", "desc": "2198"}, {"messageId": "2199", "fix": "2507", "desc": "2201"}, {"messageId": "2196", "fix": "2508", "desc": "2198"}, {"messageId": "2199", "fix": "2509", "desc": "2201"}, {"messageId": "2196", "fix": "2510", "desc": "2198"}, {"messageId": "2199", "fix": "2511", "desc": "2201"}, {"messageId": "2196", "fix": "2512", "desc": "2198"}, {"messageId": "2199", "fix": "2513", "desc": "2201"}, {"messageId": "2196", "fix": "2514", "desc": "2198"}, {"messageId": "2199", "fix": "2515", "desc": "2201"}, {"messageId": "2196", "fix": "2516", "desc": "2198"}, {"messageId": "2199", "fix": "2517", "desc": "2201"}, {"messageId": "2196", "fix": "2518", "desc": "2198"}, {"messageId": "2199", "fix": "2519", "desc": "2201"}, {"messageId": "2196", "fix": "2520", "desc": "2198"}, {"messageId": "2199", "fix": "2521", "desc": "2201"}, {"messageId": "2196", "fix": "2522", "desc": "2198"}, {"messageId": "2199", "fix": "2523", "desc": "2201"}, {"messageId": "2196", "fix": "2524", "desc": "2198"}, {"messageId": "2199", "fix": "2525", "desc": "2201"}, {"messageId": "2196", "fix": "2526", "desc": "2198"}, {"messageId": "2199", "fix": "2527", "desc": "2201"}, {"messageId": "2196", "fix": "2528", "desc": "2198"}, {"messageId": "2199", "fix": "2529", "desc": "2201"}, {"messageId": "2196", "fix": "2530", "desc": "2198"}, {"messageId": "2199", "fix": "2531", "desc": "2201"}, {"messageId": "2196", "fix": "2532", "desc": "2198"}, {"messageId": "2199", "fix": "2533", "desc": "2201"}, {"messageId": "2196", "fix": "2534", "desc": "2198"}, {"messageId": "2199", "fix": "2535", "desc": "2201"}, {"messageId": "2196", "fix": "2536", "desc": "2198"}, {"messageId": "2199", "fix": "2537", "desc": "2201"}, {"messageId": "2196", "fix": "2538", "desc": "2198"}, {"messageId": "2199", "fix": "2539", "desc": "2201"}, {"messageId": "2196", "fix": "2540", "desc": "2198"}, {"messageId": "2199", "fix": "2541", "desc": "2201"}, {"messageId": "2196", "fix": "2542", "desc": "2198"}, {"messageId": "2199", "fix": "2543", "desc": "2201"}, {"messageId": "2196", "fix": "2544", "desc": "2198"}, {"messageId": "2199", "fix": "2545", "desc": "2201"}, {"messageId": "2196", "fix": "2546", "desc": "2198"}, {"messageId": "2199", "fix": "2547", "desc": "2201"}, {"messageId": "2196", "fix": "2548", "desc": "2198"}, {"messageId": "2199", "fix": "2549", "desc": "2201"}, {"messageId": "2196", "fix": "2550", "desc": "2198"}, {"messageId": "2199", "fix": "2551", "desc": "2201"}, {"messageId": "2196", "fix": "2552", "desc": "2198"}, {"messageId": "2199", "fix": "2553", "desc": "2201"}, {"messageId": "2196", "fix": "2554", "desc": "2198"}, {"messageId": "2199", "fix": "2555", "desc": "2201"}, {"messageId": "2196", "fix": "2556", "desc": "2198"}, {"messageId": "2199", "fix": "2557", "desc": "2201"}, {"messageId": "2196", "fix": "2558", "desc": "2198"}, {"messageId": "2199", "fix": "2559", "desc": "2201"}, {"messageId": "2196", "fix": "2560", "desc": "2198"}, {"messageId": "2199", "fix": "2561", "desc": "2201"}, {"messageId": "2196", "fix": "2562", "desc": "2198"}, {"messageId": "2199", "fix": "2563", "desc": "2201"}, {"messageId": "2196", "fix": "2564", "desc": "2198"}, {"messageId": "2199", "fix": "2565", "desc": "2201"}, {"messageId": "2196", "fix": "2566", "desc": "2198"}, {"messageId": "2199", "fix": "2567", "desc": "2201"}, {"messageId": "2196", "fix": "2568", "desc": "2198"}, {"messageId": "2199", "fix": "2569", "desc": "2201"}, {"messageId": "2196", "fix": "2570", "desc": "2198"}, {"messageId": "2199", "fix": "2571", "desc": "2201"}, {"messageId": "2196", "fix": "2572", "desc": "2198"}, {"messageId": "2199", "fix": "2573", "desc": "2201"}, {"messageId": "2196", "fix": "2574", "desc": "2198"}, {"messageId": "2199", "fix": "2575", "desc": "2201"}, {"messageId": "2196", "fix": "2576", "desc": "2198"}, {"messageId": "2199", "fix": "2577", "desc": "2201"}, {"messageId": "2196", "fix": "2578", "desc": "2198"}, {"messageId": "2199", "fix": "2579", "desc": "2201"}, {"messageId": "2196", "fix": "2580", "desc": "2198"}, {"messageId": "2199", "fix": "2581", "desc": "2201"}, {"messageId": "2196", "fix": "2582", "desc": "2198"}, {"messageId": "2199", "fix": "2583", "desc": "2201"}, {"messageId": "2196", "fix": "2584", "desc": "2198"}, {"messageId": "2199", "fix": "2585", "desc": "2201"}, {"messageId": "2196", "fix": "2586", "desc": "2198"}, {"messageId": "2199", "fix": "2587", "desc": "2201"}, {"messageId": "2196", "fix": "2588", "desc": "2198"}, {"messageId": "2199", "fix": "2589", "desc": "2201"}, {"messageId": "2196", "fix": "2590", "desc": "2198"}, {"messageId": "2199", "fix": "2591", "desc": "2201"}, {"messageId": "2196", "fix": "2592", "desc": "2198"}, {"messageId": "2199", "fix": "2593", "desc": "2201"}, {"messageId": "2196", "fix": "2594", "desc": "2198"}, {"messageId": "2199", "fix": "2595", "desc": "2201"}, {"messageId": "2196", "fix": "2596", "desc": "2198"}, {"messageId": "2199", "fix": "2597", "desc": "2201"}, {"messageId": "2196", "fix": "2598", "desc": "2198"}, {"messageId": "2199", "fix": "2599", "desc": "2201"}, {"messageId": "2196", "fix": "2600", "desc": "2198"}, {"messageId": "2199", "fix": "2601", "desc": "2201"}, {"messageId": "2196", "fix": "2602", "desc": "2198"}, {"messageId": "2199", "fix": "2603", "desc": "2201"}, {"messageId": "2196", "fix": "2604", "desc": "2198"}, {"messageId": "2199", "fix": "2605", "desc": "2201"}, {"messageId": "2196", "fix": "2606", "desc": "2198"}, {"messageId": "2199", "fix": "2607", "desc": "2201"}, {"messageId": "2196", "fix": "2608", "desc": "2198"}, {"messageId": "2199", "fix": "2609", "desc": "2201"}, {"messageId": "2196", "fix": "2610", "desc": "2198"}, {"messageId": "2199", "fix": "2611", "desc": "2201"}, {"messageId": "2196", "fix": "2612", "desc": "2198"}, {"messageId": "2199", "fix": "2613", "desc": "2201"}, {"messageId": "2196", "fix": "2614", "desc": "2198"}, {"messageId": "2199", "fix": "2615", "desc": "2201"}, {"messageId": "2196", "fix": "2616", "desc": "2198"}, {"messageId": "2199", "fix": "2617", "desc": "2201"}, {"messageId": "2196", "fix": "2618", "desc": "2198"}, {"messageId": "2199", "fix": "2619", "desc": "2201"}, {"messageId": "2196", "fix": "2620", "desc": "2198"}, {"messageId": "2199", "fix": "2621", "desc": "2201"}, {"messageId": "2196", "fix": "2622", "desc": "2198"}, {"messageId": "2199", "fix": "2623", "desc": "2201"}, {"messageId": "2196", "fix": "2624", "desc": "2198"}, {"messageId": "2199", "fix": "2625", "desc": "2201"}, {"messageId": "2196", "fix": "2626", "desc": "2198"}, {"messageId": "2199", "fix": "2627", "desc": "2201"}, {"messageId": "2196", "fix": "2628", "desc": "2198"}, {"messageId": "2199", "fix": "2629", "desc": "2201"}, {"messageId": "2196", "fix": "2630", "desc": "2198"}, {"messageId": "2199", "fix": "2631", "desc": "2201"}, {"messageId": "2196", "fix": "2632", "desc": "2198"}, {"messageId": "2199", "fix": "2633", "desc": "2201"}, {"messageId": "2196", "fix": "2634", "desc": "2198"}, {"messageId": "2199", "fix": "2635", "desc": "2201"}, {"messageId": "2196", "fix": "2636", "desc": "2198"}, {"messageId": "2199", "fix": "2637", "desc": "2201"}, [8438, 8502], "const queryBuilder = this.client.from(table).select(query || '*');", {"messageId": "2196", "fix": "2638", "desc": "2198"}, {"messageId": "2199", "fix": "2639", "desc": "2201"}, {"messageId": "2196", "fix": "2640", "desc": "2198"}, {"messageId": "2199", "fix": "2641", "desc": "2201"}, {"messageId": "2196", "fix": "2642", "desc": "2198"}, {"messageId": "2199", "fix": "2643", "desc": "2201"}, {"messageId": "2196", "fix": "2644", "desc": "2198"}, {"messageId": "2199", "fix": "2645", "desc": "2201"}, {"messageId": "2196", "fix": "2646", "desc": "2198"}, {"messageId": "2199", "fix": "2647", "desc": "2201"}, {"messageId": "2196", "fix": "2648", "desc": "2198"}, {"messageId": "2199", "fix": "2649", "desc": "2201"}, {"messageId": "2196", "fix": "2650", "desc": "2198"}, {"messageId": "2199", "fix": "2651", "desc": "2201"}, {"messageId": "2196", "fix": "2652", "desc": "2198"}, {"messageId": "2199", "fix": "2653", "desc": "2201"}, {"messageId": "2196", "fix": "2654", "desc": "2198"}, {"messageId": "2199", "fix": "2655", "desc": "2201"}, {"messageId": "2196", "fix": "2656", "desc": "2198"}, {"messageId": "2199", "fix": "2657", "desc": "2201"}, {"messageId": "2196", "fix": "2658", "desc": "2198"}, {"messageId": "2199", "fix": "2659", "desc": "2201"}, {"messageId": "2196", "fix": "2660", "desc": "2198"}, {"messageId": "2199", "fix": "2661", "desc": "2201"}, {"messageId": "2196", "fix": "2662", "desc": "2198"}, {"messageId": "2199", "fix": "2663", "desc": "2201"}, {"messageId": "2196", "fix": "2664", "desc": "2198"}, {"messageId": "2199", "fix": "2665", "desc": "2201"}, {"messageId": "2196", "fix": "2666", "desc": "2198"}, {"messageId": "2199", "fix": "2667", "desc": "2201"}, {"messageId": "2196", "fix": "2668", "desc": "2198"}, {"messageId": "2199", "fix": "2669", "desc": "2201"}, {"messageId": "2196", "fix": "2670", "desc": "2198"}, {"messageId": "2199", "fix": "2671", "desc": "2201"}, {"messageId": "2196", "fix": "2672", "desc": "2198"}, {"messageId": "2199", "fix": "2673", "desc": "2201"}, {"messageId": "2196", "fix": "2674", "desc": "2198"}, {"messageId": "2199", "fix": "2675", "desc": "2201"}, {"messageId": "2196", "fix": "2676", "desc": "2198"}, {"messageId": "2199", "fix": "2677", "desc": "2201"}, {"messageId": "2196", "fix": "2678", "desc": "2198"}, {"messageId": "2199", "fix": "2679", "desc": "2201"}, {"messageId": "2196", "fix": "2680", "desc": "2198"}, {"messageId": "2199", "fix": "2681", "desc": "2201"}, [7477, 7504], "const clsEntries: any[] = [];", {"messageId": "2196", "fix": "2682", "desc": "2198"}, {"messageId": "2199", "fix": "2683", "desc": "2201"}, {"messageId": "2196", "fix": "2684", "desc": "2198"}, {"messageId": "2199", "fix": "2685", "desc": "2201"}, {"messageId": "2196", "fix": "2686", "desc": "2198"}, {"messageId": "2199", "fix": "2687", "desc": "2201"}, {"messageId": "2196", "fix": "2688", "desc": "2198"}, {"messageId": "2199", "fix": "2689", "desc": "2201"}, {"messageId": "2196", "fix": "2690", "desc": "2198"}, {"messageId": "2199", "fix": "2691", "desc": "2201"}, {"messageId": "2196", "fix": "2692", "desc": "2198"}, {"messageId": "2199", "fix": "2693", "desc": "2201"}, {"messageId": "2196", "fix": "2694", "desc": "2198"}, {"messageId": "2199", "fix": "2695", "desc": "2201"}, {"messageId": "2196", "fix": "2696", "desc": "2198"}, {"messageId": "2199", "fix": "2697", "desc": "2201"}, {"messageId": "2196", "fix": "2698", "desc": "2198"}, {"messageId": "2199", "fix": "2699", "desc": "2201"}, {"messageId": "2196", "fix": "2700", "desc": "2198"}, {"messageId": "2199", "fix": "2701", "desc": "2201"}, {"messageId": "2196", "fix": "2702", "desc": "2198"}, {"messageId": "2199", "fix": "2703", "desc": "2201"}, {"messageId": "2196", "fix": "2704", "desc": "2198"}, {"messageId": "2199", "fix": "2705", "desc": "2201"}, {"messageId": "2196", "fix": "2706", "desc": "2198"}, {"messageId": "2199", "fix": "2707", "desc": "2201"}, {"messageId": "2196", "fix": "2708", "desc": "2198"}, {"messageId": "2199", "fix": "2709", "desc": "2201"}, {"messageId": "2196", "fix": "2710", "desc": "2198"}, {"messageId": "2199", "fix": "2711", "desc": "2201"}, {"messageId": "2196", "fix": "2712", "desc": "2198"}, {"messageId": "2199", "fix": "2713", "desc": "2201"}, {"messageId": "2196", "fix": "2714", "desc": "2198"}, {"messageId": "2199", "fix": "2715", "desc": "2201"}, {"messageId": "2196", "fix": "2716", "desc": "2198"}, {"messageId": "2199", "fix": "2717", "desc": "2201"}, {"messageId": "2196", "fix": "2718", "desc": "2198"}, {"messageId": "2199", "fix": "2719", "desc": "2201"}, {"messageId": "2196", "fix": "2720", "desc": "2198"}, {"messageId": "2199", "fix": "2721", "desc": "2201"}, {"messageId": "2196", "fix": "2722", "desc": "2198"}, {"messageId": "2199", "fix": "2723", "desc": "2201"}, {"messageId": "2196", "fix": "2724", "desc": "2198"}, {"messageId": "2199", "fix": "2725", "desc": "2201"}, {"messageId": "2196", "fix": "2726", "desc": "2198"}, {"messageId": "2199", "fix": "2727", "desc": "2201"}, {"messageId": "2196", "fix": "2728", "desc": "2198"}, {"messageId": "2199", "fix": "2729", "desc": "2201"}, {"messageId": "2196", "fix": "2730", "desc": "2198"}, {"messageId": "2199", "fix": "2731", "desc": "2201"}, {"messageId": "2196", "fix": "2732", "desc": "2198"}, {"messageId": "2199", "fix": "2733", "desc": "2201"}, {"messageId": "2196", "fix": "2734", "desc": "2198"}, {"messageId": "2199", "fix": "2735", "desc": "2201"}, {"messageId": "2196", "fix": "2736", "desc": "2198"}, {"messageId": "2199", "fix": "2737", "desc": "2201"}, {"messageId": "2196", "fix": "2738", "desc": "2198"}, {"messageId": "2199", "fix": "2739", "desc": "2201"}, {"messageId": "2196", "fix": "2740", "desc": "2198"}, {"messageId": "2199", "fix": "2741", "desc": "2201"}, {"messageId": "2196", "fix": "2742", "desc": "2198"}, {"messageId": "2199", "fix": "2743", "desc": "2201"}, {"messageId": "2196", "fix": "2744", "desc": "2198"}, {"messageId": "2199", "fix": "2745", "desc": "2201"}, {"messageId": "2196", "fix": "2746", "desc": "2198"}, {"messageId": "2199", "fix": "2747", "desc": "2201"}, {"messageId": "2196", "fix": "2748", "desc": "2198"}, {"messageId": "2199", "fix": "2749", "desc": "2201"}, {"messageId": "2196", "fix": "2750", "desc": "2198"}, {"messageId": "2199", "fix": "2751", "desc": "2201"}, {"messageId": "2196", "fix": "2752", "desc": "2198"}, {"messageId": "2199", "fix": "2753", "desc": "2201"}, {"messageId": "2196", "fix": "2754", "desc": "2198"}, {"messageId": "2199", "fix": "2755", "desc": "2201"}, {"messageId": "2196", "fix": "2756", "desc": "2198"}, {"messageId": "2199", "fix": "2757", "desc": "2201"}, {"messageId": "2196", "fix": "2758", "desc": "2198"}, {"messageId": "2199", "fix": "2759", "desc": "2201"}, {"messageId": "2196", "fix": "2760", "desc": "2198"}, {"messageId": "2199", "fix": "2761", "desc": "2201"}, {"messageId": "2196", "fix": "2762", "desc": "2198"}, {"messageId": "2199", "fix": "2763", "desc": "2201"}, {"messageId": "2196", "fix": "2764", "desc": "2198"}, {"messageId": "2199", "fix": "2765", "desc": "2201"}, {"messageId": "2196", "fix": "2766", "desc": "2198"}, {"messageId": "2199", "fix": "2767", "desc": "2201"}, {"messageId": "2196", "fix": "2768", "desc": "2198"}, {"messageId": "2199", "fix": "2769", "desc": "2201"}, {"messageId": "2196", "fix": "2770", "desc": "2198"}, {"messageId": "2199", "fix": "2771", "desc": "2201"}, {"messageId": "2196", "fix": "2772", "desc": "2198"}, {"messageId": "2199", "fix": "2773", "desc": "2201"}, {"messageId": "2196", "fix": "2774", "desc": "2198"}, {"messageId": "2199", "fix": "2775", "desc": "2201"}, {"messageId": "2196", "fix": "2776", "desc": "2198"}, {"messageId": "2199", "fix": "2777", "desc": "2201"}, {"messageId": "2196", "fix": "2778", "desc": "2198"}, {"messageId": "2199", "fix": "2779", "desc": "2201"}, {"messageId": "2196", "fix": "2780", "desc": "2198"}, {"messageId": "2199", "fix": "2781", "desc": "2201"}, {"messageId": "2196", "fix": "2782", "desc": "2198"}, {"messageId": "2199", "fix": "2783", "desc": "2201"}, {"messageId": "2196", "fix": "2784", "desc": "2198"}, {"messageId": "2199", "fix": "2785", "desc": "2201"}, {"messageId": "2196", "fix": "2786", "desc": "2198"}, {"messageId": "2199", "fix": "2787", "desc": "2201"}, {"messageId": "2196", "fix": "2788", "desc": "2198"}, {"messageId": "2199", "fix": "2789", "desc": "2201"}, {"messageId": "2196", "fix": "2790", "desc": "2198"}, {"messageId": "2199", "fix": "2791", "desc": "2201"}, {"messageId": "2196", "fix": "2792", "desc": "2198"}, {"messageId": "2199", "fix": "2793", "desc": "2201"}, {"messageId": "2196", "fix": "2794", "desc": "2198"}, {"messageId": "2199", "fix": "2795", "desc": "2201"}, "replaceWithAlt", {"alt": "2796"}, {"range": "2797", "text": "2798"}, "Replace with `&apos;`.", {"alt": "2799"}, {"range": "2800", "text": "2801"}, "Replace with `&lsquo;`.", {"alt": "2802"}, {"range": "2803", "text": "2804"}, "Replace with `&#39;`.", {"alt": "2805"}, {"range": "2806", "text": "2807"}, "Replace with `&rsquo;`.", {"alt": "2796"}, {"range": "2808", "text": "2809"}, {"alt": "2799"}, {"range": "2810", "text": "2811"}, {"alt": "2802"}, {"range": "2812", "text": "2813"}, {"alt": "2805"}, {"range": "2814", "text": "2815"}, {"alt": "2796"}, {"range": "2816", "text": "2817"}, {"alt": "2799"}, {"range": "2818", "text": "2819"}, {"alt": "2802"}, {"range": "2820", "text": "2821"}, {"alt": "2805"}, {"range": "2822", "text": "2823"}, {"alt": "2796"}, {"range": "2824", "text": "2825"}, {"alt": "2799"}, {"range": "2826", "text": "2827"}, {"alt": "2802"}, {"range": "2828", "text": "2829"}, {"alt": "2805"}, {"range": "2830", "text": "2831"}, {"alt": "2796"}, {"range": "2832", "text": "2833"}, {"alt": "2799"}, {"range": "2834", "text": "2835"}, {"alt": "2802"}, {"range": "2836", "text": "2837"}, {"alt": "2805"}, {"range": "2838", "text": "2839"}, "suggestUnknown", {"range": "2840", "text": "2841"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2842", "text": "2843"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2844", "text": "2841"}, {"range": "2845", "text": "2843"}, {"range": "2846", "text": "2841"}, {"range": "2847", "text": "2843"}, {"range": "2848", "text": "2841"}, {"range": "2849", "text": "2843"}, {"range": "2850", "text": "2841"}, {"range": "2851", "text": "2843"}, {"range": "2852", "text": "2841"}, {"range": "2853", "text": "2843"}, {"range": "2854", "text": "2841"}, {"range": "2855", "text": "2843"}, {"range": "2856", "text": "2841"}, {"range": "2857", "text": "2843"}, {"range": "2858", "text": "2841"}, {"range": "2859", "text": "2843"}, {"range": "2860", "text": "2841"}, {"range": "2861", "text": "2843"}, {"range": "2862", "text": "2841"}, {"range": "2863", "text": "2843"}, {"range": "2864", "text": "2841"}, {"range": "2865", "text": "2843"}, {"range": "2866", "text": "2841"}, {"range": "2867", "text": "2843"}, {"range": "2868", "text": "2841"}, {"range": "2869", "text": "2843"}, {"range": "2870", "text": "2841"}, {"range": "2871", "text": "2843"}, {"range": "2872", "text": "2841"}, {"range": "2873", "text": "2843"}, {"range": "2874", "text": "2841"}, {"range": "2875", "text": "2843"}, {"range": "2876", "text": "2841"}, {"range": "2877", "text": "2843"}, {"range": "2878", "text": "2841"}, {"range": "2879", "text": "2843"}, {"range": "2880", "text": "2841"}, {"range": "2881", "text": "2843"}, {"range": "2882", "text": "2841"}, {"range": "2883", "text": "2843"}, {"range": "2884", "text": "2841"}, {"range": "2885", "text": "2843"}, {"range": "2886", "text": "2841"}, {"range": "2887", "text": "2843"}, {"range": "2888", "text": "2841"}, {"range": "2889", "text": "2843"}, {"range": "2890", "text": "2841"}, {"range": "2891", "text": "2843"}, {"range": "2892", "text": "2841"}, {"range": "2893", "text": "2843"}, {"range": "2894", "text": "2841"}, {"range": "2895", "text": "2843"}, {"range": "2896", "text": "2841"}, {"range": "2897", "text": "2843"}, {"range": "2898", "text": "2841"}, {"range": "2899", "text": "2843"}, {"range": "2900", "text": "2841"}, {"range": "2901", "text": "2843"}, {"range": "2902", "text": "2841"}, {"range": "2903", "text": "2843"}, {"range": "2904", "text": "2841"}, {"range": "2905", "text": "2843"}, {"range": "2906", "text": "2841"}, {"range": "2907", "text": "2843"}, {"range": "2908", "text": "2841"}, {"range": "2909", "text": "2843"}, {"range": "2910", "text": "2841"}, {"range": "2911", "text": "2843"}, {"range": "2912", "text": "2841"}, {"range": "2913", "text": "2843"}, {"range": "2914", "text": "2841"}, {"range": "2915", "text": "2843"}, {"range": "2916", "text": "2841"}, {"range": "2917", "text": "2843"}, {"range": "2918", "text": "2841"}, {"range": "2919", "text": "2843"}, {"range": "2920", "text": "2841"}, {"range": "2921", "text": "2843"}, {"range": "2922", "text": "2841"}, {"range": "2923", "text": "2843"}, {"range": "2924", "text": "2841"}, {"range": "2925", "text": "2843"}, {"range": "2926", "text": "2841"}, {"range": "2927", "text": "2843"}, {"range": "2928", "text": "2841"}, {"range": "2929", "text": "2843"}, {"range": "2930", "text": "2841"}, {"range": "2931", "text": "2843"}, {"range": "2932", "text": "2841"}, {"range": "2933", "text": "2843"}, {"range": "2934", "text": "2841"}, {"range": "2935", "text": "2843"}, {"range": "2936", "text": "2841"}, {"range": "2937", "text": "2843"}, {"range": "2938", "text": "2841"}, {"range": "2939", "text": "2843"}, {"range": "2940", "text": "2841"}, {"range": "2941", "text": "2843"}, {"range": "2942", "text": "2841"}, {"range": "2943", "text": "2843"}, {"range": "2944", "text": "2841"}, {"range": "2945", "text": "2843"}, {"range": "2946", "text": "2841"}, {"range": "2947", "text": "2843"}, {"range": "2948", "text": "2841"}, {"range": "2949", "text": "2843"}, {"range": "2950", "text": "2841"}, {"range": "2951", "text": "2843"}, {"range": "2952", "text": "2841"}, {"range": "2953", "text": "2843"}, {"range": "2954", "text": "2841"}, {"range": "2955", "text": "2843"}, {"range": "2956", "text": "2841"}, {"range": "2957", "text": "2843"}, {"range": "2958", "text": "2841"}, {"range": "2959", "text": "2843"}, {"range": "2960", "text": "2841"}, {"range": "2961", "text": "2843"}, {"range": "2962", "text": "2841"}, {"range": "2963", "text": "2843"}, {"range": "2964", "text": "2841"}, {"range": "2965", "text": "2843"}, {"range": "2966", "text": "2841"}, {"range": "2967", "text": "2843"}, {"range": "2968", "text": "2841"}, {"range": "2969", "text": "2843"}, {"range": "2970", "text": "2841"}, {"range": "2971", "text": "2843"}, {"range": "2972", "text": "2841"}, {"range": "2973", "text": "2843"}, {"range": "2974", "text": "2841"}, {"range": "2975", "text": "2843"}, {"range": "2976", "text": "2841"}, {"range": "2977", "text": "2843"}, "Update the dependencies array to be: [isOpen, side, align, updatePosition]", {"range": "2978", "text": "2979"}, "Update the dependencies array to be: [isVisible, side, updatePosition]", {"range": "2980", "text": "2981"}, {"range": "2982", "text": "2841"}, {"range": "2983", "text": "2843"}, {"range": "2984", "text": "2841"}, {"range": "2985", "text": "2843"}, {"range": "2986", "text": "2841"}, {"range": "2987", "text": "2843"}, {"range": "2988", "text": "2841"}, {"range": "2989", "text": "2843"}, {"range": "2990", "text": "2841"}, {"range": "2991", "text": "2843"}, {"range": "2992", "text": "2841"}, {"range": "2993", "text": "2843"}, {"range": "2994", "text": "2841"}, {"range": "2995", "text": "2843"}, {"range": "2996", "text": "2841"}, {"range": "2997", "text": "2843"}, {"range": "2998", "text": "2841"}, {"range": "2999", "text": "2843"}, {"range": "3000", "text": "2841"}, {"range": "3001", "text": "2843"}, "Update the dependencies array to be: [loadDashboardData, timeRange]", {"range": "3002", "text": "3003"}, {"range": "3004", "text": "2841"}, {"range": "3005", "text": "2843"}, {"range": "3006", "text": "2841"}, {"range": "3007", "text": "2843"}, {"range": "3008", "text": "2841"}, {"range": "3009", "text": "2843"}, {"range": "3010", "text": "2841"}, {"range": "3011", "text": "2843"}, {"range": "3012", "text": "2841"}, {"range": "3013", "text": "2843"}, {"alt": "2796"}, {"range": "3014", "text": "3015"}, {"alt": "2799"}, {"range": "3016", "text": "3017"}, {"alt": "2802"}, {"range": "3018", "text": "3019"}, {"alt": "2805"}, {"range": "3020", "text": "3021"}, {"range": "3022", "text": "2841"}, {"range": "3023", "text": "2843"}, {"range": "3024", "text": "2841"}, {"range": "3025", "text": "2843"}, {"range": "3026", "text": "2841"}, {"range": "3027", "text": "2843"}, {"range": "3028", "text": "2841"}, {"range": "3029", "text": "2843"}, {"range": "3030", "text": "2841"}, {"range": "3031", "text": "2843"}, {"range": "3032", "text": "2841"}, {"range": "3033", "text": "2843"}, {"range": "3034", "text": "2841"}, {"range": "3035", "text": "2843"}, {"range": "3036", "text": "2841"}, {"range": "3037", "text": "2843"}, {"range": "3038", "text": "2841"}, {"range": "3039", "text": "2843"}, {"range": "3040", "text": "2841"}, {"range": "3041", "text": "2843"}, {"range": "3042", "text": "2841"}, {"range": "3043", "text": "2843"}, {"range": "3044", "text": "2841"}, {"range": "3045", "text": "2843"}, {"range": "3046", "text": "2841"}, {"range": "3047", "text": "2843"}, {"range": "3048", "text": "2841"}, {"range": "3049", "text": "2843"}, {"range": "3050", "text": "2841"}, {"range": "3051", "text": "2843"}, {"range": "3052", "text": "2841"}, {"range": "3053", "text": "2843"}, {"range": "3054", "text": "2841"}, {"range": "3055", "text": "2843"}, {"range": "3056", "text": "2841"}, {"range": "3057", "text": "2843"}, {"range": "3058", "text": "2841"}, {"range": "3059", "text": "2843"}, {"range": "3060", "text": "2841"}, {"range": "3061", "text": "2843"}, {"range": "3062", "text": "2841"}, {"range": "3063", "text": "2843"}, {"range": "3064", "text": "2841"}, {"range": "3065", "text": "2843"}, {"range": "3066", "text": "2841"}, {"range": "3067", "text": "2843"}, {"range": "3068", "text": "2841"}, {"range": "3069", "text": "2843"}, {"range": "3070", "text": "2841"}, {"range": "3071", "text": "2843"}, {"range": "3072", "text": "2841"}, {"range": "3073", "text": "2843"}, {"range": "3074", "text": "2841"}, {"range": "3075", "text": "2843"}, {"range": "3076", "text": "2841"}, {"range": "3077", "text": "2843"}, {"range": "3078", "text": "2841"}, {"range": "3079", "text": "2843"}, {"range": "3080", "text": "2841"}, {"range": "3081", "text": "2843"}, {"range": "3082", "text": "2841"}, {"range": "3083", "text": "2843"}, {"range": "3084", "text": "2841"}, {"range": "3085", "text": "2843"}, {"range": "3086", "text": "2841"}, {"range": "3087", "text": "2843"}, {"range": "3088", "text": "2841"}, {"range": "3089", "text": "2843"}, {"range": "3090", "text": "2841"}, {"range": "3091", "text": "2843"}, {"range": "3092", "text": "2841"}, {"range": "3093", "text": "2843"}, {"range": "3094", "text": "2841"}, {"range": "3095", "text": "2843"}, {"range": "3096", "text": "2841"}, {"range": "3097", "text": "2843"}, {"range": "3098", "text": "2841"}, {"range": "3099", "text": "2843"}, {"range": "3100", "text": "2841"}, {"range": "3101", "text": "2843"}, {"range": "3102", "text": "2841"}, {"range": "3103", "text": "2843"}, {"range": "3104", "text": "2841"}, {"range": "3105", "text": "2843"}, {"range": "3106", "text": "2841"}, {"range": "3107", "text": "2843"}, {"range": "3108", "text": "2841"}, {"range": "3109", "text": "2843"}, {"range": "3110", "text": "2841"}, {"range": "3111", "text": "2843"}, {"range": "3112", "text": "2841"}, {"range": "3113", "text": "2843"}, {"range": "3114", "text": "2841"}, {"range": "3115", "text": "2843"}, {"range": "3116", "text": "2841"}, {"range": "3117", "text": "2843"}, {"range": "3118", "text": "2841"}, {"range": "3119", "text": "2843"}, {"range": "3120", "text": "2841"}, {"range": "3121", "text": "2843"}, {"range": "3122", "text": "2841"}, {"range": "3123", "text": "2843"}, {"range": "3124", "text": "2841"}, {"range": "3125", "text": "2843"}, {"range": "3126", "text": "2841"}, {"range": "3127", "text": "2843"}, {"range": "3128", "text": "2841"}, {"range": "3129", "text": "2843"}, {"range": "3130", "text": "2841"}, {"range": "3131", "text": "2843"}, {"range": "3132", "text": "2841"}, {"range": "3133", "text": "2843"}, {"range": "3134", "text": "2841"}, {"range": "3135", "text": "2843"}, {"range": "3136", "text": "2841"}, {"range": "3137", "text": "2843"}, {"range": "3138", "text": "2841"}, {"range": "3139", "text": "2843"}, {"range": "3140", "text": "2841"}, {"range": "3141", "text": "2843"}, {"range": "3142", "text": "2841"}, {"range": "3143", "text": "2843"}, {"range": "3144", "text": "2841"}, {"range": "3145", "text": "2843"}, {"range": "3146", "text": "2841"}, {"range": "3147", "text": "2843"}, {"range": "3148", "text": "2841"}, {"range": "3149", "text": "2843"}, {"range": "3150", "text": "2841"}, {"range": "3151", "text": "2843"}, {"range": "3152", "text": "2841"}, {"range": "3153", "text": "2843"}, {"range": "3154", "text": "2841"}, {"range": "3155", "text": "2843"}, {"range": "3156", "text": "2841"}, {"range": "3157", "text": "2843"}, {"range": "3158", "text": "2841"}, {"range": "3159", "text": "2843"}, {"range": "3160", "text": "2841"}, {"range": "3161", "text": "2843"}, {"range": "3162", "text": "2841"}, {"range": "3163", "text": "2843"}, {"range": "3164", "text": "2841"}, {"range": "3165", "text": "2843"}, {"range": "3166", "text": "2841"}, {"range": "3167", "text": "2843"}, {"range": "3168", "text": "2841"}, {"range": "3169", "text": "2843"}, {"range": "3170", "text": "2841"}, {"range": "3171", "text": "2843"}, {"range": "3172", "text": "2841"}, {"range": "3173", "text": "2843"}, {"range": "3174", "text": "2841"}, {"range": "3175", "text": "2843"}, {"range": "3176", "text": "2841"}, {"range": "3177", "text": "2843"}, {"range": "3178", "text": "2841"}, {"range": "3179", "text": "2843"}, {"range": "3180", "text": "2841"}, {"range": "3181", "text": "2843"}, {"range": "3182", "text": "2841"}, {"range": "3183", "text": "2843"}, {"range": "3184", "text": "2841"}, {"range": "3185", "text": "2843"}, {"range": "3186", "text": "2841"}, {"range": "3187", "text": "2843"}, {"range": "3188", "text": "2841"}, {"range": "3189", "text": "2843"}, {"range": "3190", "text": "2841"}, {"range": "3191", "text": "2843"}, {"range": "3192", "text": "2841"}, {"range": "3193", "text": "2843"}, {"range": "3194", "text": "2841"}, {"range": "3195", "text": "2843"}, {"range": "3196", "text": "2841"}, {"range": "3197", "text": "2843"}, {"range": "3198", "text": "2841"}, {"range": "3199", "text": "2843"}, {"range": "3200", "text": "2841"}, {"range": "3201", "text": "2843"}, {"range": "3202", "text": "2841"}, {"range": "3203", "text": "2843"}, {"range": "3204", "text": "2841"}, {"range": "3205", "text": "2843"}, {"range": "3206", "text": "2841"}, {"range": "3207", "text": "2843"}, {"range": "3208", "text": "2841"}, {"range": "3209", "text": "2843"}, {"range": "3210", "text": "2841"}, {"range": "3211", "text": "2843"}, {"range": "3212", "text": "2841"}, {"range": "3213", "text": "2843"}, {"range": "3214", "text": "2841"}, {"range": "3215", "text": "2843"}, {"range": "3216", "text": "2841"}, {"range": "3217", "text": "2843"}, {"range": "3218", "text": "2841"}, {"range": "3219", "text": "2843"}, {"range": "3220", "text": "2841"}, {"range": "3221", "text": "2843"}, {"range": "3222", "text": "2841"}, {"range": "3223", "text": "2843"}, {"range": "3224", "text": "2841"}, {"range": "3225", "text": "2843"}, {"range": "3226", "text": "2841"}, {"range": "3227", "text": "2843"}, {"range": "3228", "text": "2841"}, {"range": "3229", "text": "2843"}, {"range": "3230", "text": "2841"}, {"range": "3231", "text": "2843"}, {"range": "3232", "text": "2841"}, {"range": "3233", "text": "2843"}, {"range": "3234", "text": "2841"}, {"range": "3235", "text": "2843"}, {"range": "3236", "text": "2841"}, {"range": "3237", "text": "2843"}, {"range": "3238", "text": "2841"}, {"range": "3239", "text": "2843"}, {"range": "3240", "text": "2841"}, {"range": "3241", "text": "2843"}, {"range": "3242", "text": "2841"}, {"range": "3243", "text": "2843"}, {"range": "3244", "text": "2841"}, {"range": "3245", "text": "2843"}, {"range": "3246", "text": "2841"}, {"range": "3247", "text": "2843"}, {"range": "3248", "text": "2841"}, {"range": "3249", "text": "2843"}, {"range": "3250", "text": "2841"}, {"range": "3251", "text": "2843"}, {"range": "3252", "text": "2841"}, {"range": "3253", "text": "2843"}, {"range": "3254", "text": "2841"}, {"range": "3255", "text": "2843"}, {"range": "3256", "text": "2841"}, {"range": "3257", "text": "2843"}, {"range": "3258", "text": "2841"}, {"range": "3259", "text": "2843"}, {"range": "3260", "text": "2841"}, {"range": "3261", "text": "2843"}, {"range": "3262", "text": "2841"}, {"range": "3263", "text": "2843"}, {"range": "3264", "text": "2841"}, {"range": "3265", "text": "2843"}, {"range": "3266", "text": "2841"}, {"range": "3267", "text": "2843"}, {"range": "3268", "text": "2841"}, {"range": "3269", "text": "2843"}, {"range": "3270", "text": "2841"}, {"range": "3271", "text": "2843"}, {"range": "3272", "text": "2841"}, {"range": "3273", "text": "2843"}, {"range": "3274", "text": "2841"}, {"range": "3275", "text": "2843"}, {"range": "3276", "text": "2841"}, {"range": "3277", "text": "2843"}, {"range": "3278", "text": "2841"}, {"range": "3279", "text": "2843"}, {"range": "3280", "text": "2841"}, {"range": "3281", "text": "2843"}, {"range": "3282", "text": "2841"}, {"range": "3283", "text": "2843"}, {"range": "3284", "text": "2841"}, {"range": "3285", "text": "2843"}, {"range": "3286", "text": "2841"}, {"range": "3287", "text": "2843"}, {"range": "3288", "text": "2841"}, {"range": "3289", "text": "2843"}, {"range": "3290", "text": "2841"}, {"range": "3291", "text": "2843"}, {"range": "3292", "text": "2841"}, {"range": "3293", "text": "2843"}, {"range": "3294", "text": "2841"}, {"range": "3295", "text": "2843"}, {"range": "3296", "text": "2841"}, {"range": "3297", "text": "2843"}, {"range": "3298", "text": "2841"}, {"range": "3299", "text": "2843"}, {"range": "3300", "text": "2841"}, {"range": "3301", "text": "2843"}, {"range": "3302", "text": "2841"}, {"range": "3303", "text": "2843"}, {"range": "3304", "text": "2841"}, {"range": "3305", "text": "2843"}, {"range": "3306", "text": "2841"}, {"range": "3307", "text": "2843"}, {"range": "3308", "text": "2841"}, {"range": "3309", "text": "2843"}, {"range": "3310", "text": "2841"}, {"range": "3311", "text": "2843"}, {"range": "3312", "text": "2841"}, {"range": "3313", "text": "2843"}, {"range": "3314", "text": "2841"}, {"range": "3315", "text": "2843"}, {"range": "3316", "text": "2841"}, {"range": "3317", "text": "2843"}, {"range": "3318", "text": "2841"}, {"range": "3319", "text": "2843"}, {"range": "3320", "text": "2841"}, {"range": "3321", "text": "2843"}, {"range": "3322", "text": "2841"}, {"range": "3323", "text": "2843"}, {"range": "3324", "text": "2841"}, {"range": "3325", "text": "2843"}, {"range": "3326", "text": "2841"}, {"range": "3327", "text": "2843"}, {"range": "3328", "text": "2841"}, {"range": "3329", "text": "2843"}, {"range": "3330", "text": "2841"}, {"range": "3331", "text": "2843"}, {"range": "3332", "text": "2841"}, {"range": "3333", "text": "2843"}, {"range": "3334", "text": "2841"}, {"range": "3335", "text": "2843"}, {"range": "3336", "text": "2841"}, {"range": "3337", "text": "2843"}, {"range": "3338", "text": "2841"}, {"range": "3339", "text": "2843"}, {"range": "3340", "text": "2841"}, {"range": "3341", "text": "2843"}, {"range": "3342", "text": "2841"}, {"range": "3343", "text": "2843"}, {"range": "3344", "text": "2841"}, {"range": "3345", "text": "2843"}, {"range": "3346", "text": "2841"}, {"range": "3347", "text": "2843"}, {"range": "3348", "text": "2841"}, {"range": "3349", "text": "2843"}, {"range": "3350", "text": "2841"}, {"range": "3351", "text": "2843"}, {"range": "3352", "text": "2841"}, {"range": "3353", "text": "2843"}, {"range": "3354", "text": "2841"}, {"range": "3355", "text": "2843"}, {"range": "3356", "text": "2841"}, {"range": "3357", "text": "2843"}, {"range": "3358", "text": "2841"}, {"range": "3359", "text": "2843"}, {"range": "3360", "text": "2841"}, {"range": "3361", "text": "2843"}, {"range": "3362", "text": "2841"}, {"range": "3363", "text": "2843"}, {"range": "3364", "text": "2841"}, {"range": "3365", "text": "2843"}, {"range": "3366", "text": "2841"}, {"range": "3367", "text": "2843"}, {"range": "3368", "text": "2841"}, {"range": "3369", "text": "2843"}, {"range": "3370", "text": "2841"}, {"range": "3371", "text": "2843"}, {"range": "3372", "text": "2841"}, {"range": "3373", "text": "2843"}, {"range": "3374", "text": "2841"}, {"range": "3375", "text": "2843"}, {"range": "3376", "text": "2841"}, {"range": "3377", "text": "2843"}, {"range": "3378", "text": "2841"}, {"range": "3379", "text": "2843"}, {"range": "3380", "text": "2841"}, {"range": "3381", "text": "2843"}, {"range": "3382", "text": "2841"}, {"range": "3383", "text": "2843"}, {"range": "3384", "text": "2841"}, {"range": "3385", "text": "2843"}, {"range": "3386", "text": "2841"}, {"range": "3387", "text": "2843"}, {"range": "3388", "text": "2841"}, {"range": "3389", "text": "2843"}, {"range": "3390", "text": "2841"}, {"range": "3391", "text": "2843"}, {"range": "3392", "text": "2841"}, {"range": "3393", "text": "2843"}, {"range": "3394", "text": "2841"}, {"range": "3395", "text": "2843"}, {"range": "3396", "text": "2841"}, {"range": "3397", "text": "2843"}, {"range": "3398", "text": "2841"}, {"range": "3399", "text": "2843"}, {"range": "3400", "text": "2841"}, {"range": "3401", "text": "2843"}, {"range": "3402", "text": "2841"}, {"range": "3403", "text": "2843"}, {"range": "3404", "text": "2841"}, {"range": "3405", "text": "2843"}, {"range": "3406", "text": "2841"}, {"range": "3407", "text": "2843"}, {"range": "3408", "text": "2841"}, {"range": "3409", "text": "2843"}, {"range": "3410", "text": "2841"}, {"range": "3411", "text": "2843"}, {"range": "3412", "text": "2841"}, {"range": "3413", "text": "2843"}, {"range": "3414", "text": "2841"}, {"range": "3415", "text": "2843"}, {"range": "3416", "text": "2841"}, {"range": "3417", "text": "2843"}, {"range": "3418", "text": "2841"}, {"range": "3419", "text": "2843"}, {"range": "3420", "text": "2841"}, {"range": "3421", "text": "2843"}, {"range": "3422", "text": "2841"}, {"range": "3423", "text": "2843"}, {"range": "3424", "text": "2841"}, {"range": "3425", "text": "2843"}, {"range": "3426", "text": "2841"}, {"range": "3427", "text": "2843"}, {"range": "3428", "text": "2841"}, {"range": "3429", "text": "2843"}, {"range": "3430", "text": "2841"}, {"range": "3431", "text": "2843"}, {"range": "3432", "text": "2841"}, {"range": "3433", "text": "2843"}, {"range": "3434", "text": "2841"}, {"range": "3435", "text": "2843"}, {"range": "3436", "text": "2841"}, {"range": "3437", "text": "2843"}, "&apos;", [2771, 2900], "\n              We&apos;ve sent a verification link to your email address. Please click the link to activate your account.\n            ", "&lsquo;", [2771, 2900], "\n              We&lsquo;ve sent a verification link to your email address. Please click the link to activate your account.\n            ", "&#39;", [2771, 2900], "\n              We&#39;ve sent a verification link to your email address. Please click the link to activate your account.\n            ", "&rsquo;", [2771, 2900], "\n              We&rsquo;ve sent a verification link to your email address. Please click the link to activate your account.\n            ", [2083, 2134], "\n              We&apos;ve sent a password reset link to ", [2083, 2134], "\n              We&lsquo;ve sent a password reset link to ", [2083, 2134], "\n              We&#39;ve sent a password reset link to ", [2083, 2134], "\n              We&rsquo;ve sent a password reset link to ", [2557, 2625], "\n                Didn&apos;t receive the email? Check your spam folder or", [2557, 2625], "\n                Didn&lsquo;t receive the email? Check your spam folder or", [2557, 2625], "\n                Didn&#39;t receive the email? Check your spam folder or", [2557, 2625], "\n                Didn&rsquo;t receive the email? Check your spam folder or", [3453, 3550], "\n            Enter your email address and we&apos;ll send you a link to reset your password\n          ", [3453, 3550], "\n            Enter your email address and we&lsquo;ll send you a link to reset your password\n          ", [3453, 3550], "\n            Enter your email address and we&#39;ll send you a link to reset your password\n          ", [3453, 3550], "\n            Enter your email address and we&rsquo;ll send you a link to reset your password\n          ", [6622, 6647], "\n            You&apos;ve used ", [6622, 6647], "\n            You&lsquo;ve used ", [6622, 6647], "\n            You&#39;ve used ", [6622, 6647], "\n            You&rsquo;ve used ", [5572, 5575], "unknown", [5572, 5575], "never", [777, 780], [777, 780], [1246, 1249], [1246, 1249], [1486, 1489], [1486, 1489], [1554, 1557], [1554, 1557], [1837, 1840], [1837, 1840], [1867, 1870], [1867, 1870], [1897, 1900], [1897, 1900], [1919, 1922], [1919, 1922], [1957, 1960], [1957, 1960], [2268, 2271], [2268, 2271], [719, 722], [719, 722], [12259, 12262], [12259, 12262], [12300, 12303], [12300, 12303], [2061, 2064], [2061, 2064], [3712, 3715], [3712, 3715], [5456, 5459], [5456, 5459], [6084, 6087], [6084, 6087], [6693, 6696], [6693, 6696], [7184, 7187], [7184, 7187], [7667, 7670], [7667, 7670], [8118, 8121], [8118, 8121], [8269, 8272], [8269, 8272], [8472, 8475], [8472, 8475], [9464, 9467], [9464, 9467], [9272, 9275], [9272, 9275], [9423, 9426], [9423, 9426], [7172, 7175], [7172, 7175], [7178, 7181], [7178, 7181], [7447, 7450], [7447, 7450], [9715, 9718], [9715, 9718], [1864, 1867], [1864, 1867], [2285, 2288], [2285, 2288], [2711, 2714], [2711, 2714], [3306, 3309], [3306, 3309], [3843, 3846], [3843, 3846], [4386, 4389], [4386, 4389], [4683, 4686], [4683, 4686], [4773, 4776], [4773, 4776], [5042, 5045], [5042, 5045], [5134, 5137], [5134, 5137], [5408, 5411], [5408, 5411], [5500, 5503], [5500, 5503], [5884, 5887], [5884, 5887], [5922, 5925], [5922, 5925], [5960, 5963], [5960, 5963], [5990, 5993], [5990, 5993], [6036, 6039], [6036, 6039], [6419, 6422], [6419, 6422], [6458, 6461], [6458, 6461], [6497, 6500], [6497, 6500], [6528, 6531], [6528, 6531], [6575, 6578], [6575, 6578], [6963, 6966], [6963, 6966], [7002, 7005], [7002, 7005], [7041, 7044], [7041, 7044], [7072, 7075], [7072, 7075], [7119, 7122], [7119, 7122], [7498, 7501], [7498, 7501], [7930, 7933], [7930, 7933], [8369, 8372], [8369, 8372], [12422, 12425], [12422, 12425], [234, 237], [234, 237], [1639, 1642], [1639, 1642], [2399, 2402], [2399, 2402], [2053, 2056], [2053, 2056], [2737, 2740], [2737, 2740], [3042, 3045], [3042, 3045], [3210, 3231], "[isOpen, side, align, updatePosition]", [2457, 2474], "[isVisible, side, updatePosition]", [2731, 2734], [2731, 2734], [2752, 2755], [2752, 2755], [2769, 2772], [2769, 2772], [2794, 2797], [2794, 2797], [2105, 2108], [2105, 2108], [2421, 2424], [2421, 2424], [2710, 2713], [2710, 2713], [1125, 1128], [1125, 1128], [1424, 1427], [1424, 1427], [1475, 1478], [1475, 1478], [1798, 1809], "[loadDashboardData, timeRange]", [6168, 6171], [6168, 6171], [10001, 10004], [10001, 10004], [10511, 10514], [10511, 10514], [8628, 8631], [8628, 8631], [9027, 9030], [9027, 9030], [3919, 4005], "\n              This page encountered an error and couldn&apos;t load properly.\n            ", [3919, 4005], "\n              This page encountered an error and couldn&lsquo;t load properly.\n            ", [3919, 4005], "\n              This page encountered an error and couldn&#39;t load properly.\n            ", [3919, 4005], "\n              This page encountered an error and couldn&rsquo;t load properly.\n            ", [7272, 7275], [7272, 7275], [1823, 1826], [1823, 1826], [2497, 2500], [2497, 2500], [3138, 3141], [3138, 3141], [4832, 4835], [4832, 4835], [3038, 3041], [3038, 3041], [548, 551], [548, 551], [671, 674], [671, 674], [4380, 4383], [4380, 4383], [11295, 11298], [11295, 11298], [11456, 11459], [11456, 11459], [307, 310], [307, 310], [346, 349], [346, 349], [4666, 4669], [4666, 4669], [687, 690], [687, 690], [839, 842], [839, 842], [836, 839], [836, 839], [1993, 1996], [1993, 1996], [2031, 2034], [2031, 2034], [2102, 2105], [2102, 2105], [3225, 3228], [3225, 3228], [3278, 3281], [3278, 3281], [3324, 3327], [3324, 3327], [5354, 5357], [5354, 5357], [5367, 5370], [5367, 5370], [462, 465], [462, 465], [1502, 1505], [1502, 1505], [1659, 1662], [1659, 1662], [1818, 1821], [1818, 1821], [1976, 1979], [1976, 1979], [2153, 2156], [2153, 2156], [3986, 3989], [3986, 3989], [4039, 4042], [4039, 4042], [4085, 4088], [4085, 4088], [6511, 6514], [6511, 6514], [6789, 6792], [6789, 6792], [7066, 7069], [7066, 7069], [7376, 7379], [7376, 7379], [7733, 7736], [7733, 7736], [8254, 8257], [8254, 8257], [10521, 10524], [10521, 10524], [994, 997], [994, 997], [1225, 1228], [1225, 1228], [6799, 6802], [6799, 6802], [8731, 8734], [8731, 8734], [6948, 6951], [6948, 6951], [7572, 7575], [7572, 7575], [8130, 8133], [8130, 8133], [8626, 8629], [8626, 8629], [10079, 10082], [10079, 10082], [10238, 10241], [10238, 10241], [10545, 10548], [10545, 10548], [518, 521], [518, 521], [672, 675], [672, 675], [3329, 3332], [3329, 3332], [3362, 3365], [3362, 3365], [3398, 3401], [3398, 3401], [4156, 4159], [4156, 4159], [7031, 7034], [7031, 7034], [8171, 8174], [8171, 8174], [8228, 8231], [8228, 8231], [8845, 8848], [8845, 8848], [1419, 1422], [1419, 1422], [1485, 1488], [1485, 1488], [2120, 2123], [2120, 2123], [3404, 3407], [3404, 3407], [1816, 1819], [1816, 1819], [2225, 2228], [2225, 2228], [2886, 2889], [2886, 2889], [3564, 3567], [3564, 3567], [4186, 4189], [4186, 4189], [446, 449], [446, 449], [1620, 1623], [1620, 1623], [1859, 1862], [1859, 1862], [2441, 2444], [2441, 2444], [2912, 2915], [2912, 2915], [3329, 3332], [3329, 3332], [4016, 4019], [4016, 4019], [4457, 4460], [4457, 4460], [1484, 1487], [1484, 1487], [3587, 3590], [3587, 3590], [3605, 3608], [3605, 3608], [5241, 5244], [5241, 5244], [6434, 6437], [6434, 6437], [6452, 6455], [6452, 6455], [8536, 8539], [8536, 8539], [8554, 8557], [8554, 8557], [10239, 10242], [10239, 10242], [10257, 10260], [10257, 10260], [11595, 11598], [11595, 11598], [11613, 11616], [11613, 11616], [12046, 12049], [12046, 12049], [12064, 12067], [12064, 12067], [1895, 1898], [1895, 1898], [1937, 1940], [1937, 1940], [3947, 3950], [3947, 3950], [8557, 8560], [8557, 8560], [8610, 8613], [8610, 8613], [10799, 10802], [10799, 10802], [11271, 11274], [11271, 11274], [11742, 11745], [11742, 11745], [12233, 12236], [12233, 12236], [12696, 12699], [12696, 12699], [13072, 13075], [13072, 13075], [1609, 1612], [1609, 1612], [1651, 1654], [1651, 1654], [4104, 4107], [4104, 4107], [4157, 4160], [4157, 4160], [8701, 8704], [8701, 8704], [9194, 9197], [9194, 9197], [9547, 9550], [9547, 9550], [9922, 9925], [9922, 9925], [10295, 10298], [10295, 10298], [10675, 10678], [10675, 10678], [11034, 11037], [11034, 11037], [11383, 11386], [11383, 11386], [11764, 11767], [11764, 11767], [1836, 1839], [1836, 1839], [1900, 1903], [1900, 1903], [1942, 1945], [1942, 1945], [3839, 3842], [3839, 3842], [4596, 4599], [4596, 4599], [4649, 4652], [4649, 4652], [5692, 5695], [5692, 5695], [6207, 6210], [6207, 6210], [6724, 6727], [6724, 6727], [7859, 7862], [7859, 7862], [7919, 7922], [7919, 7922], [8361, 8364], [8361, 8364], [8684, 8687], [8684, 8687], [8718, 8721], [8718, 8721], [8995, 8998], [8995, 8998], [9027, 9030], [9027, 9030], [9062, 9065], [9062, 9065], [9520, 9523], [9520, 9523], [9555, 9558], [9555, 9558], [9979, 9982], [9979, 9982], [10038, 10041], [10038, 10041], [10412, 10415], [10412, 10415], [10447, 10450], [10447, 10450], [1540, 1543], [1540, 1543], [2139, 2142], [2139, 2142], [2686, 2689], [2686, 2689], [2724, 2727], [2724, 2727], [3027, 3030], [3027, 3030], [4038, 4041], [4038, 4041], [6188, 6191], [6188, 6191], [8058, 8061], [8058, 8061], [9224, 9227], [9224, 9227], [3102, 3105], [3102, 3105], [3202, 3205], [3202, 3205], [7493, 7496], [7493, 7496], [7645, 7648], [7645, 7648], [7742, 7745], [7742, 7745], [6701, 6704], [6701, 6704], [6766, 6769], [6766, 6769], [6876, 6879], [6876, 6879], [8225, 8228], [8225, 8228], [8839, 8842], [8839, 8842], [1911, 1914], [1911, 1914], [1980, 1983], [1980, 1983], [2100, 2103], [2100, 2103], [2162, 2165], [2162, 2165], [2224, 2227], [2224, 2227], [6813, 6816], [6813, 6816], [6877, 6880], [6877, 6880], [8501, 8504], [8501, 8504], [8563, 8566], [8563, 8566], [10047, 10050], [10047, 10050], [10114, 10117], [10114, 10117], [11454, 11457], [11454, 11457], [11521, 11524], [11521, 11524], [12805, 12808], [12805, 12808], [12875, 12878], [12875, 12878], [13743, 13746], [13743, 13746], [13813, 13816], [13813, 13816], [15575, 15578], [15575, 15578], [15649, 15652], [15649, 15652], [15722, 15725], [15722, 15725], [16777, 16780], [16777, 16780], [17236, 17239], [17236, 17239], [17309, 17312], [17309, 17312], [17381, 17384], [17381, 17384], [18544, 18547], [18544, 18547], [18609, 18612], [18609, 18612], [22318, 22321], [22318, 22321], [22584, 22587], [22584, 22587], [24925, 24928], [24925, 24928], [12736, 12739], [12736, 12739], [18919, 18922], [18919, 18922], [26110, 26113], [26110, 26113], [31061, 31064], [31061, 31064], [2505, 2508], [2505, 2508], [9925, 9928], [9925, 9928], [10825, 10828], [10825, 10828], [11729, 11732], [11729, 11732], [2314, 2317], [2314, 2317], [1225, 1228], [1225, 1228], [1284, 1287], [1284, 1287], [6959, 6962], [6959, 6962], [8295, 8298], [8295, 8298], [8524, 8527], [8524, 8527], [8803, 8806], [8803, 8806], [9282, 9285], [9282, 9285], [1793, 1796], [1793, 1796], [6404, 6407], [6404, 6407], [8120, 8123], [8120, 8123], [8277, 8280], [8277, 8280]]