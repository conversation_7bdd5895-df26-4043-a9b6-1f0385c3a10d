import noun from './_noun.js'
import verb from './_verb.js'
// 'the pilot' vs 'pilot the plane'
const clue = {
  beforeTags: Object.assign({}, verb.beforeTags, noun.beforeTags, {
  }),
  afterTags: Object.assign({}, verb.afterTags, noun.afterTags, {}),
  beforeWords: Object.assign({}, verb.beforeWords, noun.beforeWords, {}),
  afterWords: Object.assign({}, verb.afterWords, noun.afterWords, {}),
}

export default clue