const v = 'Infinitive'

export default {
  beforeTags: {
    Modal: v, //would date
    Adverb: v, //quickly date
    Negative: v, //not date
    Plural: v, //characters drink
    // ProperNoun: vb,//google thought
  },
  afterTags: {
    Determiner: v, //flash the
    Adverb: v, //date quickly
    Possessive: v, //date his
    Reflexive: v, //resolve yourself
    // Noun:true, //date spencer
    Preposition: v, //date around, dump onto, grumble about
    // Conjunction: v, // dip to, dip through
    Cardinal: v, //cut 3 squares
    Comparative: v, //feel greater
    Superlative: v, //feel greatest
  },
  beforeWords: {
    i: v, //i date
    we: v, //we date
    you: v, //you date
    they: v, //they date
    to: v, //to date
    please: v, //please check
    will: v, //will check
    have: v,
    had: v,
    would: v,
    could: v,
    should: v,
    do: v,
    did: v,
    does: v,
    can: v,
    must: v,
    us: v,
    me: v,
    let: v,
    even: v,
    when: v,
    help: v, //help combat
    // them: v,
    he: v,
    she: v,
    it: v,
    being: v,
    // prefixes
    bi: v,
    co: v,
    contra: v,
    de: v,
    inter: v,
    intra: v,
    mis: v,
    pre: v,
    out: v,
    counter: v,
    nobody: v,
    somebody: v,
    anybody: v,
    everybody: v,
    // un: v,
    // over: v,
    // under: v,
  },
  afterWords: {
    the: v, //echo the
    me: v, //date me
    you: v, //date you
    him: v, //loves him
    us: v, //cost us
    her: v, //
    his: v, //
    them: v, //
    they: v, //
    it: v, //hope it
    himself: v,
    herself: v,
    itself: v,
    myself: v,
    ourselves: v,
    themselves: v,
    something: v,
    anything: v,

    a: v, //covers a
    an: v, //covers an
    // from: v, //ranges from
    up: v, //serves up
    down: v, //serves up
    by: v,
    // in: v, //bob in
    out: v,
    // on: v,
    off: v,
    under: v,
    what: v, //look what
    // when: v,//starts when
    // for:true, //settled for
    all: v, //shiver all night
    // conjunctions
    to: v, //dip to
    because: v, //
    although: v, //
    // after: v,
    // before: v,//
    how: v, //
    otherwise: v, //
    together: v, //fit together
    though: v, //
    into: v, //
    yet: v, //
    more: v, //kill more
    here: v, // look here
    there: v, //
    away: v, //float away
  },
}
