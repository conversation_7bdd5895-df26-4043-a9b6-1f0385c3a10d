!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).suffixThumb={})}(this,(function(e){"use strict";const t=function(e="",t={}){let n=function(e,t={}){return t.hasOwnProperty(e)?t[e]:null}(e,t.ex);return n=n||function(e,t=[]){for(let n=0;n<t.length;n+=1)if(e.endsWith(t[n]))return e;return null}(e,t.same),n=n||function(e,t,n={}){t=t||{};for(let r=e.length-1;r>=1;r-=1){let l=e.length-r,o=e.substring(l,e.length);if(!0===t.hasOwnProperty(o))return e.slice(0,l)+t[o];if(!0===n.hasOwnProperty(o))return e.slice(0,l)+n[o]}return t.hasOwnProperty("")?e+t[""]:n.hasOwnProperty("")?e+n[""]:null}(e,t.fwd,t.both),n=n||e,n},n=function(e){return Object.entries(e).reduce(((e,t)=>(e[t[1]]=t[0],e)),{})},r=function(e={}){return{reversed:!0,both:n(e.both),ex:n(e.ex),fwd:e.rev||{}}},l=function(e,t={}){let n=new Set,r=new Set;return e=e.filter((e=>!n.has(e[0])&&(!r.has(e[1])&&(n.add(e[0]),r.add(e[1]),!0))))},o=function(e,t=0){let n=[],[r,l]=e;for(let e=0;e<r.length&&r[e]===l[e];e+=1)n.push(r[e]);let o=n.length-t;return t>=n.length?null:{from:r.substring(o),to:l.substring(o)}},u=(e,t)=>{if(0===t)return 100;let n=e/t*100;return n=Math.round(10*n)/10,n},i=function(e,t){let n=0,r=new Set;return e?0===t.length?{total:n,percent:100,rule:e,clear:r,count:0}:(t.forEach((t=>{let l=function(e,t){if(t.from.length>=e.length)return null;if(e.endsWith(t.from)){let n=e.length-t.from.length;return e.slice(0,n)+t.to}return null}(t[0],e);null!==l&&(n+=1,l===t[1]&&r.add(t[0]))})),{total:n,count:r.size,percent:u(r.size,n),rule:e,clear:r}):{total:n,percent:0,rule:e,clear:r,count:0}},f=function(e,t,n){let r=e.slice(0),l={};for(let e=0;e<6;e+=1)for(let u=0;u<r.length;u+=1){let f=o(r[u],e),s=i(f,r);if(s.rule&&s.percent>n.threshold&&s.count>n.min){if(i(f,t).percent<100)continue;l[f.from]=f.to,r=r.filter((e=>!s.clear.has(e[0])||(t.push(e),!1)))}}return{rules:l,pending:r,finished:t}},s={threshold:80,min:0},c=e=>[e[1],e[0]];const h=function(e={}){let t={};return Object.keys(e).forEach((n=>{let r=function(e,t){let n=((e,t)=>{let n=[];for(let r=0;r<e.length&&e[r]===t[r];r+=1)n.push(e[r]);return n.join("")})(e,t);return n.length<1?t:n.length+t.substr(n.length)}(n,e[n]);t[n]=r})),function(e){let t={};Object.keys(e).forEach((n=>{let r=e[n];t[r]=t[r]||[],t[r].push(n)}));let n=[];return Object.keys(t).forEach((e=>{n.push(`${e}:${t[e].join(",")}`)})),n.join("¦")}(t)},a=/^([0-9]+)/,d=function(e){let t=function(e){let t={};return e.split("¦").forEach((e=>{let[n,r]=e.split(":");r=(r||"").split(","),r.forEach((e=>{t[e]=n}))})),t}(e);return Object.keys(t).reduce(((e,n)=>(e[n]=function(e="",t=""){let n=(t=String(t)).match(a);if(null===n)return t;let r=Number(n[1])||0;return e.substring(0,r)+t.replace(a,"")}(n,t[n]),e)),{})},p=e=>[e[1],e[0]],g=function(e,n){let r=0;return e.forEach((e=>{let l=t(e[0],n);l===e[1]?r+=1:console.log("❌ ",e,"→ "+l)})),((e,t)=>{let n=e/t*100;return n=Math.round(10*n)/10,n+"%"})(r,e.length)};e.compress=function(e){return{fwd:h(e.fwd),both:h(e.both),rev:h(e.rev),ex:h(e.ex)}},e.convert=t,e.learn=function(e,t={}){t=Object.assign({},s,t);let n={},r={};e=function(e,t){return(e=l(e)).filter((e=>{let[n,r]=e;return n.substring(0,1)===r.substring(0,1)||(t[n]=r,!1)}))}(e,n);let{rules:o,pending:u,finished:h}=f(e,[],t),{fwd:a,both:d,revPairs:p}=function(e,t,n){let r={},l=t.slice(0),o=[];return Object.entries(e).reverse().forEach((u=>{let f={from:u[1],to:u[0]};if(!f.to)return;let s=i(f,t);s.percent>n.threshold&&(r[f.to]=f.from,delete e[f.to],l=l.filter((e=>!s.clear.has(e[0])||(o.push(e),!1))))})),{fwd:e,both:r,revPairs:{pending:l,finished:o}}}(o,e.map(c),t),g=[];if(!1!==t.reverse){let e=f(p.pending,p.finished,t);g=e.pending,r=e.rules}return t.min<=1&&(u.forEach((e=>{n[e[0]]=e[1]})),g.forEach((e=>{n[e[1]]=e[0]}))),{fwd:a,both:d,rev:r,ex:n}},e.reverse=r,e.test=function(e,t={}){e=l(e);let n=g(e,t),o=g(e.map(p),r(t));var u;console.log(`${u=n,"[34m"+u+"[0m"}  -  🔄 ${(e=>"[36m"+e+"[0m")(o)}`)},e.uncompress=function(e={}){return"string"==typeof e&&(e=JSON.parse(e)),e.fwd=d(e.fwd||""),e.both=d(e.both||""),e.rev=d(e.rev||""),e.ex=d(e.ex||""),e},e.validate=l,Object.defineProperty(e,"__esModule",{value:!0})}));
