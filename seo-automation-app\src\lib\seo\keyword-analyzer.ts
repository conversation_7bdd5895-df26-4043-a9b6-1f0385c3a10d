import { countWords } from './word-count';
import { PorterStemmer, WordTokenizer } from 'natural';

export function calculateKeywordDensity(text: string, keyword: string): number {
  if (!text || !keyword) {
    return 0;
  }

  const words = text.toLowerCase().split(/\s+/);
  const keywordCount = words.filter(word => word.includes(keyword.toLowerCase())).length;

  return (keywordCount / words.length) * 100;
}

export function findKeywordVariations(text: string, keyword: string): string[] {
  if (!text || !keyword) {
    return [];
  }

  const tokenizer = new WordTokenizer();
  const words = tokenizer.tokenize(text.toLowerCase());
  const keywordStem = PorterStemmer.stem(keyword.toLowerCase());

  const variations = words?.filter(word => PorterStemmer.stem(word) === keywordStem);

  return [...new Set(variations)];
}

export function getKeywordDistribution(text: string, keyword: string): number[] {
  if (!text || !keyword) {
    return [];
  }

  const sentences = text.split(/[.!?]/);
  const keywordStem = PorterStemmer.stem(keyword.toLowerCase());

  return sentences.map(sentence => {
    const tokenizer = new WordTokenizer();
    const words = tokenizer.tokenize(sentence.toLowerCase());
    return words?.filter(word => PorterStemmer.stem(word) === keywordStem).length || 0;
  });
}

export function calculateKeywordProminence(text: string, keyword: string, headings: string[]): number {
  if (!text || !keyword) {
    return 0;
  }

  let score = 0;
  const keywordLower = keyword.toLowerCase();

  // Check title
  if (text.toLowerCase().startsWith(keywordLower)) {
    score += 10;
  }

  // Check headings
  headings.forEach(heading => {
    if (heading.toLowerCase().includes(keywordLower)) {
      score += 5;
    }
  });

  // Check first paragraph
  const firstParagraph = text.split('\n')[0];
  if (firstParagraph.toLowerCase().includes(keywordLower)) {
    score += 5;
  }

  return score;
}