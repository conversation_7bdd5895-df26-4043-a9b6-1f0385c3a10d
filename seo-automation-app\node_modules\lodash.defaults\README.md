# lodash.defaults v4.2.0

The [lodash](https://lodash.com/) method `_.defaults` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.defaults
```

In Node.js:
```js
var defaults = require('lodash.defaults');
```

See the [documentation](https://lodash.com/docs#defaults) or [package source](https://github.com/lodash/lodash/blob/4.2.0-npm-packages/lodash.defaults) for more details.
