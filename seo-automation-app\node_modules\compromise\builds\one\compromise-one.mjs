var t={methods:{one:{},two:{},three:{},four:{}},model:{one:{},two:{},three:{}},compute:{},hooks:[]};const e={compute:function(t){const{world:e}=this,n=e.compute;return"string"==typeof t&&n.hasOwnProperty(t)?n[t](this):(t=>"[object Array]"===Object.prototype.toString.call(t))(t)?t.forEach((r=>{e.compute.hasOwnProperty(r)?n[r](this):console.warn("no compute:",t)})):"function"==typeof t?t(this):console.warn("no compute:",t),this}};var n={forEach:function(t){return this.fullPointer.forEach(((e,n)=>{let r=this.update([e]);t(r,n)})),this},map:function(t,e){let n=this.fullPointer.map(((e,n)=>{let r=this.update([e]),o=t(r,n);return void 0===o?this.none():o}));if(0===n.length)return e||this.update([]);if(void 0!==n[0]){if("string"==typeof n[0])return n;if("object"==typeof n[0]&&(null===n[0]||!n[0].isView))return n}let r=[];return n.forEach((t=>{r=r.concat(t.fullPointer)})),this.toView(r)},filter:function(t){let e=this.fullPointer;return e=e.filter(((e,n)=>{let r=this.update([e]);return t(r,n)})),this.update(e)},find:function(t){let e=this.fullPointer.find(((e,n)=>{let r=this.update([e]);return t(r,n)}));return this.update([e])},some:function(t){return this.fullPointer.some(((e,n)=>{let r=this.update([e]);return t(r,n)}))},random:function(t=1){let e=this.fullPointer,n=Math.floor(Math.random()*e.length);return n+t>this.length&&(n=this.length-t,n=n<0?0:n),e=e.slice(n,n+t),this.update(e)}};const r={termList:function(){return this.methods.one.termList(this.docs)},terms:function(t){let e=this.match(".");return"number"==typeof t?e.eq(t):e},groups:function(t){if(t||0===t)return this.update(this._groups[t]||[]);let e={};return Object.keys(this._groups).forEach((t=>{e[t]=this.update(this._groups[t])})),e},eq:function(t){let e=this.pointer;return e||(e=this.docs.map(((t,e)=>[e]))),e[t]?this.update([e[t]]):this.none()},first:function(){return this.eq(0)},last:function(){let t=this.fullPointer.length-1;return this.eq(t)},firstTerms:function(){return this.match("^.")},lastTerms:function(){return this.match(".$")},slice:function(t,e){let n=this.pointer||this.docs.map(((t,e)=>[e]));return n=n.slice(t,e),this.update(n)},all:function(){return this.update().toView()},fullSentences:function(){let t=this.fullPointer.map((t=>[t[0]]));return this.update(t).toView()},none:function(){return this.update([])},isDoc:function(t){if(!t||!t.isView)return!1;let e=this.fullPointer,n=t.fullPointer;return!e.length!==n.length&&e.every(((t,e)=>!!n[e]&&(t[0]===n[e][0]&&t[1]===n[e][1]&&t[2]===n[e][2])))},wordCount:function(){return this.docs.reduce(((t,e)=>(t+=e.filter((t=>""!==t.text)).length,t)),0)},isFull:function(){let t=this.pointer;if(!t)return!0;if(0===t.length||0!==t[0][0])return!1;let e=0,n=0;return this.document.forEach((t=>e+=t.length)),this.docs.forEach((t=>n+=t.length)),e===n},getNth:function(t){return"number"==typeof t?this.eq(t):"string"==typeof t?this.if(t):this}};r.group=r.groups,r.fullSentence=r.fullSentences,r.sentence=r.fullSentences,r.lastTerm=r.lastTerms,r.firstTerm=r.firstTerms;const o=Object.assign({},r,e,n);o.get=o.eq;class View{constructor(e,n,r={}){[["document",e],["world",t],["_groups",r],["_cache",null],["viewType","View"]].forEach((t=>{Object.defineProperty(this,t[0],{value:t[1],writable:!0})})),this.ptrs=n}get docs(){let e=this.document;return this.ptrs&&(e=t.methods.one.getDoc(this.ptrs,this.document)),e}get pointer(){return this.ptrs}get methods(){return this.world.methods}get model(){return this.world.model}get hooks(){return this.world.hooks}get isView(){return!0}get found(){return this.docs.length>0}get length(){return this.docs.length}get fullPointer(){let{docs:t,ptrs:e,document:n}=this,r=e||t.map(((t,e)=>[e]));return r.map((t=>{let[e,r,o,i,s]=t;return r=r||0,o=o||(n[e]||[]).length,n[e]&&n[e][r]&&(i=i||n[e][r].id,n[e][o-1]&&(s=s||n[e][o-1].id)),[e,r,o,i,s]}))}update(t){let e=new View(this.document,t);if(this._cache&&t&&t.length>0){let n=[];t.forEach(((t,e)=>{let[r,o,i]=t;(1===t.length||0===o&&this.document[r].length===i)&&(n[e]=this._cache[r])})),n.length>0&&(e._cache=n)}return e.world=this.world,e}toView(t){return new View(this.document,t||this.pointer)}fromText(t){const{methods:e}=this;let n=e.one.tokenize.fromString(t,this.world),r=new View(n);return r.world=this.world,r.compute(["normal","freeze","lexicon"]),this.world.compute.preTagger&&r.compute("preTagger"),r.compute("unfreeze"),r}clone(){let t=this.document.slice(0);t=t.map((t=>t.map((t=>((t=Object.assign({},t)).tags=new Set(t.tags),t)))));let e=this.update(this.pointer);return e.document=t,e._cache=this._cache,e}}Object.assign(View.prototype,o);const i=function(t){return t&&"object"==typeof t&&!Array.isArray(t)};function s(t,e){if(i(e))for(const n in e)i(e[n])?(t[n]||Object.assign(t,{[n]:{}}),s(t[n],e[n])):Object.assign(t,{[n]:e[n]});return t}const l=function(t,e,n,r){const{methods:o,model:i,compute:l,hooks:u}=e;t.methods&&function(t,e){for(const n in e)t[n]=t[n]||{},Object.assign(t[n],e[n])}(o,t.methods),t.model&&s(i,t.model),t.irregulars&&function(t,e){let n=t.two.models||{};Object.keys(e).forEach((t=>{e[t].pastTense&&(n.toPast&&(n.toPast.ex[t]=e[t].pastTense),n.fromPast&&(n.fromPast.ex[e[t].pastTense]=t)),e[t].presentTense&&(n.toPresent&&(n.toPresent.ex[t]=e[t].presentTense),n.fromPresent&&(n.fromPresent.ex[e[t].presentTense]=t)),e[t].gerund&&(n.toGerund&&(n.toGerund.ex[t]=e[t].gerund),n.fromGerund&&(n.fromGerund.ex[e[t].gerund]=t)),e[t].comparative&&(n.toComparative&&(n.toComparative.ex[t]=e[t].comparative),n.fromComparative&&(n.fromComparative.ex[e[t].comparative]=t)),e[t].superlative&&(n.toSuperlative&&(n.toSuperlative.ex[t]=e[t].superlative),n.fromSuperlative&&(n.fromSuperlative.ex[e[t].superlative]=t))}))}(i,t.irregulars),t.compute&&Object.assign(l,t.compute),u&&(e.hooks=u.concat(t.hooks||[])),t.api&&t.api(n),t.lib&&Object.keys(t.lib).forEach((e=>r[e]=t.lib[e])),t.tags&&r.addTags(t.tags),t.words&&r.addWords(t.words),t.frozen&&r.addWords(t.frozen,!0),t.mutate&&t.mutate(e,r)},u=function(t){return"[object Array]"===Object.prototype.toString.call(t)},a=function(t,e,n){const{methods:r}=n;let o=new e([]);if(o.world=n,"number"==typeof t&&(t=String(t)),!t)return o;if("string"==typeof t){return new e(r.one.tokenize.fromString(t,n))}if(i=t,"[object Object]"===Object.prototype.toString.call(i)&&t.isView)return new e(t.document,t.ptrs);var i;if(u(t)){if(u(t[0])){let n=t.map((t=>t.map((t=>({text:t,normal:t,pre:"",post:" ",tags:new Set})))));return new e(n)}let n=t.map((t=>t.terms.map((t=>(u(t.tags)&&(t.tags=new Set(t.tags)),t)))));return new e(n)}return o};let c=Object.assign({},t);const h=function(t,e){e&&h.addWords(e);let n=a(t,View,c);return t&&n.compute(c.hooks),n};Object.defineProperty(h,"_world",{value:c,writable:!0}),h.tokenize=function(t,e){const{compute:n}=this._world;e&&h.addWords(e);let r=a(t,View,c);return n.contractions&&r.compute(["alias","normal","machine","contractions"]),r},h.plugin=function(t){return l(t,this._world,View,this),this},h.extend=h.plugin,h.world=function(){return this._world},h.model=function(){return this._world.model},h.methods=function(){return this._world.methods},h.hooks=function(){return this._world.hooks},h.verbose=function(t){const e="undefined"!=typeof process&&process.env?process.env:self.env||{};return e.DEBUG_TAGS="tagger"===t||!0===t||"",e.DEBUG_MATCH="match"===t||!0===t||"",e.DEBUG_CHUNKS="chunker"===t||!0===t||"",this},h.version="14.14.4";var f={one:{cacheDoc:function(t){let e=t.map((t=>{let e=new Set;return t.forEach((t=>{""!==t.normal&&e.add(t.normal),t.switch&&e.add(`%${t.switch}%`),t.implicit&&e.add(t.implicit),t.machine&&e.add(t.machine),t.root&&e.add(t.root),t.alias&&t.alias.forEach((t=>e.add(t)));let n=Array.from(t.tags);for(let t=0;t<n.length;t+=1)e.add("#"+n[t])})),e}));return e}}};const p={cache:function(){return this._cache=this.methods.one.cacheDoc(this.document),this},uncache:function(){return this._cache=null,this}};var d={api:function(t){Object.assign(t.prototype,p)},compute:{cache:function(t){t._cache=t.methods.one.cacheDoc(t.document)}},methods:f};const m=t=>/^\p{Lu}[\p{Ll}'’]/u.test(t)||/^\p{Lu}$/u.test(t),w=(t,e,n)=>{if(n.forEach((t=>t.dirty=!0)),t){let r=[e,0].concat(n);Array.prototype.splice.apply(t,r)}return t},b=function(t){let e=t[t.length-1];!e||/ $/.test(e.post)||/[-–—]/.test(e.post)||(e.post+=" ")},y=(t,e,n)=>{const r=/[-.?!,;:)–—'"]/g;let o=t[e-1];if(!o)return;let i=o.post;if(r.test(i)){let t=i.match(r).join(""),e=n[n.length-1];e.post=t+e.post,o.post=o.post.replace(r,"")}},v=function(t,e,n,r){let[o,i,s]=e;0===i||s===r[o].length?b(n):(b(n),b([t[e[1]]])),function(t,e,n){let r=t[e];if(0!==e||!m(r.text))return;n[0].text=n[0].text.replace(/^\p{Ll}/u,(t=>t.toUpperCase()));let o=t[e];o.tags.has("ProperNoun")||o.tags.has("Acronym")||m(o.text)&&o.text.length>1&&(o.text=(i=o.text,i.replace(/^\p{Lu}/u,(t=>t.toLowerCase()))));var i}(t,i,n),w(t,i,n)};let x=0;const j=t=>(t=t.length<3?"0"+t:t).length<3?"0"+t:t,E=function(t){let[e,n]=t.index||[0,0];x+=1,x=x>46655?0:x,e=e>46655?0:e,n=n>1294?0:n;let r=j(x.toString(36));r+=j(e.toString(36));let o=n.toString(36);return o=o.length<2?"0"+o:o,r+=o,r+=parseInt(36*Math.random(),10).toString(36),t.normal+"|"+r.toUpperCase()},O=function(t){if(t.has("@hasContraction")&&"function"==typeof t.contractions){t.grow("@hasContraction").contractions().expand()}},k=t=>"[object Array]"===Object.prototype.toString.call(t),P=function(t,e,n){const{document:r,world:o}=e;e.uncache();let i=e.fullPointer,s=e.fullPointer;e.forEach(((l,u)=>{let a=l.fullPointer[0],[c]=a,h=r[c],f=function(t,e){const{methods:n}=e;return"string"==typeof t?n.one.tokenize.fromString(t,e)[0]:"object"==typeof t&&t.isView?t.clone().docs[0]||[]:k(t)?k(t[0])?t[0]:t:[]}(t,o);0!==f.length&&(f=function(t){return t.map((t=>(t.id=E(t),t)))}(f),n?(O(e.update([a]).firstTerm()),v(h,a,f,r)):(O(e.update([a]).lastTerm()),function(t,e,n,r){let[o,,i]=e,s=(r[o]||[]).length;i<s?(y(t,i,n),b(n)):s===i&&(b(t),y(t,i,n),r[o+1]&&(n[n.length-1].post+=" ")),w(t,e[2],n),e[4]=n[n.length-1].id}(h,a,f,r)),r[c]&&r[c][a[1]]&&(a[3]=r[c][a[1]].id),s[u]=a,a[2]+=f.length,i[u]=a)}));let l=e.toView(i);return e.ptrs=s,l.compute(["id","index","freeze","lexicon"]),l.world.compute.preTagger&&l.compute("preTagger"),l.compute("unfreeze"),l},_={insertAfter:function(t){return P(t,this,!1)},insertBefore:function(t){return P(t,this,!0)}};_.append=_.insertAfter,_.prepend=_.insertBefore,_.insert=_.insertAfter;const S=/\$[0-9a-z]+/g,z={},A=t=>t.replace(/^\p{Ll}/u,(t=>t.toUpperCase())),$=t=>t.replace(/^\p{Lu}/u,(t=>t.toLowerCase()));z.replaceWith=function(t,e={}){let n=this.fullPointer,r=this;if(this.uncache(),"function"==typeof t)return function(t,e,n){return t.forEach((t=>{let r=e(t);t.replaceWith(r,n)})),t}(r,t,e);let o=r.docs[0];if(!o)return r;let i=e.possessives&&o[o.length-1].tags.has("Possessive"),s=e.case&&(l=o[0].text,/^\p{Lu}[\p{Ll}'’]/u.test(l)||/^\p{Lu}$/u.test(l));var l;t=function(t,e){if("string"!=typeof t)return t;let n=e.groups();return t=t.replace(S,(t=>{let e=t.replace(/\$/,"");return n.hasOwnProperty(e)?n[e].text():t})),t}(t,r);let u=this.update(n);n=n.map((t=>t.slice(0,3)));let a=(u.docs[0]||[]).map((t=>Array.from(t.tags))),c=u.docs[0][0].pre,h=u.docs[0][u.docs[0].length-1].post;if("string"==typeof t&&(t=this.fromText(t).compute("id")),r.insertAfter(t),u.has("@hasContraction")&&r.contractions){r.grow("@hasContraction+").contractions().expand()}if(r.delete(u),i){let t=r.docs[0],e=t[t.length-1];e.tags.has("Possessive")||(e.text+="'s",e.normal+="'s",e.tags.add("Possessive"))}if(c&&r.docs[0]&&(r.docs[0][0].pre=c),h&&r.docs[0]){let t=r.docs[0][r.docs[0].length-1];t.post.trim()||(t.post=h)}let f=r.toView(n).compute(["index","freeze","lexicon"]);if(f.world.compute.preTagger&&f.compute("preTagger"),f.compute("unfreeze"),e.tags&&f.terms().forEach(((t,e)=>{t.tagSafe(a[e])})),!f.docs[0]||!f.docs[0][0])return f;if(e.case){let t=s?A:$;f.docs[0][0].text=t(f.docs[0][0].text)}return f},z.replace=function(t,e,n){if(t&&!e)return this.replaceWith(t,n);let r=this.match(t);return r.found?(this.soften(),r.replaceWith(e,n)):this};const T={remove:function(t){const{indexN:e}=this.methods.one.pointer;this.uncache();let n=this.all(),r=this;t&&(n=this,r=this.match(t));let o=!n.ptrs;if(r.has("@hasContraction")&&r.contractions){r.grow("@hasContraction").contractions().expand()}let i=n.fullPointer,s=r.fullPointer.reverse(),l=function(t,e){e.forEach((e=>{let[n,r,o]=e,i=o-r;t[n]&&(o===t[n].length&&o>1&&function(t,e){let n=t.length-1,r=t[n],o=t[n-e];o&&r&&(o.post+=r.post,o.post=o.post.replace(/ +([.?!,;:])/,"$1"),o.post=o.post.replace(/[,;:]+([.?!])/,"$1"))}(t[n],i),t[n].splice(r,i))}));for(let e=t.length-1;e>=0;e-=1)if(0===t[e].length&&(t.splice(e,1),e===t.length&&t[e-1])){let n=t[e-1],r=n[n.length-1];r&&(r.post=r.post.trimEnd())}return t}(this.document,s);return i=function(t,e){return t=t.map((t=>{let[n]=t;return e[n]?(e[n].forEach((e=>{let n=e[2]-e[1];t[1]<=e[1]&&t[2]>=e[2]&&(t[2]-=n)})),t):t})),t.forEach(((e,n)=>{if(0===e[1]&&0==e[2])for(let e=n+1;e<t.length;e+=1)t[e][0]-=1,t[e][0]<0&&(t[e][0]=0)})),t=(t=t.filter((t=>t[2]-t[1]>0))).map((t=>(t[3]=null,t[4]=null,t)))}(i,e(s)),n.ptrs=i,n.document=l,n.compute("index"),o&&(n.ptrs=void 0),t?n.toView(i):(this.ptrs=[],n.none())}};T.delete=T.remove;const C={pre:function(t,e){return void 0===t&&this.found?this.docs[0][0].pre:(this.docs.forEach((n=>{let r=n[0];!0===e?r.pre+=t:r.pre=t})),this)},post:function(t,e){if(void 0===t){let t=this.docs[this.docs.length-1];return t[t.length-1].post}return this.docs.forEach((n=>{let r=n[n.length-1];!0===e?r.post+=t:r.post=t})),this},trim:function(){if(!this.found)return this;let t=this.docs,e=t[0][0];e.pre=e.pre.trimStart();let n=t[t.length-1],r=n[n.length-1];return r.post=r.post.trimEnd(),this},hyphenate:function(){return this.docs.forEach((t=>{t.forEach(((e,n)=>{0!==n&&(e.pre=""),t[n+1]&&(e.post="-")}))})),this},dehyphenate:function(){const t=/[-–—]/;return this.docs.forEach((e=>{e.forEach((e=>{t.test(e.post)&&(e.post=" ")}))})),this},toQuotations:function(t,e){return t=t||'"',e=e||'"',this.docs.forEach((n=>{n[0].pre=t+n[0].pre;let r=n[n.length-1];r.post=e+r.post})),this},toParentheses:function(t,e){return t=t||"(",e=e||")",this.docs.forEach((n=>{n[0].pre=t+n[0].pre;let r=n[n.length-1];r.post=e+r.post})),this}};C.deHyphenate=C.dehyphenate,C.toQuotation=C.toQuotations;var L={alpha:(t,e)=>t.normal<e.normal?-1:t.normal>e.normal?1:0,length:(t,e)=>{let n=t.normal.trim().length,r=e.normal.trim().length;return n<r?1:n>r?-1:0},wordCount:(t,e)=>t.words<e.words?1:t.words>e.words?-1:0,sequential:(t,e)=>t[0]<e[0]?1:t[0]>e[0]?-1:t[1]>e[1]?1:-1,byFreq:function(t){let e={};return t.forEach((t=>{e[t.normal]=e[t.normal]||0,e[t.normal]+=1})),t.sort(((t,n)=>{let r=e[t.normal],o=e[n.normal];return r<o?1:r>o?-1:0})),t}};const N=new Set(["index","sequence","seq","sequential","chron","chronological"]),V=new Set(["freq","frequency","topk","repeats"]),F=new Set(["alpha","alphabetical"]);var q={unique:function(){let t=new Set;return this.filter((e=>{let n=e.text("machine");return!t.has(n)&&(t.add(n),!0)}))},reverse:function(){let t=this.pointer||this.docs.map(((t,e)=>[e]));return t=[].concat(t),t=t.reverse(),this._cache&&(this._cache=this._cache.reverse()),this.update(t)},sort:function(t){let{docs:e,pointer:n}=this;if(this.uncache(),"function"==typeof t)return function(t,e){let n=t.fullPointer;return n=n.sort(((n,r)=>(n=t.update([n]),r=t.update([r]),e(n,r)))),t.ptrs=n,t}(this,t);t=t||"alpha";let r=n||e.map(((t,e)=>[e])),o=e.map(((t,e)=>({index:e,words:t.length,normal:t.map((t=>t.machine||t.normal||"")).join(" "),pointer:r[e]})));return N.has(t)&&(t="sequential"),F.has(t)&&(t="alpha"),V.has(t)?(o=L.byFreq(o),this.update(o.map((t=>t.pointer)))):"function"==typeof L[t]?(o=o.sort(L[t]),this.update(o.map((t=>t.pointer)))):this}};const G=function(t,e){if(t.length>0){let e=t[t.length-1],n=e[e.length-1];!1===/ /.test(n.post)&&(n.post+=" ")}return t=t.concat(e)};var D={concat:function(t){if("string"==typeof t){let e=this.fromText(t);if(this.found&&this.ptrs){let t=this.fullPointer,n=t[t.length-1][0];this.document.splice(n,0,...e.document)}else this.document=this.document.concat(e.document);return this.all().compute("index")}if("object"==typeof t&&t.isView)return function(t,e){if(t.document===e.document){let n=t.fullPointer.concat(e.fullPointer);return t.toView(n).compute("index")}return e.fullPointer.forEach((e=>{e[0]+=t.document.length})),t.document=G(t.document,e.docs),t.all()}(this,t);if(e=t,"[object Array]"===Object.prototype.toString.call(e)){let e=G(this.document,t);return this.document=e,this.all()}var e;return this}};var B={harden:function(){return this.ptrs=this.fullPointer,this},soften:function(){let t=this.ptrs;return!t||t.length<1||(t=t.map((t=>t.slice(0,3))),this.ptrs=t),this}};const M=Object.assign({},{toLowerCase:function(){return this.termList().forEach((t=>{t.text=t.text.toLowerCase()})),this},toUpperCase:function(){return this.termList().forEach((t=>{t.text=t.text.toUpperCase()})),this},toTitleCase:function(){return this.termList().forEach((t=>{t.text=t.text.replace(/^ *[a-z\u00C0-\u00FF]/,(t=>t.toUpperCase()))})),this},toCamelCase:function(){return this.docs.forEach((t=>{t.forEach(((e,n)=>{0!==n&&(e.text=e.text.replace(/^ *[a-z\u00C0-\u00FF]/,(t=>t.toUpperCase()))),n!==t.length-1&&(e.post="")}))})),this}},_,z,T,C,q,D,B),U={id:function(t){let e=t.docs;for(let t=0;t<e.length;t+=1)for(let n=0;n<e[t].length;n+=1){let r=e[t][n];r.id=r.id||E(r)}}};var W={api:function(t){Object.assign(t.prototype,M)},compute:U};const I=!0;var R={one:{contractions:[{word:"@",out:["at"]},{word:"arent",out:["are","not"]},{word:"alot",out:["a","lot"]},{word:"brb",out:["be","right","back"]},{word:"cannot",out:["can","not"]},{word:"dun",out:["do","not"]},{word:"can't",out:["can","not"]},{word:"shan't",out:["should","not"]},{word:"won't",out:["will","not"]},{word:"that's",out:["that","is"]},{word:"what's",out:["what","is"]},{word:"let's",out:["let","us"]},{word:"dunno",out:["do","not","know"]},{word:"gonna",out:["going","to"]},{word:"gotta",out:["have","got","to"]},{word:"gimme",out:["give","me"]},{word:"outta",out:["out","of"]},{word:"tryna",out:["trying","to"]},{word:"gtg",out:["got","to","go"]},{word:"im",out:["i","am"]},{word:"imma",out:["I","will"]},{word:"imo",out:["in","my","opinion"]},{word:"irl",out:["in","real","life"]},{word:"ive",out:["i","have"]},{word:"rn",out:["right","now"]},{word:"tbh",out:["to","be","honest"]},{word:"wanna",out:["want","to"]},{word:"c'mere",out:["come","here"]},{word:"c'mon",out:["come","on"]},{word:"shoulda",out:["should","have"]},{word:"coulda",out:["coulda","have"]},{word:"woulda",out:["woulda","have"]},{word:"musta",out:["must","have"]},{word:"tis",out:["it","is"]},{word:"twas",out:["it","was"]},{word:"y'know",out:["you","know"]},{word:"ne'er",out:["never"]},{word:"o'er",out:["over"]},{after:"ll",out:["will"]},{after:"ve",out:["have"]},{after:"re",out:["are"]},{after:"m",out:["am"]},{before:"c",out:["ce"]},{before:"m",out:["me"]},{before:"n",out:["ne"]},{before:"qu",out:["que"]},{before:"s",out:["se"]},{before:"t",out:["tu"]},{word:"shouldnt",out:["should","not"]},{word:"couldnt",out:["could","not"]},{word:"wouldnt",out:["would","not"]},{word:"hasnt",out:["has","not"]},{word:"wasnt",out:["was","not"]},{word:"isnt",out:["is","not"]},{word:"cant",out:["can","not"]},{word:"dont",out:["do","not"]},{word:"wont",out:["will","not"]},{word:"howd",out:["how","did"]},{word:"whatd",out:["what","did"]},{word:"whend",out:["when","did"]},{word:"whered",out:["where","did"]}],numberSuffixes:{st:I,nd:I,rd:I,th:I,am:I,pm:I,max:I,"°":I,s:I,e:I,er:I,"ère":I,"ème":I}}};const Q=function(t,e,n){let[r,o]=e;n&&0!==n.length&&(n=n.map(((t,e)=>(t.implicit=t.text,t.machine=t.text,t.pre="",t.post="",t.text="",t.normal="",t.index=[r,o+e],t))),n[0]&&(n[0].pre=t[r][o].pre,n[n.length-1].post=t[r][o].post,n[0].text=t[r][o].text,n[0].normal=t[r][o].normal),t[r].splice(o,1,...n))},H=/'/,Z=new Set(["what","how","when","where","why"]),K=new Set(["be","go","start","think","need"]),J=new Set(["been","gone"]),X=/'/,Y=/(e|é|aison|sion|tion)$/,tt=/(age|isme|acle|ege|oire)$/;var et=(t,e)=>["je",t[e].normal.split(X)[1]],nt=(t,e)=>{let n=t[e].normal.split(X)[1];return n&&n.endsWith("e")?["la",n]:["le",n]},rt=(t,e)=>{let n=t[e].normal.split(X)[1];return n&&Y.test(n)&&!tt.test(n)?["du",n]:n&&n.endsWith("s")?["des",n]:["de",n]};const ot=/^([0-9.]{1,4}[a-z]{0,2}) ?[-–—] ?([0-9]{1,4}[a-z]{0,2})$/i,it=/^([0-9]{1,2}(:[0-9][0-9])?(am|pm)?) ?[-–—] ?([0-9]{1,2}(:[0-9][0-9])?(am|pm)?)$/i,st=/^[0-9]{3}-[0-9]{4}$/,lt=function(t,e){let n=t[e],r=n.text.match(ot);return null!==r?!0===n.tags.has("PhoneNumber")||st.test(n.text)?null:[r[1],"to",r[2]]:(r=n.text.match(it),null!==r?[r[1],"to",r[4]]:null)},ut=/^([+-]?[0-9][.,0-9]*)([a-z°²³µ/]+)$/,at=function(t,e,n){const r=n.model.one.numberSuffixes||{};let o=t[e].text.match(ut);if(null!==o){let t=o[2].toLowerCase().trim();return r.hasOwnProperty(t)?null:[o[1],t]}return null},ct=/'/,ht=/^[0-9][^-–—]*[-–—].*?[0-9]/,ft=function(t,e,n,r){let o=e.update();o.document=[t];let i=n+r;n>0&&(n-=1),t[i]&&(i+=1),o.ptrs=[[0,n,i]]},pt={t:(t,e)=>function(t,e){return"ain't"===t[e].normal||"aint"===t[e].normal?null:[t[e].normal.replace(/n't/,""),"not"]}(t,e),d:(t,e)=>function(t,e){let n=t[e].normal.split(H)[0];if(Z.has(n))return[n,"did"];if(t[e+1]){if(J.has(t[e+1].normal))return[n,"had"];if(K.has(t[e+1].normal))return[n,"would"]}return null}(t,e)},dt={j:(t,e)=>et(t,e),l:(t,e)=>nt(t,e),d:(t,e)=>rt(t,e)},mt=function(t,e,n,r){for(let o=0;o<t.length;o+=1){let i=t[o];if(i.word===e.normal)return i.out;if(null!==r&&r===i.after)return[n].concat(i.out);if(null!==n&&n===i.before&&r&&r.length>2)return i.out.concat(r)}return null},gt=function(t,e){let n=e.fromText(t.join(" "));return n.compute(["id","alias"]),n.docs[0]},wt=function(t,e){for(let n=e+1;n<5&&t[n];n+=1)if("been"===t[n].normal)return["there","has"];return["there","is"]};var bt={contractions:t=>{let{world:e,document:n}=t;const{model:r,methods:o}=e;let i=r.one.contractions||[];n.forEach(((r,s)=>{for(let l=r.length-1;l>=0;l-=1){let u=null,a=null;if(!0===ct.test(r[l].normal)){let t=r[l].normal.split(ct);u=t[0],a=t[1]}let c=mt(i,r[l],u,a);!c&&pt.hasOwnProperty(a)&&(c=pt[a](r,l,e)),!c&&dt.hasOwnProperty(u)&&(c=dt[u](r,l)),"there"===u&&"s"===a&&(c=wt(r,l)),c?(c=gt(c,t),Q(n,[s,l],c),ft(n[s],t,l,c.length)):ht.test(r[l].normal)?(c=lt(r,l),c&&(c=gt(c,t),Q(n,[s,l],c),o.one.setTag(c,"NumberRange",e),c[2]&&c[2].tags.has("Time")&&o.one.setTag([c[0]],"Time",e,null,"time-range"),ft(n[s],t,l,c.length))):(c=at(r,l,e),c&&(c=gt(c,t),Q(n,[s,l],c),o.one.setTag([c[1]],"Unit",e,null,"contraction-unit")))}}))}};const yt={model:R,compute:bt,hooks:["contractions"]},vt=function(t){const e=t.world,{model:n,methods:r}=t.world,o=r.one.setTag,{frozenLex:i}=n.one,s=n.one._multiCache||{};t.docs.forEach((t=>{for(let n=0;n<t.length;n+=1){let r=t[n],l=r.machine||r.normal;if(void 0!==s[l]&&t[n+1]){for(let r=n+s[l]-1;r>n;r-=1){let s=t.slice(n,r+1),l=s.map((t=>t.machine||t.normal)).join(" ");!0!==i.hasOwnProperty(l)||(o(s,i[l],e,!1,"1-frozen-multi-lexicon"),s.forEach((t=>t.frozen=!0)))}}void 0!==i[l]&&i.hasOwnProperty(l)&&(o([r],i[l],e,!1,"1-freeze-lexicon"),r.frozen=!0)}}))};const xt=t=>"[34m"+t+"[0m",jt=t=>"[3m[2m"+t+"[0m",Et=function(t){t.docs.forEach((t=>{console.log(xt("\n  ┌─────────")),t.forEach((t=>{let e=`  ${jt("│")}  `,n=t.implicit||t.text||"-";!0===t.frozen?e+=`${xt(n)} ❄️`:e+=jt(n),console.log(e)}))}))};var Ot={compute:{frozen:vt,freeze:vt,unfreeze:function(t){return t.docs.forEach((t=>{t.forEach((t=>{delete t.frozen}))})),t}},mutate:t=>{const e=t.methods.one;e.termMethods.isFrozen=t=>!0===t.frozen,e.debug.freeze=Et,e.debug.frozen=Et},api:function(t){t.prototype.freeze=function(){return this.docs.forEach((t=>{t.forEach((t=>{t.frozen=!0}))})),this},t.prototype.unfreeze=function(){this.compute("unfreeze")},t.prototype.isFrozen=function(){return this.match("@isFrozen+")}},hooks:["freeze"]};const kt=function(t,e,n){const{model:r,methods:o}=n,i=o.one.setTag,s=r.one._multiCache||{},{lexicon:l}=r.one||{};let u=t[e],a=u.machine||u.normal;if(void 0!==s[a]&&t[e+1]){for(let r=e+s[a]-1;r>e;r-=1){let o=t.slice(e,r+1);if(o.length<=1)return!1;let s=o.map((t=>t.machine||t.normal)).join(" ");if(!0===l.hasOwnProperty(s)){let t=l[s];return i(o,t,n,!1,"1-multi-lexicon"),!t||2!==t.length||"PhrasalVerb"!==t[0]&&"PhrasalVerb"!==t[1]||i([o[1]],"Particle",n,!1,"1-phrasal-particle"),!0}}return!1}return null},Pt=/^(under|over|mis|re|un|dis|semi|pre|post)-?/,_t=new Set(["Verb","Infinitive","PastTense","Gerund","PresentTense","Adjective","Participle"]),St=function(t,e,n){const{model:r,methods:o}=n,i=o.one.setTag,{lexicon:s}=r.one;let l=t[e],u=l.machine||l.normal;if(void 0!==s[u]&&s.hasOwnProperty(u))return i([l],s[u],n,!1,"1-lexicon"),!0;if(l.alias){let t=l.alias.find((t=>s.hasOwnProperty(t)));if(t)return i([l],s[t],n,!1,"1-lexicon-alias"),!0}if(!0===Pt.test(u)){let t=u.replace(Pt,"");if(s.hasOwnProperty(t)&&t.length>3&&_t.has(s[t]))return i([l],s[t],n,!1,"1-lexicon-prefix"),!0}return null};var zt={lexicon:function(t){const e=t.world;t.docs.forEach((t=>{for(let n=0;n<t.length;n+=1)if(0===t[n].tags.size){let r=null;r=r||kt(t,n,e),r=r||St(t,n,e)}}))}};var At={one:{expandLexicon:function(t){let e={},n={};return Object.keys(t).forEach((r=>{let o=t[r],i=(r=(r=r.toLowerCase().trim()).replace(/'s\b/,"")).split(/ /);i.length>1&&(void 0===n[i[0]]||i.length>n[i[0]])&&(n[i[0]]=i.length),e[r]=e[r]||o})),delete e[""],delete e.null,delete e[" "],{lex:e,_multi:n}}}};var $t={addWords:function(t,e=!1){const n=this.world(),{methods:r,model:o}=n;if(!t)return;if(Object.keys(t).forEach((e=>{"string"==typeof t[e]&&t[e].startsWith("#")&&(t[e]=t[e].replace(/^#/,""))})),!0===e){let{lex:e,_multi:i}=r.one.expandLexicon(t,n);return Object.assign(o.one._multiCache,i),void Object.assign(o.one.frozenLex,e)}if(r.two.expandLexicon){let{lex:e,_multi:i}=r.two.expandLexicon(t,n);Object.assign(o.one.lexicon,e),Object.assign(o.one._multiCache,i)}let{lex:i,_multi:s}=r.one.expandLexicon(t,n);Object.assign(o.one.lexicon,i),Object.assign(o.one._multiCache,s)}};var Tt={model:{one:{lexicon:{},_multiCache:{},frozenLex:{}}},methods:At,compute:zt,lib:$t,hooks:["lexicon"]};const Ct=function(t,e){let n=[{}],r=[null],o=[0],i=[],s=0;t.forEach((function(t){let o=0,i=function(t,e){const{methods:n,model:r}=e;let o=n.one.tokenize.splitTerms(t,r).map((t=>n.one.tokenize.splitWhitespace(t,r)));return o.map((t=>t.text.toLowerCase()))}(t,e);for(let t=0;t<i.length;t++){let e=i[t];n[o]&&n[o].hasOwnProperty(e)?o=n[o][e]:(s++,n[o][e]=s,n[s]={},o=s,r[s]=null)}r[o]=[i.length]}));for(let t in n[0])s=n[0][t],o[s]=0,i.push(s);for(;i.length;){let t=i.shift(),e=Object.keys(n[t]);for(let l=0;l<e.length;l+=1){let u=e[l],a=n[t][u];for(i.push(a),s=o[t];s>0&&!n[s].hasOwnProperty(u);)s=o[s];if(n.hasOwnProperty(s)){let t=n[s][u];o[a]=t,r[t]&&(r[a]=r[a]||[],r[a]=r[a].concat(r[t]))}else o[a]=0}}return{goNext:n,endAs:r,failTo:o}},Lt=function(t,e,n){let r=0,o=[];for(let i=0;i<t.length;i++){let s=t[i][n.form]||t[i].normal;for(;r>0&&(void 0===e.goNext[r]||!e.goNext[r].hasOwnProperty(s));)r=e.failTo[r]||0;if(e.goNext[r].hasOwnProperty(s)&&(r=e.goNext[r][s],e.endAs[r])){let n=e.endAs[r];for(let e=0;e<n.length;e++){let r=n[e],s=t[i-r+1],[l,u]=s.index;o.push([l,u,u+r,s.id])}}}return o},Nt=function(t,e){for(let n=0;n<t.length;n+=1)if(!0===e.has(t[n]))return!1;return!0};const Vt=(t,e)=>{for(let n=t.length-1;n>=0;n-=1)if(t[n]!==e)return t=t.slice(0,n+1);return t},Ft={buildTrie:function(t){return function(t){return t.goNext=t.goNext.map((t=>{if(0!==Object.keys(t).length)return t})),t.goNext=Vt(t.goNext,void 0),t.failTo=Vt(t.failTo,0),t.endAs=Vt(t.endAs,null),t}(Ct(t,this.world()))}};Ft.compile=Ft.buildTrie;var qt={api:function(t){t.prototype.lookup=function(t,e={}){if(!t)return this.none();var n;"string"==typeof t&&(t=[t]);let r=function(t,e,n){let r=[];n.form=n.form||"normal";let o=t.docs;if(!e.goNext||!e.goNext[0])return console.error("Compromise invalid lookup trie"),t.none();let i=Object.keys(e.goNext[0]);for(let s=0;s<o.length;s++){if(t._cache&&t._cache[s]&&!0===Nt(i,t._cache[s]))continue;let l=o[s],u=Lt(l,e,n);u.length>0&&(r=r.concat(u))}return t.update(r)}(this,(n=t,"[object Object]"===Object.prototype.toString.call(n)?t:Ct(t,this.world)),e);return r=r.settle(),r}},lib:Ft};const Gt=function(t,e){return e?(t.forEach((t=>{let n=t[0];e[n]&&(t[0]=e[n][0],t[1]+=e[n][1],t[2]+=e[n][1])})),t):t},Dt=function(t,e){let{ptrs:n,byGroup:r}=t;return n=Gt(n,e),Object.keys(r).forEach((t=>{r[t]=Gt(r[t],e)})),{ptrs:n,byGroup:r}},Bt=function(t,e,n){const r=n.methods.one;return"number"==typeof t&&(t=String(t)),"string"==typeof t&&(t=r.killUnicode(t,n),t=r.parseMatch(t,e,n)),t},Mt=t=>"[object Object]"===Object.prototype.toString.call(t),Ut=t=>t&&Mt(t)&&!0===t.isView,Wt=t=>t&&Mt(t)&&!0===t.isNet;var It={matchOne:function(t,e,n){const r=this.methods.one;if(Ut(t))return this.intersection(t).eq(0);if(Wt(t))return this.sweep(t,{tagger:!1,matchOne:!0}).view;let o={regs:t=Bt(t,n,this.world),group:e,justOne:!0},i=r.match(this.docs,o,this._cache),{ptrs:s,byGroup:l}=Dt(i,this.fullPointer),u=this.toView(s);return u._groups=l,u},match:function(t,e,n){const r=this.methods.one;if(Ut(t))return this.intersection(t);if(Wt(t))return this.sweep(t,{tagger:!1}).view.settle();let o={regs:t=Bt(t,n,this.world),group:e},i=r.match(this.docs,o,this._cache),{ptrs:s,byGroup:l}=Dt(i,this.fullPointer),u=this.toView(s);return u._groups=l,u},has:function(t,e,n){const r=this.methods.one;if(Ut(t)){return this.intersection(t).fullPointer.length>0}if(Wt(t))return this.sweep(t,{tagger:!1}).view.found;let o={regs:t=Bt(t,n,this.world),group:e,justOne:!0};return r.match(this.docs,o,this._cache).ptrs.length>0},if:function(t,e,n){const r=this.methods.one;if(Ut(t))return this.filter((e=>e.intersection(t).found));if(Wt(t)){let e=this.sweep(t,{tagger:!1}).view.settle();return this.if(e)}let o={regs:t=Bt(t,n,this.world),group:e,justOne:!0},i=this.fullPointer,s=this._cache||[];i=i.filter(((t,e)=>{let n=this.update([t]);return r.match(n.docs,o,s[e]).ptrs.length>0}));let l=this.update(i);return this._cache&&(l._cache=i.map((t=>s[t[0]]))),l},ifNo:function(t,e,n){const{methods:r}=this,o=r.one;if(Ut(t))return this.filter((e=>!e.intersection(t).found));if(Wt(t)){let e=this.sweep(t,{tagger:!1}).view.settle();return this.ifNo(e)}t=Bt(t,n,this.world);let i=this._cache||[],s=this.filter(((n,r)=>{let s={regs:t,group:e,justOne:!0};return 0===o.match(n.docs,s,i[r]).ptrs.length}));return this._cache&&(s._cache=s.ptrs.map((t=>i[t[0]]))),s}};var Rt={before:function(t,e,n){const{indexN:r}=this.methods.one.pointer;let o=[],i=r(this.fullPointer);Object.keys(i).forEach((t=>{let e=i[t].sort(((t,e)=>t[1]>e[1]?1:-1))[0];e[1]>0&&o.push([e[0],0,e[1]])}));let s=this.toView(o);return t?s.match(t,e,n):s},after:function(t,e,n){const{indexN:r}=this.methods.one.pointer;let o=[],i=r(this.fullPointer),s=this.document;Object.keys(i).forEach((t=>{let e=i[t].sort(((t,e)=>t[1]>e[1]?-1:1))[0],[n,,r]=e;r<s[n].length&&o.push([n,r,s[n].length])}));let l=this.toView(o);return t?l.match(t,e,n):l},growLeft:function(t,e,n){"string"==typeof t&&(t=this.world.methods.one.parseMatch(t,n,this.world)),t[t.length-1].end=!0;let r=this.fullPointer;return this.forEach(((n,o)=>{let i=n.before(t,e);if(i.found){let t=i.terms();r[o][1]-=t.length,r[o][3]=t.docs[0][0].id}})),this.update(r)},growRight:function(t,e,n){"string"==typeof t&&(t=this.world.methods.one.parseMatch(t,n,this.world)),t[0].start=!0;let r=this.fullPointer;return this.forEach(((n,o)=>{let i=n.after(t,e);if(i.found){let t=i.terms();r[o][2]+=t.length,r[o][4]=null}})),this.update(r)},grow:function(t,e,n){return this.growRight(t,e,n).growLeft(t,e,n)}};const Qt=function(t,e){return[t[0],t[1],e[2]]},Ht=(t,e,n)=>{return"string"==typeof t||(r=t,"[object Array]"===Object.prototype.toString.call(r))?e.match(t,n):t||e.none();var r},Zt=function(t,e){let[n,r,o]=t;return e.document[n]&&e.document[n][r]&&(t[3]=t[3]||e.document[n][r].id,e.document[n][o-1]&&(t[4]=t[4]||e.document[n][o-1].id)),t},Kt={splitOn:function(t,e){const{splitAll:n}=this.methods.one.pointer;let r=Ht(t,this,e).fullPointer,o=n(this.fullPointer,r),i=[];return o.forEach((t=>{i.push(t.passthrough),i.push(t.before),i.push(t.match),i.push(t.after)})),i=i.filter((t=>t)),i=i.map((t=>Zt(t,this))),this.update(i)},splitBefore:function(t,e){const{splitAll:n}=this.methods.one.pointer;let r=Ht(t,this,e).fullPointer,o=n(this.fullPointer,r);for(let t=0;t<o.length;t+=1)!o[t].after&&o[t+1]&&o[t+1].before&&o[t].match&&o[t].match[0]===o[t+1].before[0]&&(o[t].after=o[t+1].before,delete o[t+1].before);let i=[];return o.forEach((t=>{i.push(t.passthrough),i.push(t.before),t.match&&t.after?i.push(Qt(t.match,t.after)):i.push(t.match)})),i=i.filter((t=>t)),i=i.map((t=>Zt(t,this))),this.update(i)},splitAfter:function(t,e){const{splitAll:n}=this.methods.one.pointer;let r=Ht(t,this,e).fullPointer,o=n(this.fullPointer,r),i=[];return o.forEach((t=>{i.push(t.passthrough),t.before&&t.match?i.push(Qt(t.before,t.match)):(i.push(t.before),i.push(t.match)),i.push(t.after)})),i=i.filter((t=>t)),i=i.map((t=>Zt(t,this))),this.update(i)}};Kt.split=Kt.splitAfter;const Jt=function(t,e){return!(!t||!e)&&(t[0]===e[0]&&t[2]===e[1])},Xt=function(t,e,n){const r=t.world,o=r.methods.one.parseMatch;n=n||"^.";let i=o(e=e||".$",{},r),s=o(n,{},r);i[i.length-1].end=!0,s[0].start=!0;let l=t.fullPointer,u=[l[0]];for(let e=1;e<l.length;e+=1){let n=u[u.length-1],r=l[e],o=t.update([n]),a=t.update([r]);Jt(n,r)&&o.has(i)&&a.has(s)?u[u.length-1]=[n[0],n[1],r[2],n[3],r[4]]:u.push(r)}return t.update(u)},Yt={joinIf:function(t,e){return Xt(this,t,e)},join:function(){return Xt(this)}},te=Object.assign({},It,Rt,Kt,Yt);te.lookBehind=te.before,te.lookBefore=te.before,te.lookAhead=te.after,te.lookAfter=te.after,te.notIf=te.ifNo;const ee=/(?:^|\s)([![^]*(?:<[^<]*>)?\/.*?[^\\/]\/[?\]+*$~]*)(?:\s|$)/,ne=/([!~[^]*(?:<[^<]*>)?\([^)]+[^\\)]\)[?\]+*$~]*)(?:\s|$)/,re=/ /g,oe=t=>/^[![^]*(<[^<]*>)?\//.test(t)&&/\/[?\]+*$~]*$/.test(t),ie=function(t){return t=(t=t.map((t=>t.trim()))).filter((t=>t))},se=/\{([0-9]+)?(, *[0-9]*)?\}/,le=/&&/,ue=new RegExp(/^<\s*(\S+)\s*>/),ae=t=>t.charAt(0).toUpperCase()+t.substring(1),ce=t=>t.charAt(t.length-1),he=t=>t.charAt(0),fe=t=>t.substring(1),pe=t=>t.substring(0,t.length-1),de=function(t){return t=fe(t),t=pe(t)},me=function(t,e){let n={};for(let r=0;r<2;r+=1){if("$"===ce(t)&&(n.end=!0,t=pe(t)),"^"===he(t)&&(n.start=!0,t=fe(t)),"?"===ce(t)&&(n.optional=!0,t=pe(t)),("["===he(t)||"]"===ce(t))&&(n.group=null,"["===he(t)&&(n.groupStart=!0),"]"===ce(t)&&(n.groupEnd=!0),t=(t=t.replace(/^\[/,"")).replace(/\]$/,""),"<"===he(t))){const e=ue.exec(t);e.length>=2&&(n.group=e[1],t=t.replace(e[0],""))}if("+"===ce(t)&&(n.greedy=!0,t=pe(t)),"*"!==t&&"*"===ce(t)&&"\\*"!==t&&(n.greedy=!0,t=pe(t)),"!"===he(t)&&(n.negative=!0,t=fe(t)),"~"===he(t)&&"~"===ce(t)&&t.length>2&&(t=de(t),n.fuzzy=!0,n.min=e.fuzzy||.85,!1===/\(/.test(t)))return n.word=t,n;if("/"===he(t)&&"/"===ce(t))return t=de(t),e.caseSensitive&&(n.use="text"),n.regex=new RegExp(t),n;if(!0===se.test(t)&&(t=t.replace(se,((t,e,r)=>(void 0===r?(n.min=Number(e),n.max=Number(e)):(r=r.replace(/, */,""),void 0===e?(n.min=0,n.max=Number(r)):(n.min=Number(e),n.max=Number(r||999))),n.greedy=!0,n.min||(n.optional=!0),"")))),"("===he(t)&&")"===ce(t)){le.test(t)?(n.choices=t.split(le),n.operator="and"):(n.choices=t.split("|"),n.operator="or"),n.choices[0]=fe(n.choices[0]);let r=n.choices.length-1;n.choices[r]=pe(n.choices[r]),n.choices=n.choices.map((t=>t.trim())),n.choices=n.choices.filter((t=>t)),n.choices=n.choices.map((t=>t.split(/ /g).map((t=>me(t,e))))),t=""}if("{"===he(t)&&"}"===ce(t)){if(t=de(t),n.root=t,/\//.test(t)){let t=n.root.split(/\//);n.root=t[0],n.pos=t[1],"adj"===n.pos&&(n.pos="Adjective"),n.pos=n.pos.charAt(0).toUpperCase()+n.pos.substr(1).toLowerCase(),void 0!==t[2]&&(n.sense=t[2])}return n}if("<"===he(t)&&">"===ce(t))return t=de(t),n.chunk=ae(t),n.greedy=!0,n;if("%"===he(t)&&"%"===ce(t))return t=de(t),n.switch=t,n}return"#"===he(t)?(n.tag=fe(t),n.tag=ae(n.tag),n):"@"===he(t)?(n.method=fe(t),n):"."===t?(n.anything=!0,n):"*"===t?(n.anything=!0,n.greedy=!0,n.optional=!0,n):(t&&(t=(t=t.replace("\\*","*")).replace("\\.","."),e.caseSensitive?n.use="text":t=t.toLowerCase(),n.word=t),n)},ge=/[a-z0-9][-–—][a-z]/i,we=function(t,e){let{all:n}=e.methods.two.transform.verb||{},r=t.root;return n?n(r,e.model):[]},be=function(t,e){let{all:n}=e.methods.two.transform.noun||{};return n?n(t.root,e.model):[t.root]},ye=function(t,e){let{all:n}=e.methods.two.transform.adjective||{};return n?n(t.root,e.model):[t.root]},ve=function(t){return t=function(t){let e=0,n=null;for(let r=0;r<t.length;r++){const o=t[r];!0===o.groupStart&&(n=o.group,null===n&&(n=String(e),e+=1)),null!==n&&(o.group=n),!0===o.groupEnd&&(n=null)}return t}(t),t=function(t){return t.map((t=>(t.fuzzy&&t.choices&&t.choices.forEach((e=>{1===e.length&&e[0].word&&(e[0].fuzzy=!0,e[0].min=t.min)})),t)))}(t=t.map((t=>{if(void 0!==t.choices){if("or"!==t.operator)return t;if(!0===t.fuzzy)return t;!0===t.choices.every((t=>{if(1!==t.length)return!1;let e=t[0];return!0!==e.fuzzy&&!e.start&&!e.end&&void 0!==e.word&&!0!==e.negative&&!0!==e.optional&&!0!==e.method}))&&(t.fastOr=new Set,t.choices.forEach((e=>{t.fastOr.add(e[0].word)})),delete t.choices)}return t}))),t},xe=function(t,e){for(let n of e)if(t.has(n))return!0;return!1},je=function(t,e){for(let n=0;n<t.length;n+=1){let r=t[n];if(!0!==r.optional&&!0!==r.negative&&!0!==r.fuzzy){if(void 0!==r.word&&!1===e.has(r.word))return!0;if(void 0!==r.tag&&!1===e.has("#"+r.tag))return!0;if(r.fastOr&&!1===xe(r.fastOr,e))return!1}}return!1},Ee=function(t,e,n=3){if(t===e)return 1;if(t.length<n||e.length<n)return 0;const r=function(t,e){let n=t.length,r=e.length;if(0===n)return r;if(0===r)return n;let o=(r>n?r:n)+1;if(Math.abs(n-r)>(o||100))return o||100;let i,s,l,u,a,c,h=[];for(let t=0;t<o;t++)h[t]=[t],h[t].length=o;for(let t=0;t<o;t++)h[0][t]=t;for(let o=1;o<=n;++o)for(s=t[o-1],i=1;i<=r;++i){if(o===i&&h[o][i]>4)return n;l=e[i-1],u=s===l?0:1,a=h[o-1][i]+1,(c=h[o][i-1]+1)<a&&(a=c),(c=h[o-1][i-1]+u)<a&&(a=c);let r=o>1&&i>1&&s===e[i-2]&&t[o-2]===l&&(c=h[o-2][i-2]+u)<a;h[o][i]=r?c:a}return h[n][r]}(t,e);let o=Math.max(t.length,e.length);return 1-(0===o?0:r/o)},Oe=/([\u0022\uFF02\u0027\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F])/,ke=/([\u0022\uFF02\u0027\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4])/,Pe=/^[-–—]$/,_e=/ [-–—]{1,3} /,Se=(t,e)=>-1!==t.post.indexOf(e),ze={hasQuote:t=>Oe.test(t.pre)||ke.test(t.post),hasComma:t=>Se(t,","),hasPeriod:t=>!0===Se(t,".")&&!1===Se(t,"..."),hasExclamation:t=>Se(t,"!"),hasQuestionMark:t=>Se(t,"?")||Se(t,"¿"),hasEllipses:t=>Se(t,"..")||Se(t,"…"),hasSemicolon:t=>Se(t,";"),hasColon:t=>Se(t,":"),hasSlash:t=>/\//.test(t.text),hasHyphen:t=>Pe.test(t.post)||Pe.test(t.pre),hasDash:t=>_e.test(t.post)||_e.test(t.pre),hasContraction:t=>Boolean(t.implicit),isAcronym:t=>t.tags.has("Acronym"),isKnown:t=>t.tags.size>0,isTitleCase:t=>/^\p{Lu}[a-z'\u00C0-\u00FF]/u.test(t.text),isUpperCase:t=>/^\p{Lu}+$/u.test(t.text)};ze.hasQuotation=ze.hasQuote;let Ae=function(){};Ae=function(t,e,n,r){let o=function(t,e,n,r){if(!0===e.anything)return!0;if(!0===e.start&&0!==n)return!1;if(!0===e.end&&n!==r-1)return!1;if(void 0!==e.id&&e.id===t.id)return!0;if(void 0!==e.word){if(e.use)return e.word===t[e.use];if(null!==t.machine&&t.machine===e.word)return!0;if(void 0!==t.alias&&t.alias.hasOwnProperty(e.word))return!0;if(!0===e.fuzzy){if(e.word===t.root)return!0;if(Ee(e.word,t.normal)>=e.min)return!0}return!(!t.alias||!t.alias.some((t=>t===e.word)))||e.word===t.text||e.word===t.normal}if(void 0!==e.tag)return!0===t.tags.has(e.tag);if(void 0!==e.method)return"function"==typeof ze[e.method]&&!0===ze[e.method](t);if(void 0!==e.pre)return t.pre&&t.pre.includes(e.pre);if(void 0!==e.post)return t.post&&t.post.includes(e.post);if(void 0!==e.regex){let n=t.normal;return e.use&&(n=t[e.use]),e.regex.test(n)}if(void 0!==e.chunk)return t.chunk===e.chunk;if(void 0!==e.switch)return t.switch===e.switch;if(void 0!==e.machine)return t.normal===e.machine||t.machine===e.machine||t.root===e.machine;if(void 0!==e.sense)return t.sense===e.sense;if(void 0!==e.fastOr){if(e.pos&&!t.tags.has(e.pos))return null;let n=t.root||t.implicit||t.machine||t.normal;return e.fastOr.has(n)||e.fastOr.has(t.text)}return void 0!==e.choices&&("and"===e.operator?e.choices.every((e=>Ae(t,e,n,r))):e.choices.some((e=>Ae(t,e,n,r))))}(t,e,n,r);return!0===e.negative?!o:o};const $e=function(t,e){if(!0===t.end&&!0===t.greedy&&e.start_i+e.t<e.phrase_length-1){let n=Object.assign({},t,{end:!1});if(!0===Ae(e.terms[e.t],n,e.start_i+e.t,e.phrase_length))return!0}return!1},Te=function(t,e){return t.groups[t.inGroup]||(t.groups[t.inGroup]={start:e,length:0}),t.groups[t.inGroup]},Ce=function(t){let{regs:e}=t,n=e[t.r],r=function(t,e){let n=t.t;if(!e)return t.terms.length;for(;n<t.terms.length;n+=1)if(!0===Ae(t.terms[n],e,t.start_i+n,t.phrase_length))return n;return null}(t,e[t.r+1]);if(null===r||0===r)return null;if(void 0!==n.min&&r-t.t<n.min)return null;if(void 0!==n.max&&r-t.t>n.max)return t.t=t.t+n.max,!0;if(!0===t.hasGroup){Te(t,t.t).length=r-t.t}return t.t=r,!0},Le=function(t,e=0){let n=t.regs[t.r],r=!1;for(let i=0;i<n.choices.length;i+=1){let s=n.choices[i];if(o=s,"[object Array]"!==Object.prototype.toString.call(o))return!1;if(r=s.every(((n,r)=>{let o=0,i=t.t+r+e+o;if(void 0===t.terms[i])return!1;let s=Ae(t.terms[i],n,i+t.start_i,t.phrase_length);if(!0===s&&!0===n.greedy)for(let e=1;e<t.terms.length;e+=1){let r=t.terms[i+e];if(r){if(!0!==Ae(r,n,t.start_i+e,t.phrase_length))break;o+=1}}return e+=o,s})),r){e+=s.length;break}}var o;return r&&!0===n.greedy?Le(t,e):e},Ne=function(t){const{regs:e}=t;let n=e[t.r],r=Le(t);if(r){if(!0===n.negative)return null;if(!0===t.hasGroup){Te(t,t.t).length+=r}if(!0===n.end){let e=t.phrase_length;if(t.t+t.start_i+r!==e)return null}return t.t+=r,!0}return!!n.optional||null},Ve=function(t){const{regs:e}=t;let n=e[t.r],r=function(t){let e=0;return!0===t.regs[t.r].choices.every((n=>{let r=n.every(((e,n)=>{let r=t.t+n;return void 0!==t.terms[r]&&Ae(t.terms[r],e,r,t.phrase_length)}));return!0===r&&n.length>e&&(e=n.length),r}))&&e}(t);if(r){if(!0===n.negative)return null;if(!0===t.hasGroup){Te(t,t.t).length+=r}if(!0===n.end){let e=t.phrase_length-1;if(t.t+t.start_i!==e)return null}return t.t+=r,!0}return!!n.optional||null},Fe=function(t){const{regs:e}=t;let n=e[t.r],r=Object.assign({},n);if(r.negative=!1,Ae(t.terms[t.t],r,t.start_i+t.t,t.phrase_length))return!1;if(n.optional){let n=e[t.r+1];if(n){if(Ae(t.terms[t.t],n,t.start_i+t.t,t.phrase_length))t.r+=1;else if(n.optional&&e[t.r+2]){Ae(t.terms[t.t],e[t.r+2],t.start_i+t.t,t.phrase_length)&&(t.r+=2)}}}return n.greedy?function(t,e,n){let r=0;for(let o=t.t;o<t.terms.length;o+=1){let i=Ae(t.terms[o],e,t.start_i+t.t,t.phrase_length);if(i)break;if(n&&(i=Ae(t.terms[o],n,t.start_i+t.t,t.phrase_length),i))break;if(r+=1,void 0!==e.max&&r===e.max)break}return!(0===r||e.min&&e.min>r||(t.t+=r,0))}(t,r,e[t.r+1]):(t.t+=1,!0)},qe=function(t){const{regs:e,phrase_length:n}=t;let r=e[t.r];return t.t=function(t,e){let n=Object.assign({},t.regs[t.r],{start:!1,end:!1}),r=t.t;for(;t.t<t.terms.length;t.t+=1){if(e&&Ae(t.terms[t.t],e,t.start_i+t.t,t.phrase_length))return t.t;let o=t.t-r+1;if(void 0!==n.max&&o===n.max)return t.t;if(!1===Ae(t.terms[t.t],n,t.start_i+t.t,t.phrase_length))return void 0!==n.min&&o<n.min?null:t.t}return t.t}(t,e[t.r+1]),null===t.t||r.min&&r.min>t.t?null:!0!==r.end||t.start_i+t.t===n||null},Ge=function(t){const{regs:e}=t;let n=e[t.r],r=t.terms[t.t],o=t.t;if(n.optional&&e[t.r+1]&&n.negative)return!0;if(n.optional&&e[t.r+1]&&function(t){const{regs:e}=t;let n=e[t.r],r=t.terms[t.t],o=Ae(r,e[t.r+1],t.start_i+t.t,t.phrase_length);if(n.negative||o){let n=t.terms[t.t+1];n&&Ae(n,e[t.r+1],t.start_i+t.t,t.phrase_length)||(t.r+=1)}}(t),r.implicit&&t.terms[t.t+1]&&function(t){let e=t.terms[t.t],n=t.regs[t.r];if(e.implicit&&t.terms[t.t+1]){if(!t.terms[t.t+1].implicit)return;n.word===e.normal&&(t.t+=1),"hasContraction"===n.method&&(t.t+=1)}}(t),t.t+=1,!0===n.end&&t.t!==t.terms.length&&!0!==n.greedy)return null;if(!0===n.greedy){if(!qe(t))return null}return!0===t.hasGroup&&function(t,e){let n=t.regs[t.r];const r=Te(t,e);t.t>1&&n.greedy?r.length+=t.t-e:r.length++}(t,o),!0},De=function(t,e,n,r){if(0===t.length||0===e.length)return null;let o={t:0,terms:t,r:0,regs:e,groups:{},start_i:n,phrase_length:r,inGroup:null};for(;o.r<e.length;o.r+=1){let t=e[o.r];if(o.hasGroup=Boolean(t.group),!0===o.hasGroup?o.inGroup=t.group:o.inGroup=null,!o.terms[o.t]){if(!1===e.slice(o.r).some((t=>!t.optional)))break;return null}if(!0!==t.anything||!0!==t.greedy)if(void 0===t.choices||"or"!==t.operator)if(void 0===t.choices||"and"!==t.operator)if(!0!==t.anything)if(!0!==$e(t,o))if(t.negative){if(!Fe(o))return null}else if(!0!==Ae(o.terms[o.t],t,o.start_i+o.t,o.phrase_length)){if(!0!==t.optional)return null}else{if(!Ge(o))return null}else{if(!Ge(o))return null}else{if(t.negative&&t.anything)return null;if(!Ge(o))return null}else{if(!Ve(o))return null}else{if(!Ne(o))return null}else{if(!Ce(o))return null}}let i=[null,n,o.t+n];if(i[1]===i[2])return null;let s={};return Object.keys(o.groups).forEach((t=>{let e=o.groups[t],r=n+e.start;s[t]=[null,r,r+e.length]})),{pointer:i,groups:s}},Be=function(t,e){return t.pointer[0]=e,Object.keys(t.groups).forEach((n=>{t.groups[n][0]=e})),t},Me=function(t,e,n){let r=De(t,e,0,t.length);return r?(r=Be(r,n),r):null},Ue={one:{termMethods:ze,parseMatch:function(t,e,n){if(null==t||""===t)return[];e=e||{},"number"==typeof t&&(t=String(t));let r=function(t){let e=t.split(ee),n=[];e.forEach((t=>{oe(t)?n.push(t):n=n.concat(t.split(ne))})),n=ie(n);let r=[];return n.forEach((t=>{(t=>/^[![^]*(<[^<]*>)?\(/.test(t)&&/\)[?\]+*$~]*$/.test(t))(t)||oe(t)?r.push(t):r=r.concat(t.split(re))})),r=ie(r),r}(t);return r=r.map((t=>me(t,e))),r=function(t,e){let n=e.model.one.prefixes;for(let e=t.length-1;e>=0;e-=1){let r=t[e];if(r.word&&ge.test(r.word)){let o=r.word.split(/[-–—]/g);if(n.hasOwnProperty(o[0]))continue;o=o.filter((t=>t)).reverse(),t.splice(e,1),o.forEach((n=>{let o=Object.assign({},r);o.word=n,t.splice(e,0,o)}))}}return t}(r,n),r=function(t,e){return t.map((t=>{if(t.root)if(e.methods.two&&e.methods.two.transform){let n=[];t.pos?"Verb"===t.pos?n=n.concat(we(t,e)):"Noun"===t.pos?n=n.concat(be(t,e)):"Adjective"===t.pos&&(n=n.concat(ye(t,e))):(n=n.concat(we(t,e)),n=n.concat(be(t,e)),n=n.concat(ye(t,e))),n=n.filter((t=>t)),n.length>0&&(t.operator="or",t.fastOr=new Set(n))}else t.machine=t.root,delete t.id,delete t.root;return t}))}(r,n),r=ve(r),r},match:function(t,e,n){n=n||[];let{regs:r,group:o,justOne:i}=e,s=[];if(!r||0===r.length)return{ptrs:[],byGroup:{}};const l=r.filter((t=>!0!==t.optional&&!0!==t.negative)).length;t:for(let e=0;e<t.length;e+=1){let o=t[e];if(!n[e]||!je(r,n[e]))if(!0!==r[0].start)for(let t=0;t<o.length;t+=1){let n=o.slice(t);if(n.length<l)break;let u=De(n,r,t,o.length);if(u){if(u=Be(u,e),s.push(u),!0===i)break t;let n=u.pointer[2];Math.abs(n-1)>t&&(t=Math.abs(n-1))}}else{let t=Me(o,r,e);t&&s.push(t)}}return!0===r[r.length-1].end&&(s=s.filter((e=>{let n=e.pointer[0];return t[n].length===e.pointer[2]}))),e.notIf&&(s=function(t,e,n){return t=t.filter((t=>{let[r,o,i]=t.pointer,s=n[r].slice(o,i);for(let t=0;t<s.length;t+=1){let n=s.slice(t);if(null!==De(n,e,t,s.length))return!1}return!0})),t}(s,e.notIf,t)),s=function(t,e){let n=[],r={};return 0===t.length||("number"==typeof e&&(e=String(e)),e?t.forEach((t=>{t.groups[e]&&n.push(t.groups[e])})):t.forEach((t=>{n.push(t.pointer),Object.keys(t.groups).forEach((e=>{r[e]=r[e]||[],r[e].push(t.groups[e])}))}))),{ptrs:n,byGroup:r}}(s,o),s.ptrs.forEach((e=>{let[n,r,o]=e;e[3]=t[n][r].id,e[4]=t[n][o-1].id})),s}}};var We={api:function(t){Object.assign(t.prototype,te)},methods:Ue,lib:{parseMatch:function(t,e){const n=this.world();let r=n.methods.one.killUnicode;return r&&(t=r(t,n)),n.methods.one.parseMatch(t,e,n)}}};const Ie=/^\../,Re=/^#./,Qe=function(t,e){let n={},r={};return Object.keys(e).forEach((o=>{let i=e[o],s=function(t){let e="",n="</span>";return t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"),Ie.test(t)?e=`<span class="${t.replace(/^\./,"")}"`:Re.test(t)?e=`<span id="${t.replace(/^#/,"")}"`:(e=`<${t}`,n=`</${t}>`),e+=">",{start:e,end:n}}(o);"string"==typeof i&&(i=t.match(i)),i.docs.forEach((t=>{if(t.every((t=>t.implicit)))return;let e=t[0].id;n[e]=n[e]||[],n[e].push(s.start);let o=t[t.length-1].id;r[o]=r[o]||[],r[o].push(s.end)}))})),{starts:n,ends:r}};var He={html:function(t){let{starts:e,ends:n}=Qe(this,t),r="";return this.docs.forEach((t=>{for(let o=0;o<t.length;o+=1){let i=t[o];e.hasOwnProperty(i.id)&&(r+=e[i.id].join("")),r+=i.pre||"",r+=i.text||"",n.hasOwnProperty(i.id)&&(r+=n[i.id].join("")),r+=i.post||""}})),r}};const Ze=/[,:;)\]*.?~!\u0022\uFF02\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4—-]+$/,Ke=/^[(['"*~\uFF02\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F]+/,Je=/[,:;)('"\u201D\]]/,Xe=/^[-–—]$/,Ye=/ /,tn=function(t,e,n=!0){let r="";return t.forEach((t=>{let n=t.pre||"",o=t.post||"";"some"===e.punctuation&&(n=n.replace(Ke,""),Xe.test(o)&&(o=" "),o=o.replace(Je,""),o=o.replace(/\?!+/,"?"),o=o.replace(/!+/,"!"),o=o.replace(/\?+/,"?"),o=o.replace(/\.{2,}/,""),t.tags.has("Abbreviation")&&(o=o.replace(/\./,""))),"some"===e.whitespace&&(n=n.replace(/\s/,""),o=o.replace(/\s+/," ")),e.keepPunct||(n=n.replace(Ke,""),o="-"===o?" ":o.replace(Ze,""));let i=t[e.form||"text"]||t.normal||"";"implicit"===e.form&&(i=t.implicit||t.text),"root"===e.form&&t.implicit&&(i=t.root||t.implicit||t.normal),"machine"!==e.form&&"implicit"!==e.form&&"root"!==e.form||!t.implicit||o&&Ye.test(o)||(o+=" "),r+=n+i+o})),!1===n&&(r=r.trim()),!0===e.lowerCase&&(r=r.toLowerCase()),r},en={text:{form:"text"},normal:{whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"normal"},machine:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"none",unicode:"some",form:"machine"},root:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"root"},implicit:{form:"implicit"}};en.clean=en.normal,en.reduced=en.root;let nn=[],rn=0;for(;rn<64;)nn[rn]=0|4294967296*Math.sin(++rn%Math.PI);const on=function(t){let e,n,r,o=[e=1732584193,n=4023233417,~e,~n],i=[],s=decodeURI(encodeURI(t))+"",l=s.length;for(t=--l/4+2|15,i[--t]=8*l;~l;)i[l>>2]|=s.charCodeAt(l)<<8*l--;for(rn=s=0;rn<t;rn+=16){for(l=o;s<64;l=[r=l[3],e+((r=l[0]+[e&n|~e&r,r&e|~r&n,e^n^r,n^(e|~r)][l=s>>4]+nn[s]+~~i[rn|15&[s,5*s+1,3*s+5,7*s][l]])<<(l=[7,12,17,22,5,9,14,20,4,11,16,23,6,10,15,21][4*l+s++%4])|r>>>-l),e,n])e=0|l[1],n=l[2];for(s=4;s;)o[--s]+=l[s]}for(t="";s<32;)t+=(o[s>>3]>>4*(1^s++)&15).toString(16);return t},sn={text:!0,terms:!0};let ln={case:"none",unicode:"some",form:"machine",punctuation:"some"};const un=function(t,e){return Object.assign({},t,e)},an={text:t=>tn(t,{keepPunct:!0},!1),normal:t=>tn(t,un(en.normal,{keepPunct:!0}),!1),implicit:t=>tn(t,un(en.implicit,{keepPunct:!0}),!1),machine:t=>tn(t,ln,!1),root:t=>tn(t,un(ln,{form:"root"}),!1),hash:t=>on(tn(t,{keepPunct:!0},!1)),offset:t=>{let e=an.text(t).length;return{index:t[0].offset.index,start:t[0].offset.start,length:e}},terms:t=>t.map((t=>{let e=Object.assign({},t);return e.tags=Array.from(t.tags),e})),confidence:(t,e,n)=>e.eq(n).confidence(),syllables:(t,e,n)=>e.eq(n).syllables(),sentence:(t,e,n)=>e.eq(n).fullSentence().text(),dirty:t=>t.some((t=>!0===t.dirty))};an.sentences=an.sentence,an.clean=an.normal,an.reduced=an.root;const cn={json:function(t){let e=(n=this,"string"==typeof(r=(r=t)||{})&&(r={}),(r=Object.assign({},sn,r)).offset&&n.compute("offset"),n.docs.map(((t,e)=>{let o={};return Object.keys(r).forEach((i=>{r[i]&&an[i]&&(o[i]=an[i](t,n,e))})),o})));var n,r;return"number"==typeof t?e[t]:e}};cn.data=cn.json;const hn=function(t){let e=t.pre||"",n=t.post||"";return e+t.text+n},fn=function(t,e){let n=function(t,e){let n={};return Object.keys(e).forEach((r=>{t.match(r).fullPointer.forEach((t=>{n[t[3]]={fn:e[r],end:t[2]}}))})),n}(t,e),r="";return t.docs.forEach(((e,o)=>{for(let i=0;i<e.length;i+=1){let s=e[i];if(n.hasOwnProperty(s.id)){let{fn:l,end:u}=n[s.id],a=t.update([[o,i,u]]);r+=e[i].pre||"",r+=l(a),i=u-1,r+=e[i].post||""}else r+=hn(s)}})),r},pn={debug:function(t){let e=this.methods.one.debug||{};return t&&e.hasOwnProperty(t)?(e[t](this),this):"undefined"!=typeof window&&window.document?(e.clientSide(this),this):(e.tags(this),this)},out:function(t){if(e=t,"[object Object]"===Object.prototype.toString.call(e))return fn(this,t);var e;if("text"===t)return this.text();if("normal"===t)return this.text("normal");if("root"===t)return this.text("root");if("machine"===t||"reduced"===t)return this.text("machine");if("hash"===t||"md5"===t)return on(this.text());if("json"===t)return this.json();if("offset"===t||"offsets"===t)return this.compute("offset"),this.json({offset:!0});if("array"===t){let t=this.docs.map((t=>t.reduce(((t,e)=>t+e.pre+e.text+e.post),"").trim()));return t.filter((t=>t))}if("freq"===t||"frequency"===t||"topk"===t)return function(t){let e={};t.forEach((t=>{e[t]=e[t]||0,e[t]+=1}));let n=Object.keys(e).map((t=>({normal:t,count:e[t]})));return n.sort(((t,e)=>t.count>e.count?-1:0))}(this.json({normal:!0}).map((t=>t.normal)));if("terms"===t){let t=[];return this.docs.forEach((e=>{let n=e.map((t=>t.text));n=n.filter((t=>t)),t=t.concat(n)})),t}return"tags"===t?this.docs.map((t=>t.reduce(((t,e)=>(t[e.implicit||e.normal]=Array.from(e.tags),t)),{}))):"debug"===t?this.debug():this.text()},wrap:function(t){return fn(this,t)}};var dn={text:function(t){let e={};var n;if(t&&"string"==typeof t&&en.hasOwnProperty(t)?e=Object.assign({},en[t]):t&&(n=t,"[object Object]"===Object.prototype.toString.call(n))&&(e=Object.assign({},t)),void 0!==e.keepSpace||this.isFull()||(e.keepSpace=!1),void 0===e.keepEndPunct&&this.pointer){let t=this.pointer[0];t&&t[1]?e.keepEndPunct=!1:e.keepEndPunct=!0}return void 0===e.keepPunct&&(e.keepPunct=!0),void 0===e.keepSpace&&(e.keepSpace=!0),function(t,e){let n="";if(!t||!t[0]||!t[0][0])return n;for(let r=0;r<t.length;r+=1)n+=tn(t[r],e,!0);if(e.keepSpace||(n=n.trim()),!1===e.keepEndPunct){t[0][0].tags.has("Emoticon")||(n=n.replace(Ke,""));let e=t[t.length-1];e[e.length-1].tags.has("Emoticon")||(n=n.replace(Ze,"")),n.endsWith("'")&&!n.endsWith("s'")&&(n=n.replace(/'/,""))}return!0===e.cleanWhitespace&&(n=n.trim()),n}(this.docs,e)}};const mn=Object.assign({},pn,dn,cn,He),gn="[0m",wn={green:t=>"[32m"+t+gn,red:t=>"[31m"+t+gn,blue:t=>"[34m"+t+gn,magenta:t=>"[35m"+t+gn,cyan:t=>"[36m"+t+gn,yellow:t=>"[33m"+t+gn,black:t=>"[30m"+t+gn,dim:t=>"[2m"+t+gn,i:t=>"[3m"+t+gn},bn={tags:function(t){let{docs:e,model:n}=t;0===e.length&&console.log(wn.blue("\n     ──────")),e.forEach((e=>{console.log(wn.blue("\n  ┌─────────")),e.forEach((e=>{let r=[...e.tags||[]],o=e.text||"-";e.sense&&(o=`{${e.normal}/${e.sense}}`),e.implicit&&(o="["+e.implicit+"]"),o=wn.yellow(o);let i="'"+o+"'";if(e.reference){let n=t.update([e.reference]).text("normal");i+=` - ${wn.dim(wn.i("["+n+"]"))}`}i=i.padEnd(18);let s=wn.blue("  │ ")+wn.i(i)+"  - "+function(t,e){return e.one.tagSet&&(t=t.map((t=>{if(!e.one.tagSet.hasOwnProperty(t))return t;const n=e.one.tagSet[t].color||"blue";return wn[n](t)}))),t.join(", ")}(r,n);console.log(s)}))})),console.log("\n")},clientSide:function(t){console.log("%c -=-=- ","background-color:#6699cc;"),t.forEach((t=>{console.groupCollapsed(t.text());let e=t.docs[0].map((t=>{let e=t.text||"-";return t.implicit&&(e="["+t.implicit+"]"),{text:e,tags:"["+Array.from(t.tags).join(", ")+"]"}}));console.table(e,["text","tags"]),console.groupEnd()}))},chunks:function(t){let{docs:e}=t;console.log(""),e.forEach((t=>{let e=[];t.forEach((t=>{"Noun"===t.chunk?e.push(wn.blue(t.implicit||t.normal)):"Verb"===t.chunk?e.push(wn.green(t.implicit||t.normal)):"Adjective"===t.chunk?e.push(wn.yellow(t.implicit||t.normal)):"Pivot"===t.chunk?e.push(wn.red(t.implicit||t.normal)):e.push(t.implicit||t.normal)})),console.log(e.join(" "),"\n")})),console.log("\n")},highlight:function(t){if(!t.found)return;let e={};t.fullPointer.forEach((t=>{e[t[0]]=e[t[0]]||[],e[t[0]].push(t)})),Object.keys(e).forEach((n=>{let r=t.update([[Number(n)]]).text();t.update(e[n]).json({offset:!0}).forEach(((t,e)=>{r=function(t,e,n){let r=((t,e,n)=>{let r=9*n,o=e.start+r,i=o+e.length;return[t.substring(0,o),t.substring(o,i),t.substring(i,t.length)]})(t,e,n);return`${r[0]}${wn.blue(r[1])}${r[2]}`}(r,t.offset,e)})),console.log(r)})),console.log("\n")}};var yn={api:function(t){Object.assign(t.prototype,mn)},methods:{one:{hash:on,debug:bn}}};const vn=function(t,e){if(t[0]!==e[0])return!1;let[,n,r]=t,[,o,i]=e;return n<=o&&r>o||o<=n&&i>n},xn=function(t){let e={};return t.forEach((t=>{e[t[0]]=e[t[0]]||[],e[t[0]].push(t)})),e},jn=function(t,e){let n=xn(e),r=[];return t.forEach((t=>{let[e]=t,o=n[e]||[];if(o=o.filter((e=>function(t,e){return t[1]<=e[1]&&e[2]<=t[2]}(t,e))),0===o.length)return void r.push({passthrough:t});o=o.sort(((t,e)=>t[1]-e[1]));let i=t;o.forEach(((t,e)=>{let n=function(t,e){let[n,r]=t,o=e[1],i=e[2],s={};if(r<o){let e=o<t[2]?o:t[2];s.before=[n,r,e]}return s.match=e,t[2]>i&&(s.after=[n,i,t[2]]),s}(i,t);o[e+1]?(r.push({before:n.before,match:n.match}),n.after&&(i=n.after)):r.push(n)}))})),r};var En={one:{termList:function(t){let e=[];for(let n=0;n<t.length;n+=1)for(let r=0;r<t[n].length;r+=1)e.push(t[n][r]);return e},getDoc:function(t,e){let n=[];return t.forEach(((r,o)=>{if(!r)return;let[i,s,l,u,a]=r,c=e[i]||[];if(void 0===s&&(s=0),void 0===l&&(l=c.length),!u||c[s]&&c[s].id===u)c=c.slice(s,l);else{let n=function(t,e,n){for(let r=0;r<20;r+=1){if(e[n-r]){let o=e[n-r].findIndex((e=>e.id===t));if(-1!==o)return[n-r,o]}if(e[n+r]){let o=e[n+r].findIndex((e=>e.id===t));if(-1!==o)return[n+r,o]}}return null}(u,e,i);if(null!==n){let r=l-s;c=e[n[0]].slice(n[1],n[1]+r);let i=c[0]?c[0].id:null;t[o]=[n[0],n[1],n[1]+r,i]}}0!==c.length&&s!==l&&(a&&c[c.length-1].id!==a&&(c=function(t,e){let[n,r,,,o]=t,i=e[n],s=i.findIndex((t=>t.id===o));return-1===s?(t[2]=e[n].length,t[4]=i.length?i[i.length-1].id:null):t[2]=s,e[n].slice(r,t[2]+1)}(r,e)),n.push(c))})),n=n.filter((t=>t.length>0)),n},pointer:{indexN:xn,splitAll:jn}}};const On=function(t,e){let n=t.concat(e),r=xn(n),o=[];return n.forEach((t=>{let[e]=t;if(1===r[e].length)return void o.push(t);let n=r[e].filter((e=>vn(t,e)));n.push(t);let i=function(t){let e=t[0][1],n=t[0][2];return t.forEach((t=>{t[1]<e&&(e=t[1]),t[2]>n&&(n=t[2])})),[t[0][0],e,n]}(n);o.push(i)})),o=function(t){let e={};for(let n=0;n<t.length;n+=1)e[t[n].join(",")]=t[n];return Object.values(e)}(o),o},kn=function(t,e){let n=[];return jn(t,e).forEach((t=>{t.passthrough&&n.push(t.passthrough),t.before&&n.push(t.before),t.after&&n.push(t.after)})),n},Pn=(t,e)=>{return"string"==typeof t||(n=t,"[object Array]"===Object.prototype.toString.call(n))?e.match(t):t||e.none();var n},_n=function(t,e){return t.map((t=>{let[n,r]=t;return e[n]&&e[n][r]&&(t[3]=e[n][r].id),t}))},Sn={union:function(t){t=Pn(t,this);let e=On(this.fullPointer,t.fullPointer);return e=_n(e,this.document),this.toView(e)}};Sn.and=Sn.union,Sn.intersection=function(t){t=Pn(t,this);let e=function(t,e){let n=xn(e),r=[];return t.forEach((t=>{let e=n[t[0]]||[];e=e.filter((e=>vn(t,e))),0!==e.length&&e.forEach((e=>{let n=function(t,e){let n=t[1]<e[1]?e[1]:t[1],r=t[2]>e[2]?e[2]:t[2];return n<r?[t[0],n,r]:null}(t,e);n&&r.push(n)}))})),r}(this.fullPointer,t.fullPointer);return e=_n(e,this.document),this.toView(e)},Sn.not=function(t){t=Pn(t,this);let e=kn(this.fullPointer,t.fullPointer);return e=_n(e,this.document),this.toView(e)},Sn.difference=Sn.not,Sn.complement=function(){let t=this.all(),e=kn(t.fullPointer,this.fullPointer);return e=_n(e,this.document),this.toView(e)},Sn.settle=function(){let t=this.fullPointer;return t.forEach((e=>{t=On(t,[e])})),t=_n(t,this.document),this.update(t)};var zn={methods:En,api:function(t){Object.assign(t.prototype,Sn)}};const An=function(t){return!0===t.optional||!0===t.negative?null:t.tag?"#"+t.tag:t.word?t.word:t.switch?`%${t.switch}%`:null},$n=function(t,e){const n=e.methods.one.parseMatch;return t.forEach((t=>{t.regs=n(t.match,{},e),"string"==typeof t.ifNo&&(t.ifNo=[t.ifNo]),t.notIf&&(t.notIf=n(t.notIf,{},e)),t.needs=function(t){let e=[];return t.forEach((t=>{e.push(An(t)),"and"===t.operator&&t.choices&&t.choices.forEach((t=>{t.forEach((t=>{e.push(An(t))}))}))})),e.filter((t=>t))}(t.regs);let{wants:r,count:o}=function(t){let e=[],n=0;return t.forEach((t=>{"or"!==t.operator||t.optional||t.negative||(t.fastOr&&Array.from(t.fastOr).forEach((t=>{e.push(t)})),t.choices&&t.choices.forEach((t=>{t.forEach((t=>{let n=An(t);n&&e.push(n)}))})),n+=1)})),{wants:e,count:n}}(t.regs);t.wants=r,t.minWant=o,t.minWords=t.regs.filter((t=>!t.optional)).length})),t};var Tn={buildNet:function(t,e){t=$n(t,e);let n={};t.forEach((t=>{t.needs.forEach((e=>{n[e]=Array.isArray(n[e])?n[e]:[],n[e].push(t)})),t.wants.forEach((e=>{n[e]=Array.isArray(n[e])?n[e]:[],n[e].push(t)}))})),Object.keys(n).forEach((t=>{let e={};n[t]=n[t].filter((t=>"boolean"!=typeof e[t.match]&&(e[t.match]=!0,!0)))}));let r=t.filter((t=>0===t.needs.length&&0===t.wants.length));return{hooks:n,always:r}},bulkMatch:function(t,e,n,r={}){let o=n.one.cacheDoc(t),i=function(t,e){return t.map(((n,r)=>{let o=[];Object.keys(e).forEach((n=>{t[r].has(n)&&(o=o.concat(e[n]))}));let i={};return o=o.filter((t=>"boolean"!=typeof i[t.match]&&(i[t.match]=!0,!0))),o}))}(o,e.hooks);i=function(t,e){return t.map(((t,n)=>{let r=e[n];return(t=(t=t.filter((t=>t.needs.every((t=>r.has(t)))))).filter((t=>void 0===t.ifNo||!0!==t.ifNo.some((t=>r.has(t)))))).filter((t=>0===t.wants.length||t.wants.filter((t=>r.has(t))).length>=t.minWant))}))}(i,o),e.always.length>0&&(i=i.map((t=>t.concat(e.always)))),i=function(t,e){return t.map(((t,n)=>{let r=e[n].length;return t=t.filter((t=>r>=t.minWords)),t}))}(i,t);let s=function(t,e,n,r,o){let i=[];for(let n=0;n<t.length;n+=1)for(let s=0;s<t[n].length;s+=1){let l=t[n][s],u=r.one.match([e[n]],l);if(u.ptrs.length>0&&(u.ptrs.forEach((t=>{t[0]=n;let e=Object.assign({},l,{pointer:t});void 0!==l.unTag&&(e.unTag=l.unTag),i.push(e)})),!0===o.matchOne))return[i[0]]}return i}(i,t,0,n,r);return s},bulkTagger:function(t,e,n){const{model:r,methods:o}=n,{getDoc:i,setTag:s,unTag:l}=o.one,u=o.two.looksPlural;if(0===t.length)return t;return("undefined"!=typeof process&&process.env?process.env:self.env||{}).DEBUG_TAGS&&console.log(`\n\n  [32m→ ${t.length} post-tagger:[0m`),t.map((t=>{if(!t.tag&&!t.chunk&&!t.unTag)return;let o=t.reason||t.match,a=i([t.pointer],e)[0];if(!0===t.safe){if(!1===function(t,e,n){let r=n.one.tagSet;if(!r.hasOwnProperty(e))return!0;let o=r[e].not||[];for(let e=0;e<t.length;e+=1){let n=t[e];for(let t=0;t<o.length;t+=1)if(!0===n.tags.has(o[t]))return!1}return!0}(a,t.tag,r))return;if("-"===a[a.length-1].post)return}if(void 0!==t.tag){if(s(a,t.tag,n,t.safe,`[post] '${o}'`),"Noun"===t.tag&&u){let e=a[a.length-1];u(e.text)?s([e],"Plural",n,t.safe,"quick-plural"):s([e],"Singular",n,t.safe,"quick-singular")}!0===t.freeze&&a.forEach((t=>t.frozen=!0))}void 0!==t.unTag&&l(a,t.unTag,n,t.safe,o),t.chunk&&a.forEach((e=>e.chunk=t.chunk))}))}},Cn={lib:{buildNet:function(t){let e=this.methods().one.buildNet(t,this.world());return e.isNet=!0,e}},api:function(t){t.prototype.sweep=function(t,e={}){const{world:n,docs:r}=this,{methods:o}=n;let i=o.one.bulkMatch(r,t,this.methods,e);!1!==e.tagger&&o.one.bulkTagger(i,r,this.world),i=i.map((t=>{let e=t.pointer,n=r[e[0]][e[1]],o=e[2]-e[1];return n.index&&(t.pointer=[n.index[0],n.index[1],e[1]+o]),t}));let s=i.map((t=>t.pointer));return i=i.map((t=>(t.view=this.update([t.pointer]),delete t.regs,delete t.needs,delete t.pointer,delete t._expanded,t))),{view:this.update(s),found:i}}},methods:{one:Tn}};const Ln=/ /,Nn=function(t,e){"Noun"===e&&(t.chunk=e),"Verb"===e&&(t.chunk=e)},Vn=function(t,e,n,r){if(!0===t.tags.has(e))return null;if("."===e)return null;!0===t.frozen&&(r=!0);let o=n[e];if(o){if(o.not&&o.not.length>0)for(let e=0;e<o.not.length;e+=1){if(!0===r&&t.tags.has(o.not[e]))return null;t.tags.delete(o.not[e])}if(o.parents&&o.parents.length>0)for(let e=0;e<o.parents.length;e+=1)t.tags.add(o.parents[e]),Nn(t,o.parents[e])}return t.tags.add(e),t.dirty=!0,Nn(t,e),!0},Fn=function(t,e,n={},r,o){const i=n.model.one.tagSet||{};if(!e)return;const s="undefined"!=typeof process&&process.env?process.env:self.env||{};var l;if(s&&s.DEBUG_TAGS&&((t,e,n="")=>{let r=t.map((t=>t.text||"["+t.implicit+"]")).join(" ");var o;"string"!=typeof e&&e.length>2&&(e=e.slice(0,2).join(", #")+" +"),e="string"!=typeof e?e.join(", #"):e,console.log(` ${(o=r,"[33m[3m"+o+"[0m").padEnd(24)} [32m→[0m #${e.padEnd(22)}  ${(t=>"[3m"+t+"[0m")(n)}`)})(t,e,o),!0!=(l=e,"[object Array]"===Object.prototype.toString.call(l)))if("string"==typeof e)if(e=e.trim(),Ln.test(e))!function(t,e,n,r){let o=e.split(Ln);t.forEach(((t,e)=>{let i=o[e];i&&(i=i.replace(/^#/,""),Vn(t,i,n,r))}))}(t,e,i,r);else{e=e.replace(/^#/,"");for(let n=0;n<t.length;n+=1)Vn(t[n],e,i,r)}else console.warn(`compromise: Invalid tag '${e}'`);else e.forEach((e=>Fn(t,e,n,r)))},qn=function(t){return t.children=t.children||[],t._cache=t._cache||{},t.props=t.props||{},t._cache.parents=t._cache.parents||[],t._cache.children=t._cache.children||[],t},Gn=/^ *(#|\/\/)/,Dn=function(t){let e=t.trim().split(/->/),n=[];e.forEach((t=>{n=n.concat(function(t){if(!(t=t.trim()))return null;if(/^\[/.test(t)&&/\]$/.test(t)){let e=(t=(t=t.replace(/^\[/,"")).replace(/\]$/,"")).split(/,/);return e=e.map((t=>t.trim())).filter((t=>t)),e=e.map((t=>qn({id:t}))),e}return[qn({id:t})]}(t))})),n=n.filter((t=>t));let r=n[0];for(let t=1;t<n.length;t+=1)r.children.push(n[t]),r=n[t];return n[0]},Bn=(t,e)=>{let n=[],r=[t];for(;r.length>0;){let t=r.pop();n.push(t),t.children&&t.children.forEach((n=>{e&&e(t,n),r.push(n)}))}return n},Mn=t=>"[object Array]"===Object.prototype.toString.call(t),Un=t=>(t=t||"").trim(),Wn=function(t=[]){return"string"==typeof t?function(t){let e=t.split(/\r?\n/),n=[];e.forEach((t=>{if(!t.trim()||Gn.test(t))return;let e=(t=>{const e=/^( {2}|\t)/;let n=0;for(;e.test(t);)t=t.replace(e,""),n+=1;return n})(t);n.push({indent:e,node:Dn(t)})}));let r=function(t){let e={children:[]};return t.forEach(((n,r)=>{0===n.indent?e.children=e.children.concat(n.node):t[r-1]&&function(t,e){let n=t[e].indent;for(;e>=0;e-=1)if(t[e].indent<n)return t[e];return t[0]}(t,r).node.children.push(n.node)})),e}(n);return r=qn(r),r}(t):Mn(t)?function(t){let e={};t.forEach((t=>{e[t.id]=t}));let n=qn({});return t.forEach((t=>{if((t=qn(t)).parent)if(e.hasOwnProperty(t.parent)){let n=e[t.parent];delete t.parent,n.children.push(t)}else console.warn(`[Grad] - missing node '${t.parent}'`);else n.children.push(t)})),n}(t):(Bn(e=t).forEach(qn),e);var e},In=function(t,e){let n="-> ";e&&(n=(t=>"[2m"+t+"[0m")("→ "));let r="";return Bn(t).forEach(((t,o)=>{let i=t.id||"";if(e&&(i=(t=>"[31m"+t+"[0m")(i)),0===o&&!t.id)return;let s=t._cache.parents.length;r+="    ".repeat(s)+n+i+"\n"})),r},Rn=function(t){let e=Bn(t);e.forEach((t=>{delete(t=Object.assign({},t)).children}));let n=e[0];return n&&!n.id&&0===Object.keys(n.props).length&&e.shift(),e},Qn={text:In,txt:In,array:Rn,flat:Rn},Hn=function(t,e){return"nested"===e||"json"===e?t:"debug"===e?(console.log(In(t,!0)),null):Qn.hasOwnProperty(e)?Qn[e](t):t},Zn=t=>{Bn(t,((t,e)=>{t.id&&(t._cache.parents=t._cache.parents||[],e._cache.parents=t._cache.parents.concat([t.id]))}))},Kn=/\//;class g{constructor(t={}){Object.defineProperty(this,"json",{enumerable:!1,value:t,writable:!0})}get children(){return this.json.children}get id(){return this.json.id}get found(){return this.json.id||this.json.children.length>0}props(t={}){let e=this.json.props||{};return"string"==typeof t&&(e[t]=!0),this.json.props=Object.assign(e,t),this}get(t){if(t=Un(t),!Kn.test(t)){let e=this.json.children.find((e=>e.id===t));return new g(e)}let e=((t,e)=>{let n=(t=>"string"!=typeof t?t:(t=t.replace(/^\//,"")).split(/\//))(e=e||"");for(let e=0;e<n.length;e+=1){let r=t.children.find((t=>t.id===n[e]));if(!r)return null;t=r}return t})(this.json,t)||qn({});return new g(e)}add(t,e={}){if(Mn(t))return t.forEach((t=>this.add(Un(t),e))),this;t=Un(t);let n=qn({id:t,props:e});return this.json.children.push(n),new g(n)}remove(t){return t=Un(t),this.json.children=this.json.children.filter((e=>e.id!==t)),this}nodes(){return Bn(this.json).map((t=>(delete(t=Object.assign({},t)).children,t)))}cache(){return(t=>{let e=Bn(t,((t,e)=>{t.id&&(t._cache.parents=t._cache.parents||[],t._cache.children=t._cache.children||[],e._cache.parents=t._cache.parents.concat([t.id]))})),n={};e.forEach((t=>{t.id&&(n[t.id]=t)})),e.forEach((t=>{t._cache.parents.forEach((e=>{n.hasOwnProperty(e)&&n[e]._cache.children.push(t.id)}))})),t._cache.children=Object.keys(n)})(this.json),this}list(){return Bn(this.json)}fillDown(){var t;return t=this.json,Bn(t,((t,e)=>{e.props=((t,e)=>(Object.keys(e).forEach((n=>{if(e[n]instanceof Set){let r=t[n]||new Set;t[n]=new Set([...r,...e[n]])}else if((t=>t&&"object"==typeof t&&!Array.isArray(t))(e[n])){let r=t[n]||{};t[n]=Object.assign({},e[n],r)}else Mn(e[n])?t[n]=e[n].concat(t[n]||[]):void 0===t[n]&&(t[n]=e[n])})),t))(e.props,t.props)})),this}depth(){Zn(this.json);let t=Bn(this.json),e=t.length>1?1:0;return t.forEach((t=>{if(0===t._cache.parents.length)return;let n=t._cache.parents.length+1;n>e&&(e=n)})),e}out(t){return Zn(this.json),Hn(this.json,t)}debug(){return Zn(this.json),Hn(this.json,"debug"),this}}const Jn=function(t){let e=Wn(t);return new g(e)};Jn.prototype.plugin=function(t){t(this)};const Xn={Noun:"blue",Verb:"green",Negative:"green",Date:"red",Value:"red",Adjective:"magenta",Preposition:"cyan",Conjunction:"cyan",Determiner:"cyan",Hyphenated:"cyan",Adverb:"cyan"},Yn=function(t){if(Xn.hasOwnProperty(t.id))return Xn[t.id];if(Xn.hasOwnProperty(t.is))return Xn[t.is];let e=t._cache.parents.find((t=>Xn[t]));return Xn[e]},tr=function(t){return t?"string"==typeof t?[t]:t:[]},er=function(t,e){return t=function(t,e){return Object.keys(t).forEach((n=>{t[n].isA&&(t[n].is=t[n].isA),t[n].notA&&(t[n].not=t[n].notA),t[n].is&&"string"==typeof t[n].is&&(e.hasOwnProperty(t[n].is)||t.hasOwnProperty(t[n].is)||(t[t[n].is]={})),t[n].not&&"string"==typeof t[n].not&&!t.hasOwnProperty(t[n].not)&&(e.hasOwnProperty(t[n].not)||t.hasOwnProperty(t[n].not)||(t[t[n].not]={}))})),t}(t,e),Object.keys(t).forEach((e=>{t[e].children=tr(t[e].children),t[e].not=tr(t[e].not)})),Object.keys(t).forEach((e=>{(t[e].not||[]).forEach((n=>{t[n]&&t[n].not&&t[n].not.push(e)}))})),t};var nr={one:{setTag:Fn,unTag:function(t,e,n){e=e.trim().replace(/^#/,"");for(let r=0;r<t.length;r+=1){let o=t[r];if(!0===o.frozen)continue;if("*"===e){o.tags.clear();continue}let i=n[e];if(i&&i.children.length>0)for(let t=0;t<i.children.length;t+=1)o.tags.delete(i.children[t]);o.tags.delete(e)}},addTags:function(t,e){Object.keys(e).length>0&&(t=function(t){return Object.keys(t).forEach((e=>{t[e]=Object.assign({},t[e]),t[e].novel=!0})),t}(t)),t=er(t,e);const n=function(t){const e=Object.keys(t).map((e=>{let n=t[e];const r={not:new Set(n.not),also:n.also,is:n.is,novel:n.novel};return{id:e,parent:n.is,props:r,children:[]}}));return Jn(e).cache().fillDown().out("array")}(Object.assign({},e,t)),r=function(t){const e={};return t.forEach((t=>{let{not:n,also:r,is:o,novel:i}=t.props,s=t._cache.parents;r&&(s=s.concat(r)),e[t.id]={is:o,not:n,novel:i,also:r,parents:s,children:t._cache.children,color:Yn(t)}})),Object.keys(e).forEach((t=>{let n=new Set(e[t].not);e[t].not.forEach((t=>{e[t]&&e[t].children.forEach((t=>n.add(t)))})),e[t].not=Array.from(n)})),e}(n);return r},canBe:function(t,e,n){if(!n.hasOwnProperty(e))return!0;let r=n[e].not||[];for(let e=0;e<r.length;e+=1)if(t.tags.has(r[e]))return!1;return!0}}};const rr=function(t){return"[object Array]"===Object.prototype.toString.call(t)},or={tag:function(t,e="",n){if(!this.found||!t)return this;let r=this.termList();if(0===r.length)return this;const{methods:o,verbose:i,world:s}=this;return!0===i&&console.log(" +  ",t,e||""),rr(t)?t.forEach((t=>o.one.setTag(r,t,s,n,e))):o.one.setTag(r,t,s,n,e),this.uncache(),this},tagSafe:function(t,e=""){return this.tag(t,e,!0)},unTag:function(t,e){if(!this.found||!t)return this;let n=this.termList();if(0===n.length)return this;const{methods:r,verbose:o,model:i}=this;!0===o&&console.log(" -  ",t,e||"");let s=i.one.tagSet;return rr(t)?t.forEach((t=>r.one.unTag(n,t,s))):r.one.unTag(n,t,s),this.uncache(),this},canBe:function(t){t=t.replace(/^#/,"");let e=this.model.one.tagSet,n=this.methods.one.canBe,r=[];this.document.forEach(((o,i)=>{o.forEach(((o,s)=>{n(o,t,e)||r.push([i,s,s+1])}))}));let o=this.update(r);return this.difference(o)}};var ir={addTags:function(t){const{model:e,methods:n}=this.world(),r=e.one.tagSet;let o=(0,n.one.addTags)(t,r);return e.one.tagSet=o,this}};const sr=new Set(["Auxiliary","Possessive"]);var lr={model:{one:{tagSet:{}}},compute:{tagRank:function(t){const{document:e,world:n}=t,r=n.model.one.tagSet;e.forEach((t=>{t.forEach((t=>{let e=Array.from(t.tags);t.tagRank=function(t,e){return t=t.sort(((t,n)=>{if(sr.has(t)||!e.hasOwnProperty(n))return 1;if(sr.has(n)||!e.hasOwnProperty(t))return-1;let r=e[t].children||[],o=r.length;return r=e[n].children||[],o-r.length})),t}(e,r)}))}))}},methods:nr,api:function(t){Object.assign(t.prototype,or)},lib:ir};const ur=/([.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s)/g,ar=/^[.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s$/,cr=/((?:\r?\n|\r)+)/,hr=/[a-z0-9\u00C0-\u00FF\u00a9\u00ae\u2000-\u3300\ud000-\udfff]/i,fr=/\S/,pr={'"':'"',"＂":"＂","“":"”","‟":"”","„":"”","⹂":"”","‚":"’","«":"»","‹":"›","‵":"′","‶":"″","‷":"‴","〝":"〞","〟":"〞"},dr=RegExp("["+Object.keys(pr).join("")+"]","g"),mr=RegExp("["+Object.values(pr).join("")+"]","g"),gr=function(t){if(!t)return!1;let e=t.match(mr);return null!==e&&1===e.length},wr=/\(/g,br=/\)/g,yr=/\S/,vr=/^\s+/,xr=function(t,e){let n=t.split(/[-–—]/);if(n.length<=1)return!1;const{prefixes:r,suffixes:o}=e.one;if(1===n[0].length&&/[a-z]/i.test(n[0]))return!1;if(r.hasOwnProperty(n[0]))return!1;if(n[1]=n[1].trim().replace(/[.?!]$/,""),o.hasOwnProperty(n[1]))return!1;if(!0===/^([a-z\u00C0-\u00FF`"'/]+)[-–—]([a-z0-9\u00C0-\u00FF].*)/i.test(t))return!0;return!0===/^[('"]?([0-9]{1,4})[-–—]([a-z\u00C0-\u00FF`"'/-]+[)'"]?$)/i.test(t)},jr=function(t){let e=[];const n=t.split(/[-–—]/);let r="-",o=t.match(/[-–—]/);o&&o[0]&&(r=o);for(let t=0;t<n.length;t++)t===n.length-1?e.push(n[t]):e.push(n[t]+r);return e},Er=/\p{L} ?\/ ?\p{L}+$/u,Or=/\S/,kr=/^[!?.]+$/,Pr=/(\S+)/;let _r=[".","?","!",":",";","-","–","—","--","...","(",")","[","]",'"',"'","`","«","»","*","•"];_r=_r.reduce(((t,e)=>(t[e]=!0,t)),{});const Sr=/\p{Letter}/u,zr=/[\p{Number}\p{Currency_Symbol}]/u,Ar=/^[a-z]\.([a-z]\.)+/i,$r=/[sn]['’]$/,Tr=/([A-Z]\.)+[A-Z]?,?$/,Cr=/^[A-Z]\.,?$/,Lr=/[A-Z]{2,}('s|,)?$/,Nr=/([a-z]\.)+[a-z]\.?$/,Vr=function(t){return function(t){return!0===Tr.test(t)||!0===Nr.test(t)||!0===Cr.test(t)||!0===Lr.test(t)}(t)&&(t=t.replace(/\./g,"")),t},Fr=function(t,e){const n=e.methods.one.killUnicode;let r=t.text||"";r=function(t){let e=t=(t=(t=t||"").toLowerCase()).trim();return t=(t=(t=t.replace(/[,;.!?]+$/,"")).replace(/\u2026/g,"...")).replace(/\u2013/g,"-"),!1===/^[:;]/.test(t)&&(t=(t=(t=t.replace(/\.{3,}$/g,"")).replace(/[",.!:;?)]+$/g,"")).replace(/^['"(]+/g,"")),""===(t=(t=t.replace(/[\u200B-\u200D\uFEFF]/g,"")).trim())&&(t=e),t.replace(/([0-9]),([0-9])/g,"$1$2")}(r),r=n(r,e),r=Vr(r),t.normal=r},qr=/[ .][A-Z]\.? *$/i,Gr=/(?:\u2026|\.{2,}) *$/,Dr=/\p{L}/u,Br=/\. *$/,Mr=/^[A-Z]\. $/;var Ur={one:{killUnicode:function(t,e){const n=e.model.one.unicode||{};let r=(t=t||"").split("");return r.forEach(((t,e)=>{n[t]&&(r[e]=n[t])})),r.join("")},tokenize:{splitSentences:function(t,e){if(t=t||"",!(t=String(t))||"string"!=typeof t||!1===yr.test(t))return[];let n=function(t){let e=[],n=t.split(cr);for(let t=0;t<n.length;t++){let r=n[t].split(ur);for(let t=0;t<r.length;t++)r[t+1]&&!0===ar.test(r[t+1])&&(r[t]+=r[t+1],r[t+1]=""),""!==r[t]&&e.push(r[t])}return e}(t=t.replace(" "," ")),r=function(t){let e=[];for(let n=0;n<t.length;n++){let r=t[n];if(void 0!==r&&""!==r){if(!1===fr.test(r)||!1===hr.test(r)){if(e[e.length-1]){e[e.length-1]+=r;continue}if(t[n+1]){t[n+1]=r+t[n+1];continue}}e.push(r)}}return e}(n);if(r=function(t,e){const n=e.methods.one.tokenize.isSentence,r=e.model.one.abbreviations||new Set;let o=[];for(let e=0;e<t.length;e++){let i=t[e];t[e+1]&&!1===n(i,r)?t[e+1]=i+(t[e+1]||""):i&&i.length>0&&(o.push(i),t[e]="")}return o}(r,e),r=function(t){let e=[];for(let n=0;n<t.length;n+=1){let r=t[n].match(dr);if(null!==r&&1===r.length){if(gr(t[n+1])&&t[n+1].length<280){t[n]+=t[n+1],e.push(t[n]),t[n+1]="",n+=1;continue}if(gr(t[n+2])){let r=t[n+1]+t[n+2];if(r.length<280){t[n]+=r,e.push(t[n]),t[n+1]="",t[n+2]="",n+=2;continue}}}e.push(t[n])}return e}(r),r=function(t){let e=[];for(let n=0;n<t.length;n+=1){let r=t[n].match(wr);null!==r&&1===r.length&&t[n+1]&&t[n+1].length<250&&null!==t[n+1].match(br)&&1===r.length&&!wr.test(t[n+1])?(t[n]+=t[n+1],e.push(t[n]),t[n+1]="",n+=1):e.push(t[n])}return e}(r),0===r.length)return[t];for(let t=1;t<r.length;t+=1){let e=r[t].match(vr);null!==e&&(r[t-1]+=e[0],r[t]=r[t].replace(vr,""))}return r},isSentence:function(t,e){if(!1===Dr.test(t))return!1;if(!0===qr.test(t))return!1;if(3===t.length&&Mr.test(t))return!1;if(!0===Gr.test(t))return!1;let n=t.replace(/[.!?\u203D\u2E18\u203C\u2047-\u2049] *$/,"").split(" "),r=n[n.length-1].toLowerCase();return!0!==e.hasOwnProperty(r)||!0!==Br.test(t)},splitTerms:function(t,e){let n=[],r=[];if("number"==typeof(t=t||"")&&(t=String(t)),function(t){return"[object Array]"===Object.prototype.toString.call(t)}(t))return t;const o=t.split(Pr);for(let t=0;t<o.length;t++)!0!==xr(o[t],e)?r.push(o[t]):r=r.concat(jr(o[t]));let i="";for(let t=0;t<r.length;t++){let e=r[t];!0===Or.test(e)&&!1===_r.hasOwnProperty(e)&&!1===kr.test(e)?(n.length>0?(n[n.length-1]+=i,n.push(e)):n.push(i+e),i=""):i+=e}return i&&(0===n.length&&(n[0]=""),n[n.length-1]+=i),n=function(t){for(let e=1;e<t.length-1;e++)Er.test(t[e])&&(t[e-1]+=t[e]+t[e+1],t[e]=null,t[e+1]=null);return t}(n),n=function(t){const e=/^[0-9]{1,4}(:[0-9][0-9])?([a-z]{1,2})? ?[-–—] ?$/,n=/^[0-9]{1,4}([a-z]{1,2})? ?$/;for(let r=0;r<t.length-1;r+=1)t[r+1]&&e.test(t[r])&&n.test(t[r+1])&&(t[r]=t[r]+t[r+1],t[r+1]=null);return t}(n),n=n.filter((t=>t)),n},splitWhitespace:(t,e)=>{let{str:n,pre:r,post:o}=function(t,e){let{prePunctuation:n,postPunctuation:r,emoticons:o}=e.one,i=t,s="",l="",u=Array.from(t);if(o.hasOwnProperty(t.trim()))return{str:t.trim(),pre:s,post:" "};let a=u.length;for(let t=0;t<a;t+=1){let t=u[0];if(!0!==n[t]){if(("+"===t||"-"===t)&&zr.test(u[1]))break;if("'"===t&&3===t.length&&zr.test(u[1]))break;if(Sr.test(t)||zr.test(t))break;s+=u.shift()}}a=u.length;for(let t=0;t<a;t+=1){let t=u[u.length-1];if(!0!==r[t]){if(Sr.test(t)||zr.test(t))break;"."===t&&!0===Ar.test(i)||"'"===t&&!0===$r.test(i)||(l=u.pop()+l)}}return""===(t=u.join(""))&&(i=i.replace(/ *$/,(t=>(l=t||"",""))),t=i,s=""),{str:t,pre:s,post:l}}(t,e);return{text:n,pre:r,post:o,tags:new Set}},fromString:function(t,e){const{methods:n,model:r}=e,{splitSentences:o,splitTerms:i,splitWhitespace:s}=n.one.tokenize;return t=o(t=t||"",e).map((t=>{let n=i(t,r);return n=n.map((t=>s(t,r))),n.forEach((t=>{Fr(t,e)})),n})),t}}}};let Wr={},Ir={};[[["approx","apt","bc","cyn","eg","esp","est","etc","ex","exp","prob","pron","gal","min","pseud","fig","jd","lat","lng","vol","fm","def","misc","plz","ea","ps","sec","pt","pref","pl","pp","qt","fr","sq","nee","ss","tel","temp","vet","ver","fem","masc","eng","adj","vb","rb","inf","situ","vivo","vitro","wr"]],[["dl","ml","gal","qt","pt","tbl","tsp","tbsp","km","dm","cm","mm","mi","td","hr","hrs","kg","hg","dg","cg","mg","µg","lb","oz","sq ft","hz","mps","mph","kmph","kb","mb","tb","lx","lm","fl oz","yb"],"Unit"],[["ad","al","arc","ba","bl","ca","cca","col","corp","ft","fy","ie","lit","ma","md","pd","tce"],"Noun"],[["adj","adm","adv","asst","atty","bldg","brig","capt","cmdr","comdr","cpl","det","dr","esq","gen","gov","hon","jr","llb","lt","maj","messrs","mlle","mme","mr","mrs","ms","mstr","phd","prof","pvt","rep","reps","res","rev","sen","sens","sfc","sgt","sir","sr","supt","surg"],"Honorific"],[["jan","feb","mar","apr","jun","jul","aug","sep","sept","oct","nov","dec"],"Month"],[["dept","univ","assn","bros","inc","ltd","co"],"Organization"],[["rd","st","dist","mt","ave","blvd","cl","cres","hwy","ariz","cal","calif","colo","conn","fla","fl","ga","ida","ia","kan","kans","minn","neb","nebr","okla","penna","penn","pa","dak","tenn","tex","ut","vt","va","wis","wisc","wy","wyo","usafa","alta","ont","que","sask"],"Place"]].forEach((t=>{t[0].forEach((e=>{Wr[e]=!0,Ir[e]="Abbreviation",void 0!==t[1]&&(Ir[e]=[Ir[e],t[1]])}))}));var Rr=["anti","bi","co","contra","de","extra","infra","inter","intra","macro","micro","mis","mono","multi","peri","pre","pro","proto","pseudo","re","sub","supra","trans","tri","un","out","ex"].reduce(((t,e)=>(t[e]=!0,t)),{});let Qr={"!":"¡","?":"¿Ɂ",'"':'“”"❝❞',"'":"‘‛❛❜’","-":"—–",a:"ªÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻȀȁȂȃȦȧȺΆΑΔΛάαλАаѦѧӐӑӒӓƛæ",b:"ßþƀƁƂƃƄƅɃΒβϐϦБВЪЬвъьѢѣҌҍ",c:"¢©ÇçĆćĈĉĊċČčƆƇƈȻȼͻͼϲϹϽϾСсєҀҁҪҫ",d:"ÐĎďĐđƉƊȡƋƌ",e:"ÈÉÊËèéêëĒēĔĕĖėĘęĚěƐȄȅȆȇȨȩɆɇΈΕΞΣέεξϵЀЁЕеѐёҼҽҾҿӖӗễ",f:"ƑƒϜϝӺӻҒғſ",g:"ĜĝĞğĠġĢģƓǤǥǦǧǴǵ",h:"ĤĥĦħƕǶȞȟΉΗЂЊЋНнђћҢңҤҥҺһӉӊ",I:"ÌÍÎÏ",i:"ìíîïĨĩĪīĬĭĮįİıƖƗȈȉȊȋΊΐΪίιϊІЇіїi̇",j:"ĴĵǰȷɈɉϳЈј",k:"ĶķĸƘƙǨǩΚκЌЖКжкќҚқҜҝҞҟҠҡ",l:"ĹĺĻļĽľĿŀŁłƚƪǀǏǐȴȽΙӀӏ",m:"ΜϺϻМмӍӎ",n:"ÑñŃńŅņŇňŉŊŋƝƞǸǹȠȵΝΠήηϞЍИЙЛПийлпѝҊҋӅӆӢӣӤӥπ",o:"ÒÓÔÕÖØðòóôõöøŌōŎŏŐőƟƠơǑǒǪǫǬǭǾǿȌȍȎȏȪȫȬȭȮȯȰȱΌΘΟθοσόϕϘϙϬϴОФоѲѳӦӧӨөӪӫ",p:"ƤΡρϷϸϼРрҎҏÞ",q:"Ɋɋ",r:"ŔŕŖŗŘřƦȐȑȒȓɌɍЃГЯгяѓҐґ",s:"ŚśŜŝŞşŠšƧƨȘșȿЅѕ",t:"ŢţŤťŦŧƫƬƭƮȚțȶȾΓΤτϮТт",u:"ÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưƱƲǓǔǕǖǗǘǙǚǛǜȔȕȖȗɄΰυϋύ",v:"νѴѵѶѷ",w:"ŴŵƜωώϖϢϣШЩшщѡѿ",x:"×ΧχϗϰХхҲҳӼӽӾӿ",y:"ÝýÿŶŷŸƳƴȲȳɎɏΎΥΫγψϒϓϔЎУучўѰѱҮүҰұӮӯӰӱӲӳ",z:"ŹźŻżŽžƵƶȤȥɀΖ"},Hr={};Object.keys(Qr).forEach((function(t){Qr[t].split("").forEach((function(e){Hr[e]=t}))}));const Zr=/\//,Kr=/[a-z]\.[a-z]/i,Jr=/[0-9]/,Xr=function(t,e){let n=t.normal||t.text||t.machine;const r=e.model.one.aliases;if(r.hasOwnProperty(n)&&(t.alias=t.alias||[],t.alias.push(r[n])),Zr.test(n)&&!Kr.test(n)&&!Jr.test(n)){let e=n.split(Zr);e.length<=3&&e.forEach((e=>{""!==(e=e.trim())&&(t.alias=t.alias||[],t.alias.push(e))}))}return t},Yr=/^\p{Letter}+-\p{Letter}+$/u,to=function(t){let e=t.implicit||t.normal||t.text;e=e.replace(/['’]s$/,""),e=e.replace(/s['’]$/,"s"),e=e.replace(/([aeiou][ktrp])in'$/,"$1ing"),Yr.test(e)&&(e=e.replace(/-/g,"")),e=e.replace(/^[#@]/,""),e!==t.normal&&(t.machine=e)},eo=function(t,e){let n=t.docs;for(let r=0;r<n.length;r+=1)for(let o=0;o<n[r].length;o+=1)e(n[r][o],t.world)},no={alias:t=>eo(t,Xr),machine:t=>eo(t,to),normal:t=>eo(t,Fr),freq:function(t){let e=t.docs,n={};for(let t=0;t<e.length;t+=1)for(let r=0;r<e[t].length;r+=1){let o=e[t][r],i=o.machine||o.normal;n[i]=n[i]||0,n[i]+=1}for(let t=0;t<e.length;t+=1)for(let r=0;r<e[t].length;r+=1){let o=e[t][r],i=o.machine||o.normal;o.freq=n[i]}},offset:function(t){let e=0,n=0,r=t.document;for(let t=0;t<r.length;t+=1)for(let o=0;o<r[t].length;o+=1){let i=r[t][o];i.offset={index:n,start:e+i.pre.length,length:i.text.length},e+=i.pre.length+i.text.length+i.post.length,n+=1}},index:function(t){let e=t.document;for(let t=0;t<e.length;t+=1)for(let n=0;n<e[t].length;n+=1)e[t][n].index=[t,n]},wordCount:function(t){let e=0,n=t.docs;for(let t=0;t<n.length;t+=1)for(let r=0;r<n[t].length;r+=1)""!==n[t][r].normal&&(e+=1,n[t][r].wordCount=e)}};var ro={compute:no,methods:Ur,model:{one:{aliases:{"&":"and","@":"at","%":"percent",plz:"please",bein:"being"},abbreviations:Wr,prefixes:Rr,suffixes:{like:!0,ish:!0,less:!0,able:!0,elect:!0,type:!0,designate:!0},prePunctuation:{"#":!0,"@":!0,_:!0,"°":!0,"​":!0,"‌":!0,"‍":!0,"\ufeff":!0},postPunctuation:{"%":!0,_:!0,"°":!0,"​":!0,"‌":!0,"‍":!0,"\ufeff":!0},lexicon:Ir,unicode:Hr,emoticons:{"<3":!0,"</3":!0,"<\\3":!0,":^P":!0,":^p":!0,":^O":!0,":^3":!0}}},hooks:["alias","machine","index","id"]};var oo={typeahead:function(t){const e=t.model.one.typeahead,n=t.docs;if(0===n.length||0===Object.keys(e).length)return;let r=n[n.length-1]||[],o=r[r.length-1];if(!o.post&&e.hasOwnProperty(o.normal)){let n=e[o.normal];o.implicit=n,o.machine=n,o.typeahead=!0,t.compute.preTagger&&t.last().unTag("*").compute(["lexicon","preTagger"])}}};const io=function(){const t=this.docs;if(0===t.length)return this;let e=t[t.length-1]||[],n=e[e.length-1];return!0===n.typeahead&&n.machine&&(n.text=n.machine,n.normal=n.machine),this},so={safe:!0,min:3};var lo={typeahead:function(t=[],e={}){let n=this.model();var r;e=Object.assign({},so,e),r=t,"[object Object]"===Object.prototype.toString.call(r)&&(Object.assign(n.one.lexicon,t),t=Object.keys(t));let o=function(t,e,n){let r={},o=[],i=n.prefixes||{};return t.forEach((t=>{let s=(t=t.toLowerCase().trim()).length;e.max&&s>e.max&&(s=e.max);for(let l=e.min;l<s;l+=1){let s=t.substring(0,l);e.safe&&n.model.one.lexicon.hasOwnProperty(s)||(!0!==i.hasOwnProperty(s)&&!0!==r.hasOwnProperty(s)?r[s]=t:o.push(s))}})),r=Object.assign({},i,r),o.forEach((t=>{delete r[t]})),r}(t,e,this.world());return Object.keys(o).forEach((t=>{n.one.typeahead.hasOwnProperty(t)?delete n.one.typeahead[t]:n.one.typeahead[t]=o[t]})),this}};var uo={model:{one:{typeahead:{}}},api:function(t){t.prototype.autoFill=io},lib:lo,compute:oo,hooks:["typeahead"]};h.extend(W),h.extend(yn),h.extend(We),h.extend(zn),h.extend(lr),h.plugin(yt),h.extend(ro),h.extend(Ot),h.plugin(d),h.extend(qt),h.extend(uo),h.extend(Tt),h.extend(Cn);export{h as default};
