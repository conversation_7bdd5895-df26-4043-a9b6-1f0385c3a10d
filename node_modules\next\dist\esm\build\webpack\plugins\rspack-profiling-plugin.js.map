{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-profiling-plugin.ts"], "sourcesContent": ["// A basic implementation to allow loaders access to loaderContext.currentTraceSpan\n\nimport type { Span } from '../../../trace'\n\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\nconst pluginName = 'RspackProfilingPlugin'\nconst moduleSpansByCompilation = new WeakMap()\nexport const compilationSpans: WeakMap<any, Span> = new WeakMap()\n\nexport class RspackProfilingPlugin {\n  runWebpackSpan: Span\n\n  constructor({ runWebpackSpan }: { runWebpackSpan: Span }) {\n    this.runWebpackSpan = runWebpackSpan\n  }\n\n  apply(compiler: any) {\n    compiler.hooks.thisCompilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        const rspack = getRspackCore()\n\n        moduleSpansByCompilation.set(compilation, new WeakMap())\n        compilationSpans.set(\n          compilation,\n          this.runWebpackSpan.traceChild('compilation-' + compilation.name)\n        )\n\n        const compilationSpan = this.runWebpackSpan.traceChild(\n          `compilation-${compilation.name}`\n        )\n\n        const moduleHooks = rspack.NormalModule.getCompilationHooks(compilation)\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const span = compilationSpan.traceChild('build-module')\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n\n          moduleSpansByCompilation?.get(compilation)?.set(module, span)\n        })\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n      }\n    )\n  }\n}\n"], "names": ["getRspackCore", "pluginName", "moduleSpansByCompilation", "WeakMap", "compilationSpans", "RspackProfilingPlugin", "constructor", "runWebpackSpan", "apply", "compiler", "hooks", "thisCompilation", "tap", "name", "stage", "Infinity", "compilation", "rspack", "set", "<PERSON><PERSON><PERSON><PERSON>", "compilationSpan", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "module", "moduleSpan", "get", "currentTraceSpan", "buildModule", "span", "setAttribute", "userRequest", "layer", "succeedModule", "stop"], "mappings": "AAAA,mFAAmF;AAInF,SAASA,aAAa,QAAQ,iCAAgC;AAE9D,MAAMC,aAAa;AACnB,MAAMC,2BAA2B,IAAIC;AACrC,OAAO,MAAMC,mBAAuC,IAAID,UAAS;AAEjE,OAAO,MAAME;IAGXC,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IAEAC,MAAMC,QAAa,EAAE;QACnBA,SAASC,KAAK,CAACC,eAAe,CAACC,GAAG,CAChC;YAAEC,MAAMZ;YAAYa,OAAO,CAACC;QAAS,GACrC,CAACC;YACC,MAAMC,SAASjB;YAEfE,yBAAyBgB,GAAG,CAACF,aAAa,IAAIb;YAC9CC,iBAAiBc,GAAG,CAClBF,aACA,IAAI,CAACT,cAAc,CAACY,UAAU,CAAC,iBAAiBH,YAAYH,IAAI;YAGlE,MAAMO,kBAAkB,IAAI,CAACb,cAAc,CAACY,UAAU,CACpD,CAAC,YAAY,EAAEH,YAAYH,IAAI,EAAE;YAGnC,MAAMQ,cAAcJ,OAAOK,YAAY,CAACC,mBAAmB,CAACP;YAC5DK,YAAYG,MAAM,CAACZ,GAAG,CACpBX,YACA,CAACwB,eAAoBC;oBACAxB;gBAAnB,MAAMyB,cAAazB,gCAAAA,yBAChB0B,GAAG,CAACZ,iCADYd,8BAEf0B,GAAG,CAACF;gBACRD,cAAcI,gBAAgB,GAAGF;YACnC;YAGFX,YAAYN,KAAK,CAACoB,WAAW,CAAClB,GAAG,CAACX,YAAY,CAACyB;oBAK7CxB;gBAJA,MAAM6B,OAAOX,gBAAgBD,UAAU,CAAC;gBACxCY,KAAKC,YAAY,CAAC,QAAQN,OAAOO,WAAW;gBAC5CF,KAAKC,YAAY,CAAC,SAASN,OAAOQ,KAAK;gBAEvChC,6CAAAA,gCAAAA,yBAA0B0B,GAAG,CAACZ,iCAA9Bd,8BAA4CgB,GAAG,CAACQ,QAAQK;YAC1D;YAEAf,YAAYN,KAAK,CAACyB,aAAa,CAACvB,GAAG,CAACX,YAAY,CAACyB;oBAC/CxB,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0B0B,GAAG,CAACZ,kCAA9Bd,oCAAAA,8BAA4C0B,GAAG,CAACF,4BAAhDxB,kCAAyDkC,IAAI;YAC/D;QACF;IAEJ;AACF", "ignoreList": [0]}