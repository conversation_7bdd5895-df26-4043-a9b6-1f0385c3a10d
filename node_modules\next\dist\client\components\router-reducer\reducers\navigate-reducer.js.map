{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts"], "sourcesContent": ["import type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../../server/app-render/types'\nimport { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { invalidateCacheBelowFlightSegmentPath } from '../invalidate-cache-below-flight-segmentpath'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { shouldHardNavigate } from '../should-hard-navigate'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport {\n  PrefetchCacheEntryStatus,\n  type Mutable,\n  type NavigateAction,\n  type ReadonlyReducerState,\n  type ReducerState,\n} from '../router-reducer-types'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport { prefetchQueue } from './prefetch-reducer'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { DEFAULT_SEGMENT_KEY } from '../../../../shared/lib/segment'\nimport { listenForDynamicRequest, startPPRNavigation } from '../ppr-navigations'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nimport { clearCacheNodeDataForSegmentPath } from '../clear-cache-node-data-for-segment-path'\nimport { handleAliasedPrefetchEntry } from '../aliased-prefetch-navigations'\nimport {\n  navigate as navigateUsingSegmentCache,\n  NavigationResultTag,\n  type NavigationResult,\n} from '../../segment-cache'\n\nexport function handleExternalUrl(\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  url: string,\n  pendingPush: boolean\n) {\n  mutable.mpaNavigation = true\n  mutable.canonicalUrl = url\n  mutable.pendingPush = pendingPush\n  mutable.scrollableSegments = undefined\n\n  return handleMutable(state, mutable)\n}\n\nfunction generateSegmentsFromPatch(\n  flightRouterPatch: FlightRouterState\n): FlightSegmentPath[] {\n  const segments: FlightSegmentPath[] = []\n  const [segment, parallelRoutes] = flightRouterPatch\n\n  if (Object.keys(parallelRoutes).length === 0) {\n    return [[segment]]\n  }\n\n  for (const [parallelRouteKey, parallelRoute] of Object.entries(\n    parallelRoutes\n  )) {\n    for (const childSegment of generateSegmentsFromPatch(parallelRoute)) {\n      // If the segment is empty, it means we are at the root of the tree\n      if (segment === '') {\n        segments.push([parallelRouteKey, ...childSegment])\n      } else {\n        segments.push([segment, parallelRouteKey, ...childSegment])\n      }\n    }\n  }\n\n  return segments\n}\n\nfunction triggerLazyFetchForLeafSegments(\n  newCache: CacheNode,\n  currentCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath,\n  treePatch: FlightRouterState\n) {\n  let appliedPatch = false\n\n  newCache.rsc = currentCache.rsc\n  newCache.prefetchRsc = currentCache.prefetchRsc\n  newCache.loading = currentCache.loading\n  newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n  const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map(\n    (segment) => [...flightSegmentPath, ...segment]\n  )\n\n  for (const segmentPaths of segmentPathsToFill) {\n    clearCacheNodeDataForSegmentPath(newCache, currentCache, segmentPaths)\n\n    appliedPatch = true\n  }\n\n  return appliedPatch\n}\n\nfunction handleNavigationResult(\n  url: URL,\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  pendingPush: boolean,\n  result: NavigationResult\n): ReducerState {\n  switch (result.tag) {\n    case NavigationResultTag.MPA: {\n      // Perform an MPA navigation.\n      const newUrl = result.data\n      return handleExternalUrl(state, mutable, newUrl, pendingPush)\n    }\n    case NavigationResultTag.NoOp: {\n      // The server responded with no change to the current page. However, if\n      // the URL changed, we still need to update that.\n      const newCanonicalUrl = result.data.canonicalUrl\n      mutable.canonicalUrl = newCanonicalUrl\n\n      // Check if the only thing that changed was the hash fragment.\n      const oldUrl = new URL(state.canonicalUrl, url)\n      const onlyHashChange =\n        // We don't need to compare the origins, because client-driven\n        // navigations are always same-origin.\n        url.pathname === oldUrl.pathname &&\n        url.search === oldUrl.search &&\n        url.hash !== oldUrl.hash\n      if (onlyHashChange) {\n        // The only updated part of the URL is the hash.\n        mutable.onlyHashChange = true\n        mutable.shouldScroll = result.data.shouldScroll\n        mutable.hashFragment = url.hash\n        // Setting this to an empty array triggers a scroll for all new and\n        // updated segments. See `ScrollAndFocusHandler` for more details.\n        mutable.scrollableSegments = []\n      }\n\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Success: {\n      // Received a new result.\n      mutable.cache = result.data.cacheNode\n      mutable.patchedTree = result.data.flightRouterState\n      mutable.canonicalUrl = result.data.canonicalUrl\n      mutable.scrollableSegments = result.data.scrollableSegments\n      mutable.shouldScroll = result.data.shouldScroll\n      mutable.hashFragment = result.data.hash\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Async: {\n      return result.data.then(\n        (asyncResult) =>\n          handleNavigationResult(url, state, mutable, pendingPush, asyncResult),\n        // If the navigation failed, return the current state.\n        // TODO: This matches the current behavior but we need to do something\n        // better here if the network fails.\n        () => {\n          return state\n        }\n      )\n    }\n    default: {\n      result satisfies never\n      return state\n    }\n  }\n}\n\nexport function navigateReducer(\n  state: ReadonlyReducerState,\n  action: NavigateAction\n): ReducerState {\n  const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } =\n    action\n  const mutable: Mutable = {}\n  const { hash } = url\n  const href = createHrefFromUrl(url)\n  const pendingPush = navigateType === 'push'\n  // we want to prune the prefetch cache on every navigation to avoid it growing too large\n  prunePrefetchCache(state.prefetchCache)\n\n  mutable.preserveCustomHistoryState = false\n  mutable.pendingPush = pendingPush\n\n  if (isExternalUrl) {\n    return handleExternalUrl(state, mutable, url.toString(), pendingPush)\n  }\n\n  // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n  // which will trigger an MPA navigation.\n  if (document.getElementById('__next-page-redirect')) {\n    return handleExternalUrl(state, mutable, href, pendingPush)\n  }\n\n  if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // (Very Early Experimental Feature) Segment Cache\n    //\n    // Bypass the normal prefetch cache and use the new per-segment cache\n    // implementation instead. This is only supported if PPR is enabled, too.\n    //\n    // Temporary glue code between the router reducer and the new navigation\n    // implementation. Eventually we'll rewrite the router reducer to a\n    // state machine.\n    const result = navigateUsingSegmentCache(\n      url,\n      state.cache,\n      state.tree,\n      state.nextUrl,\n      shouldScroll\n    )\n    return handleNavigationResult(url, state, mutable, pendingPush, result)\n  }\n\n  const prefetchValues = getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    tree: state.tree,\n    prefetchCache: state.prefetchCache,\n    allowAliasing,\n  })\n  const { treeAtTimeOfPrefetch, data } = prefetchValues\n\n  prefetchQueue.bump(data)\n\n  return data.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride, postponed }) => {\n      const navigatedAt = Date.now()\n\n      let isFirstRead = false\n      // we only want to mark this once\n      if (!prefetchValues.lastUsedTime) {\n        // important: we should only mark the cache node as dirty after we unsuspend from the call above\n        prefetchValues.lastUsedTime = navigatedAt\n        isFirstRead = true\n      }\n\n      if (prefetchValues.aliased) {\n        // When alias is enabled, search param may not be included in the canonicalUrl.\n        // But we want to set url to canonicalUrl so that we use redirected path for fetching dynamic data.\n        const urlWithCanonicalPathname = new URL(url.href)\n        if (canonicalUrlOverride) {\n          urlWithCanonicalPathname.pathname = canonicalUrlOverride.pathname\n        }\n\n        const result = handleAliasedPrefetchEntry(\n          navigatedAt,\n          state,\n          flightData,\n          urlWithCanonicalPathname,\n          mutable\n        )\n\n        // We didn't return new router state because we didn't apply the aliased entry for some reason.\n        // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n        // will create an on-demand prefetch entry.\n        if (result === false) {\n          return navigateReducer(state, { ...action, allowAliasing: false })\n        }\n\n        return result\n      }\n\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(state, mutable, flightData, pendingPush)\n      }\n\n      const updatedCanonicalUrl = canonicalUrlOverride\n        ? createHrefFromUrl(canonicalUrlOverride)\n        : href\n\n      const onlyHashChange =\n        !!hash &&\n        state.canonicalUrl.split('#', 1)[0] ===\n          updatedCanonicalUrl.split('#', 1)[0]\n\n      // If only the hash has changed, the server hasn't sent us any new data. We can just update\n      // the mutable properties responsible for URL and scroll handling and return early.\n      if (onlyHashChange) {\n        mutable.onlyHashChange = true\n        mutable.canonicalUrl = updatedCanonicalUrl\n        mutable.shouldScroll = shouldScroll\n        mutable.hashFragment = hash\n        mutable.scrollableSegments = []\n        return handleMutable(state, mutable)\n      }\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n      let scrollableSegments: FlightSegmentPath[] = []\n      for (const normalizedFlightData of flightData) {\n        const {\n          pathToSegment: flightSegmentPath,\n          seedData,\n          head,\n          isHeadPartial,\n          isRootRender,\n        } = normalizedFlightData\n        let treePatch = normalizedFlightData.tree\n\n        // TODO-APP: remove ''\n        const flightSegmentPathWithLeadingEmpty = ['', ...flightSegmentPath]\n\n        // Create new tree based on the flightSegmentPath and router state patch\n        let newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          flightSegmentPathWithLeadingEmpty,\n          currentTree,\n          treePatch,\n          href\n        )\n\n        // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n        // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n        if (newTree === null) {\n          newTree = applyRouterStatePatchToTree(\n            // TODO-APP: remove ''\n            flightSegmentPathWithLeadingEmpty,\n            treeAtTimeOfPrefetch,\n            treePatch,\n            href\n          )\n        }\n\n        if (newTree !== null) {\n          if (\n            // This is just a paranoid check. When a route is PPRed, the server\n            // will send back a static response that's rendered from\n            // the root. If for some reason it doesn't, we fall back to the\n            // non-PPR implementation.\n            // TODO: We should get rid of the else branch and do all navigations\n            // via startPPRNavigation. The current structure is just\n            // an incremental step.\n            seedData &&\n            isRootRender &&\n            postponed\n          ) {\n            const task = startPPRNavigation(\n              navigatedAt,\n              currentCache,\n              currentTree,\n              treePatch,\n              seedData,\n              head,\n              isHeadPartial,\n              false,\n              scrollableSegments\n            )\n\n            if (task !== null) {\n              if (task.route === null) {\n                // Detected a change to the root layout. Perform an full-\n                // page navigation.\n                return handleExternalUrl(state, mutable, href, pendingPush)\n              }\n              // Use the tree computed by startPPRNavigation instead\n              // of the one computed by applyRouterStatePatchToTree.\n              // TODO: We should remove applyRouterStatePatchToTree\n              // from the PPR path entirely.\n              const patchedRouterState: FlightRouterState = task.route\n              newTree = patchedRouterState\n\n              const newCache = task.node\n              if (newCache !== null) {\n                // We've created a new Cache Node tree that contains a prefetched\n                // version of the next page. This can be rendered instantly.\n                mutable.cache = newCache\n              }\n              const dynamicRequestTree = task.dynamicRequestTree\n              if (dynamicRequestTree !== null) {\n                // The prefetched tree has dynamic holes in it. We initiate a\n                // dynamic request to fill them in.\n                //\n                // Do not block on the result. We'll immediately render the Cache\n                // Node tree and suspend on the dynamic parts. When the request\n                // comes in, we'll fill in missing data and ping React to\n                // re-render. Unlike the lazy fetching model in the non-PPR\n                // implementation, this is modeled as a single React update +\n                // streaming, rather than multiple top-level updates. (However,\n                // even in the new model, we'll still need to sometimes update the\n                // root multiple times per navigation, like if the server sends us\n                // a different response than we expected. For now, we revert back\n                // to the lazy fetching mechanism in that case.)\n                const dynamicRequest = fetchServerResponse(\n                  new URL(updatedCanonicalUrl, url.origin),\n                  {\n                    flightRouterState: dynamicRequestTree,\n                    nextUrl: state.nextUrl,\n                  }\n                )\n\n                listenForDynamicRequest(task, dynamicRequest)\n                // We store the dynamic request on the `lazyData` property of the CacheNode\n                // because we're not going to await the dynamic request here. Since we're not blocking\n                // on the dynamic request, `layout-router` will\n                // task.node.lazyData = dynamicRequest\n              } else {\n                // The prefetched tree does not contain dynamic holes — it's\n                // fully static. We can skip the dynamic request.\n              }\n            } else {\n              // Nothing changed, so reuse the old cache.\n              // TODO: What if the head changed but not any of the segment data?\n              // Is that possible? If so, we should clone the whole tree and\n              // update the head.\n              newTree = treePatch\n            }\n          } else {\n            // The static response does not include any dynamic holes, so\n            // there's no need to do a second request.\n            // TODO: As an incremental step this just reverts back to the\n            // non-PPR implementation. We can simplify this branch further,\n            // given that PPR prefetches are always static and return the whole\n            // tree. Or in the meantime we could factor it out into a\n            // separate function.\n\n            if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n              return handleExternalUrl(state, mutable, href, pendingPush)\n            }\n\n            const cache: CacheNode = createEmptyCacheNode()\n            let applied = false\n\n            if (\n              prefetchValues.status === PrefetchCacheEntryStatus.stale &&\n              !isFirstRead\n            ) {\n              // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n              // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n              // while copying over the `loading` for the segment that contains the page data.\n              // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n\n              // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n              applied = triggerLazyFetchForLeafSegments(\n                cache,\n                currentCache,\n                flightSegmentPath,\n                treePatch\n              )\n              // since we re-used the stale cache's loading state & refreshed the data,\n              // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n              prefetchValues.lastUsedTime = navigatedAt\n            } else {\n              applied = applyFlightData(\n                navigatedAt,\n                currentCache,\n                cache,\n                normalizedFlightData,\n                prefetchValues\n              )\n            }\n\n            const hardNavigate = shouldHardNavigate(\n              // TODO-APP: remove ''\n              flightSegmentPathWithLeadingEmpty,\n              currentTree\n            )\n\n            if (hardNavigate) {\n              // Copy rsc for the root node of the cache.\n              cache.rsc = currentCache.rsc\n              cache.prefetchRsc = currentCache.prefetchRsc\n\n              invalidateCacheBelowFlightSegmentPath(\n                cache,\n                currentCache,\n                flightSegmentPath\n              )\n              // Ensure the existing cache value is used when the cache was not invalidated.\n              mutable.cache = cache\n            } else if (applied) {\n              mutable.cache = cache\n              // If we applied the cache, we update the \"current cache\" value so any other\n              // segments in the FlightDataPath will be able to reference the updated cache.\n              currentCache = cache\n            }\n\n            for (const subSegment of generateSegmentsFromPatch(treePatch)) {\n              const scrollableSegmentPath = [\n                ...flightSegmentPath,\n                ...subSegment,\n              ]\n              // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n              if (\n                scrollableSegmentPath[scrollableSegmentPath.length - 1] !==\n                DEFAULT_SEGMENT_KEY\n              ) {\n                scrollableSegments.push(scrollableSegmentPath)\n              }\n            }\n          }\n\n          currentTree = newTree\n        }\n      }\n\n      mutable.patchedTree = currentTree\n      mutable.canonicalUrl = updatedCanonicalUrl\n      mutable.scrollableSegments = scrollableSegments\n      mutable.hashFragment = hash\n      mutable.shouldScroll = shouldScroll\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n"], "names": ["handleExternalUrl", "navigateReducer", "state", "mutable", "url", "pendingPush", "mpaNavigation", "canonicalUrl", "scrollableSegments", "undefined", "handleMutable", "generateSegmentsFromPatch", "flightRouterPatch", "segments", "segment", "parallelRoutes", "Object", "keys", "length", "parallelRouteKey", "parallelRoute", "entries", "childSegment", "push", "triggerLazyFetchForLeafSegments", "newCache", "currentCache", "flightSegmentPath", "treePatch", "appliedPatch", "rsc", "prefetchRsc", "loading", "Map", "segmentPathsToFill", "map", "segmentPaths", "clearCacheNodeDataForSegmentPath", "handleNavigationResult", "result", "tag", "NavigationResultTag", "MPA", "newUrl", "data", "NoOp", "newCanonicalUrl", "oldUrl", "URL", "onlyHashChange", "pathname", "search", "hash", "shouldScroll", "hashFragment", "Success", "cache", "cacheNode", "patchedTree", "flightRouterState", "Async", "then", "asyncResult", "action", "isExternalUrl", "navigateType", "allowAliasing", "href", "createHrefFromUrl", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "preserveCustomHistoryState", "toString", "document", "getElementById", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "navigateUsingSegmentCache", "tree", "nextUrl", "prefetchValues", "getOrCreatePrefetchCacheEntry", "treeAtTimeOfPrefetch", "prefetchQueue", "bump", "flightData", "canonicalUrlOverride", "postponed", "navigatedAt", "Date", "now", "isFirstRead", "lastUsedTime", "aliased", "urlWithCanonicalPathname", "handleAliasedPrefetchEntry", "updatedCanonicalUrl", "split", "currentTree", "normalizedFlightData", "pathToSegment", "seedData", "head", "isHeadPartial", "isRootRender", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "task", "startPPRNavigation", "route", "patchedRouterState", "node", "dynamicRequestTree", "dynamicRequest", "fetchServerResponse", "origin", "listenForDynamicRequest", "isNavigatingToNewRootLayout", "createEmptyCacheNode", "applied", "status", "PrefetchCacheEntryStatus", "stale", "applyFlightData", "hardNavigate", "shouldHardNavigate", "invalidateCacheBelowFlightSegmentPath", "subSegment", "scrollableSegmentPath", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;IAoCgBA,iBAAiB;eAAjBA;;IAsIAC,eAAe;eAAfA;;;qCArKoB;mCACF;uDACoB;6CACV;oCACT;6CACS;oCAOrC;+BACuB;iCACE;iCACF;2BACO;yBACD;gCACwB;oCAIrD;kDAC0C;4CACN;8BAKpC;AAEA,SAASD,kBACdE,KAA2B,EAC3BC,OAAgB,EAChBC,GAAW,EACXC,WAAoB;IAEpBF,QAAQG,aAAa,GAAG;IACxBH,QAAQI,YAAY,GAAGH;IACvBD,QAAQE,WAAW,GAAGA;IACtBF,QAAQK,kBAAkB,GAAGC;IAE7B,OAAOC,IAAAA,4BAAa,EAACR,OAAOC;AAC9B;AAEA,SAASQ,0BACPC,iBAAoC;IAEpC,MAAMC,WAAgC,EAAE;IACxC,MAAM,CAACC,SAASC,eAAe,GAAGH;IAElC,IAAII,OAAOC,IAAI,CAACF,gBAAgBG,MAAM,KAAK,GAAG;QAC5C,OAAO;YAAC;gBAACJ;aAAQ;SAAC;IACpB;IAEA,KAAK,MAAM,CAACK,kBAAkBC,cAAc,IAAIJ,OAAOK,OAAO,CAC5DN,gBACC;QACD,KAAK,MAAMO,gBAAgBX,0BAA0BS,eAAgB;YACnE,mEAAmE;YACnE,IAAIN,YAAY,IAAI;gBAClBD,SAASU,IAAI,CAAC;oBAACJ;uBAAqBG;iBAAa;YACnD,OAAO;gBACLT,SAASU,IAAI,CAAC;oBAACT;oBAASK;uBAAqBG;iBAAa;YAC5D;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASW,gCACPC,QAAmB,EACnBC,YAAuB,EACvBC,iBAAoC,EACpCC,SAA4B;IAE5B,IAAIC,eAAe;IAEnBJ,SAASK,GAAG,GAAGJ,aAAaI,GAAG;IAC/BL,SAASM,WAAW,GAAGL,aAAaK,WAAW;IAC/CN,SAASO,OAAO,GAAGN,aAAaM,OAAO;IACvCP,SAASV,cAAc,GAAG,IAAIkB,IAAIP,aAAaX,cAAc;IAE7D,MAAMmB,qBAAqBvB,0BAA0BiB,WAAWO,GAAG,CACjE,CAACrB,UAAY;eAAIa;eAAsBb;SAAQ;IAGjD,KAAK,MAAMsB,gBAAgBF,mBAAoB;QAC7CG,IAAAA,kEAAgC,EAACZ,UAAUC,cAAcU;QAEzDP,eAAe;IACjB;IAEA,OAAOA;AACT;AAEA,SAASS,uBACPlC,GAAQ,EACRF,KAA2B,EAC3BC,OAAgB,EAChBE,WAAoB,EACpBkC,MAAwB;IAExB,OAAQA,OAAOC,GAAG;QAChB,KAAKC,iCAAmB,CAACC,GAAG;YAAE;gBAC5B,6BAA6B;gBAC7B,MAAMC,SAASJ,OAAOK,IAAI;gBAC1B,OAAO5C,kBAAkBE,OAAOC,SAASwC,QAAQtC;YACnD;QACA,KAAKoC,iCAAmB,CAACI,IAAI;YAAE;gBAC7B,uEAAuE;gBACvE,iDAAiD;gBACjD,MAAMC,kBAAkBP,OAAOK,IAAI,CAACrC,YAAY;gBAChDJ,QAAQI,YAAY,GAAGuC;gBAEvB,8DAA8D;gBAC9D,MAAMC,SAAS,IAAIC,IAAI9C,MAAMK,YAAY,EAAEH;gBAC3C,MAAM6C,iBACJ,8DAA8D;gBAC9D,sCAAsC;gBACtC7C,IAAI8C,QAAQ,KAAKH,OAAOG,QAAQ,IAChC9C,IAAI+C,MAAM,KAAKJ,OAAOI,MAAM,IAC5B/C,IAAIgD,IAAI,KAAKL,OAAOK,IAAI;gBAC1B,IAAIH,gBAAgB;oBAClB,gDAAgD;oBAChD9C,QAAQ8C,cAAc,GAAG;oBACzB9C,QAAQkD,YAAY,GAAGd,OAAOK,IAAI,CAACS,YAAY;oBAC/ClD,QAAQmD,YAAY,GAAGlD,IAAIgD,IAAI;oBAC/B,mEAAmE;oBACnE,kEAAkE;oBAClEjD,QAAQK,kBAAkB,GAAG,EAAE;gBACjC;gBAEA,OAAOE,IAAAA,4BAAa,EAACR,OAAOC;YAC9B;QACA,KAAKsC,iCAAmB,CAACc,OAAO;YAAE;gBAChC,yBAAyB;gBACzBpD,QAAQqD,KAAK,GAAGjB,OAAOK,IAAI,CAACa,SAAS;gBACrCtD,QAAQuD,WAAW,GAAGnB,OAAOK,IAAI,CAACe,iBAAiB;gBACnDxD,QAAQI,YAAY,GAAGgC,OAAOK,IAAI,CAACrC,YAAY;gBAC/CJ,QAAQK,kBAAkB,GAAG+B,OAAOK,IAAI,CAACpC,kBAAkB;gBAC3DL,QAAQkD,YAAY,GAAGd,OAAOK,IAAI,CAACS,YAAY;gBAC/ClD,QAAQmD,YAAY,GAAGf,OAAOK,IAAI,CAACQ,IAAI;gBACvC,OAAO1C,IAAAA,4BAAa,EAACR,OAAOC;YAC9B;QACA,KAAKsC,iCAAmB,CAACmB,KAAK;YAAE;gBAC9B,OAAOrB,OAAOK,IAAI,CAACiB,IAAI,CACrB,CAACC,cACCxB,uBAAuBlC,KAAKF,OAAOC,SAASE,aAAayD,cAC3D,sDAAsD;gBACtD,sEAAsE;gBACtE,oCAAoC;gBACpC;oBACE,OAAO5D;gBACT;YAEJ;QACA;YAAS;gBACPqC;gBACA,OAAOrC;YACT;IACF;AACF;AAEO,SAASD,gBACdC,KAA2B,EAC3B6D,MAAsB;IAEtB,MAAM,EAAE3D,GAAG,EAAE4D,aAAa,EAAEC,YAAY,EAAEZ,YAAY,EAAEa,aAAa,EAAE,GACrEH;IACF,MAAM5D,UAAmB,CAAC;IAC1B,MAAM,EAAEiD,IAAI,EAAE,GAAGhD;IACjB,MAAM+D,OAAOC,IAAAA,oCAAiB,EAAChE;IAC/B,MAAMC,cAAc4D,iBAAiB;IACrC,wFAAwF;IACxFI,IAAAA,sCAAkB,EAACnE,MAAMoE,aAAa;IAEtCnE,QAAQoE,0BAA0B,GAAG;IACrCpE,QAAQE,WAAW,GAAGA;IAEtB,IAAI2D,eAAe;QACjB,OAAOhE,kBAAkBE,OAAOC,SAASC,IAAIoE,QAAQ,IAAInE;IAC3D;IAEA,mEAAmE;IACnE,wCAAwC;IACxC,IAAIoE,SAASC,cAAc,CAAC,yBAAyB;QACnD,OAAO1E,kBAAkBE,OAAOC,SAASgE,MAAM9D;IACjD;IAEA,IAAIsE,QAAQC,GAAG,CAACC,2BAA2B,EAAE;QAC3C,kDAAkD;QAClD,EAAE;QACF,qEAAqE;QACrE,yEAAyE;QACzE,EAAE;QACF,wEAAwE;QACxE,mEAAmE;QACnE,iBAAiB;QACjB,MAAMtC,SAASuC,IAAAA,sBAAyB,EACtC1E,KACAF,MAAMsD,KAAK,EACXtD,MAAM6E,IAAI,EACV7E,MAAM8E,OAAO,EACb3B;QAEF,OAAOf,uBAAuBlC,KAAKF,OAAOC,SAASE,aAAakC;IAClE;IAEA,MAAM0C,iBAAiBC,IAAAA,iDAA6B,EAAC;QACnD9E;QACA4E,SAAS9E,MAAM8E,OAAO;QACtBD,MAAM7E,MAAM6E,IAAI;QAChBT,eAAepE,MAAMoE,aAAa;QAClCJ;IACF;IACA,MAAM,EAAEiB,oBAAoB,EAAEvC,IAAI,EAAE,GAAGqC;IAEvCG,8BAAa,CAACC,IAAI,CAACzC;IAEnB,OAAOA,KAAKiB,IAAI,CACd;YAAC,EAAEyB,UAAU,EAAE/E,cAAcgF,oBAAoB,EAAEC,SAAS,EAAE;QAC5D,MAAMC,cAAcC,KAAKC,GAAG;QAE5B,IAAIC,cAAc;QAClB,iCAAiC;QACjC,IAAI,CAACX,eAAeY,YAAY,EAAE;YAChC,gGAAgG;YAChGZ,eAAeY,YAAY,GAAGJ;YAC9BG,cAAc;QAChB;QAEA,IAAIX,eAAea,OAAO,EAAE;YAC1B,+EAA+E;YAC/E,mGAAmG;YACnG,MAAMC,2BAA2B,IAAI/C,IAAI5C,IAAI+D,IAAI;YACjD,IAAIoB,sBAAsB;gBACxBQ,yBAAyB7C,QAAQ,GAAGqC,qBAAqBrC,QAAQ;YACnE;YAEA,MAAMX,SAASyD,IAAAA,sDAA0B,EACvCP,aACAvF,OACAoF,YACAS,0BACA5F;YAGF,+FAA+F;YAC/F,yGAAyG;YACzG,2CAA2C;YAC3C,IAAIoC,WAAW,OAAO;gBACpB,OAAOtC,gBAAgBC,OAAO;oBAAE,GAAG6D,MAAM;oBAAEG,eAAe;gBAAM;YAClE;YAEA,OAAO3B;QACT;QAEA,4DAA4D;QAC5D,IAAI,OAAO+C,eAAe,UAAU;YAClC,OAAOtF,kBAAkBE,OAAOC,SAASmF,YAAYjF;QACvD;QAEA,MAAM4F,sBAAsBV,uBACxBnB,IAAAA,oCAAiB,EAACmB,wBAClBpB;QAEJ,MAAMlB,iBACJ,CAAC,CAACG,QACFlD,MAAMK,YAAY,CAAC2F,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,KACjCD,oBAAoBC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;QAExC,2FAA2F;QAC3F,mFAAmF;QACnF,IAAIjD,gBAAgB;YAClB9C,QAAQ8C,cAAc,GAAG;YACzB9C,QAAQI,YAAY,GAAG0F;YACvB9F,QAAQkD,YAAY,GAAGA;YACvBlD,QAAQmD,YAAY,GAAGF;YACvBjD,QAAQK,kBAAkB,GAAG,EAAE;YAC/B,OAAOE,IAAAA,4BAAa,EAACR,OAAOC;QAC9B;QAEA,IAAIgG,cAAcjG,MAAM6E,IAAI;QAC5B,IAAIrD,eAAexB,MAAMsD,KAAK;QAC9B,IAAIhD,qBAA0C,EAAE;QAChD,KAAK,MAAM4F,wBAAwBd,WAAY;YAC7C,MAAM,EACJe,eAAe1E,iBAAiB,EAChC2E,QAAQ,EACRC,IAAI,EACJC,aAAa,EACbC,YAAY,EACb,GAAGL;YACJ,IAAIxE,YAAYwE,qBAAqBrB,IAAI;YAEzC,sBAAsB;YACtB,MAAM2B,oCAAoC;gBAAC;mBAAO/E;aAAkB;YAEpE,wEAAwE;YACxE,IAAIgF,UAAUC,IAAAA,wDAA2B,EACvC,sBAAsB;YACtBF,mCACAP,aACAvE,WACAuC;YAGF,kGAAkG;YAClG,6IAA6I;YAC7I,IAAIwC,YAAY,MAAM;gBACpBA,UAAUC,IAAAA,wDAA2B,EACnC,sBAAsB;gBACtBF,mCACAvB,sBACAvD,WACAuC;YAEJ;YAEA,IAAIwC,YAAY,MAAM;gBACpB,IACE,mEAAmE;gBACnE,wDAAwD;gBACxD,+DAA+D;gBAC/D,0BAA0B;gBAC1B,oEAAoE;gBACpE,wDAAwD;gBACxD,uBAAuB;gBACvBL,YACAG,gBACAjB,WACA;oBACA,MAAMqB,OAAOC,IAAAA,kCAAkB,EAC7BrB,aACA/D,cACAyE,aACAvE,WACA0E,UACAC,MACAC,eACA,OACAhG;oBAGF,IAAIqG,SAAS,MAAM;wBACjB,IAAIA,KAAKE,KAAK,KAAK,MAAM;4BACvB,yDAAyD;4BACzD,mBAAmB;4BACnB,OAAO/G,kBAAkBE,OAAOC,SAASgE,MAAM9D;wBACjD;wBACA,sDAAsD;wBACtD,sDAAsD;wBACtD,qDAAqD;wBACrD,8BAA8B;wBAC9B,MAAM2G,qBAAwCH,KAAKE,KAAK;wBACxDJ,UAAUK;wBAEV,MAAMvF,WAAWoF,KAAKI,IAAI;wBAC1B,IAAIxF,aAAa,MAAM;4BACrB,iEAAiE;4BACjE,4DAA4D;4BAC5DtB,QAAQqD,KAAK,GAAG/B;wBAClB;wBACA,MAAMyF,qBAAqBL,KAAKK,kBAAkB;wBAClD,IAAIA,uBAAuB,MAAM;4BAC/B,6DAA6D;4BAC7D,mCAAmC;4BACnC,EAAE;4BACF,iEAAiE;4BACjE,+DAA+D;4BAC/D,yDAAyD;4BACzD,2DAA2D;4BAC3D,6DAA6D;4BAC7D,+DAA+D;4BAC/D,kEAAkE;4BAClE,kEAAkE;4BAClE,iEAAiE;4BACjE,gDAAgD;4BAChD,MAAMC,iBAAiBC,IAAAA,wCAAmB,EACxC,IAAIpE,IAAIiD,qBAAqB7F,IAAIiH,MAAM,GACvC;gCACE1D,mBAAmBuD;gCACnBlC,SAAS9E,MAAM8E,OAAO;4BACxB;4BAGFsC,IAAAA,uCAAuB,EAACT,MAAMM;wBAC9B,2EAA2E;wBAC3E,sFAAsF;wBACtF,+CAA+C;wBAC/C,sCAAsC;wBACxC,OAAO;wBACL,4DAA4D;wBAC5D,iDAAiD;wBACnD;oBACF,OAAO;wBACL,2CAA2C;wBAC3C,kEAAkE;wBAClE,8DAA8D;wBAC9D,mBAAmB;wBACnBR,UAAU/E;oBACZ;gBACF,OAAO;oBACL,6DAA6D;oBAC7D,0CAA0C;oBAC1C,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mEAAmE;oBACnE,yDAAyD;oBACzD,qBAAqB;oBAErB,IAAI2F,IAAAA,wDAA2B,EAACpB,aAAaQ,UAAU;wBACrD,OAAO3G,kBAAkBE,OAAOC,SAASgE,MAAM9D;oBACjD;oBAEA,MAAMmD,QAAmBgE,IAAAA,+BAAoB;oBAC7C,IAAIC,UAAU;oBAEd,IACExC,eAAeyC,MAAM,KAAKC,4CAAwB,CAACC,KAAK,IACxD,CAAChC,aACD;wBACA,yJAAyJ;wBACzJ,uHAAuH;wBACvH,gFAAgF;wBAChF,0FAA0F;wBAE1F,mHAAmH;wBACnH6B,UAAUjG,gCACRgC,OACA9B,cACAC,mBACAC;wBAEF,yEAAyE;wBACzE,mFAAmF;wBACnFqD,eAAeY,YAAY,GAAGJ;oBAChC,OAAO;wBACLgC,UAAUI,IAAAA,gCAAe,EACvBpC,aACA/D,cACA8B,OACA4C,sBACAnB;oBAEJ;oBAEA,MAAM6C,eAAeC,IAAAA,sCAAkB,EACrC,sBAAsB;oBACtBrB,mCACAP;oBAGF,IAAI2B,cAAc;wBAChB,2CAA2C;wBAC3CtE,MAAM1B,GAAG,GAAGJ,aAAaI,GAAG;wBAC5B0B,MAAMzB,WAAW,GAAGL,aAAaK,WAAW;wBAE5CiG,IAAAA,4EAAqC,EACnCxE,OACA9B,cACAC;wBAEF,8EAA8E;wBAC9ExB,QAAQqD,KAAK,GAAGA;oBAClB,OAAO,IAAIiE,SAAS;wBAClBtH,QAAQqD,KAAK,GAAGA;wBAChB,4EAA4E;wBAC5E,8EAA8E;wBAC9E9B,eAAe8B;oBACjB;oBAEA,KAAK,MAAMyE,cAActH,0BAA0BiB,WAAY;wBAC7D,MAAMsG,wBAAwB;+BACzBvG;+BACAsG;yBACJ;wBACD,kFAAkF;wBAClF,IACEC,qBAAqB,CAACA,sBAAsBhH,MAAM,GAAG,EAAE,KACvDiH,4BAAmB,EACnB;4BACA3H,mBAAmBe,IAAI,CAAC2G;wBAC1B;oBACF;gBACF;gBAEA/B,cAAcQ;YAChB;QACF;QAEAxG,QAAQuD,WAAW,GAAGyC;QACtBhG,QAAQI,YAAY,GAAG0F;QACvB9F,QAAQK,kBAAkB,GAAGA;QAC7BL,QAAQmD,YAAY,GAAGF;QACvBjD,QAAQkD,YAAY,GAAGA;QAEvB,OAAO3C,IAAAA,4BAAa,EAACR,OAAOC;IAC9B,GACA,IAAMD;AAEV", "ignoreList": [0]}