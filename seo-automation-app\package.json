{"name": "seo-automation-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "prepare": "husky", "migrate": "node scripts/run-migrations.js", "db:setup": "npm run migrate", "analyze": "ANALYZE=true npm run build", "build:analyze": "cross-env ANALYZE=true npm run build"}, "dependencies": {"@google-cloud/language": "^7.2.0", "@hookform/resolvers": "^5.1.1", "@mendable/firecrawl-js": "^1.29.1", "@next/bundle-analyzer": "^15.4.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@sentry/nextjs": "^9.39.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.51.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.10.0", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compromise": "^14.14.4", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "ioredis": "^5.6.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "natural": "^8.1.0", "next": "15.4.1", "openai": "^5.9.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "sentiment": "^5.0.2", "tailwind-merge": "^3.3.1", "turndown": "^7.2.0", "web-vitals": "^5.0.3", "webpack-bundle-analyzer": "^4.10.2", "xml2js": "^0.6.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@tanstack/react-query": "^5.83.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.4.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "supabase": "^2.31.4", "tailwindcss": "^4", "typescript": "^5"}}