# Story 2.3: SEO Metrics Analysis Engine

## Status
In Progress

## Story
**As a** content strategist,
**I want** detailed SEO metrics analysis from all competitor pages,
**so that** I can understand the optimization patterns that drive search rankings.

## Acceptance Criteria
1. Word count analysis provides accurate content length measurements for each competitor page
2. Keyword density calculation measures primary keyword and variation frequency throughout content
3. Heading optimization analysis identifies which headings contain target keywords and variations
4. LSI keyword extraction identifies semantically related terms and phrases used by competitors
5. Entity recognition extracts important people, places, organizations, and concepts mentioned
6. Content structure analysis maps topic flow and identifies content patterns across competitors
7. Meta tag analysis extracts title tags, meta descriptions, and other SEO elements

## Tasks / Subtasks
- [x] Build word count analysis system (AC: 1)
  - [x] Create accurate word counting algorithms
  - [x] Implement content length measurement tools
  - [x] Build word density and distribution analysis
  - [ ] Create content length comparison across competitors
  - [x] Add content depth and complexity scoring
- [x] Implement keyword density calculation (AC: 2)
  - [x] Create primary keyword frequency analysis
  - [x] Build keyword variation detection and counting
  - [x] Implement keyword density percentage calculations
  - [x] Create keyword distribution mapping throughout content
  - [x] Add keyword prominence scoring (title, headings, first paragraph)
- [x] Build heading optimization analysis (AC: 3)
  - [x] Create heading keyword detection system
  - [x] Implement heading optimization scoring
  - [x] Build heading hierarchy analysis
  - [x] Create heading keyword variation tracking
  - [x] Add heading length and structure analysis
- [x] Implement LSI keyword extraction (AC: 4)
  - [x] Create semantic keyword detection algorithms
  - [x] Build related term identification system
  - [x] Implement contextual keyword analysis
  - [x] Create LSI keyword frequency tracking
  - [ ] Add semantic relationship mapping
- [x] Build entity recognition system (AC: 5)
  - [x] Integrate Google Natural Language API for entity extraction
  - [ ] Create custom entity recognition for industry-specific terms
  - [ ] Build entity categorization (person, place, organization, concept)
  - [ ] Implement entity frequency and prominence analysis
  - [ ] Create entity relationship mapping
- [x] Implement content structure analysis (AC: 6)
  - [x] Create topic flow mapping algorithms
  - [x] Build content section analysis
  - [x] Implement content pattern recognition
  - [x] Create content organization scoring
  - [x] Add content coherence and flow analysis
- [x] Build meta tag analysis system (AC: 7)
  - [x] Create title tag extraction and analysis
  - [x] Implement meta description analysis
  - [x] Build meta keyword extraction (if present)
  - [x] Create Open Graph and Twitter Card analysis
  - [x] Add structured data and schema markup detection
- [x] Create SEO metrics calculation engine (AC: 1-7)
  - [x] Build comprehensive SEO scoring algorithms
  - [ ] Create competitor benchmark calculations
  - [ ] Implement metrics aggregation and averaging
  - [x] Build SEO optimization recommendations
  - [ ] Create metrics comparison and ranking system
- [ ] Implement metrics storage and retrieval (AC: 1-7)
  - [ ] Design database schema for SEO metrics
  - [ ] Create metrics data models and interfaces
  - [ ] Build metrics storage and indexing system
  - [ ] Implement metrics search and filtering
  - [ ] Add metrics history and trend tracking
- [x] Create SEO metrics API endpoints (AC: 1-7)
  - [x] Build POST /api/seo/analyze endpoint
  - [ ] Create GET /api/seo/metrics/{id} endpoint
  - [ ] Implement metrics comparison endpoints
  - [ ] Add batch metrics processing
  - [ ] Create metrics export and reporting functionality

## Dev Notes

### Previous Story Insights
Stories 2.1-2.2 established SERP analysis and content extraction. This story builds the SEO metrics engine that analyzes competitor optimization patterns.

### SEO Metrics Analysis Architecture
[Source: PRD.md#functional-requirements]
- **Word Count Analysis**: Accurate content length measurements
- **Keyword Density**: Primary keyword and variation frequency
- **Heading Optimization**: Keyword usage in H1-H6 tags
- **LSI Keywords**: Semantic term identification and analysis
- **Entity Recognition**: People, places, organizations, concepts
- **Content Structure**: Topic flow and organization patterns

### Keyword Density Calculation
[Source: PRD.md#functional-requirements]
```typescript
class KeywordAnalyzer {
  calculateDensity(content: string, keyword: string): number {
    const words = content.toLowerCase().split(/\s+/);
    const keywordCount = words.filter(word => 
      word.includes(keyword.toLowerCase())
    ).length;
    
    return (keywordCount / words.length) * 100;
  }
  
  findKeywordVariations(content: string, keyword: string): string[] {
    // Implement stemming and variation detection
    return variations;
  }
}
```

### LSI Keyword Extraction
[Source: PRD.md#functional-requirements]
- **Semantic Analysis**: Identify contextually related terms
- **Co-occurrence Analysis**: Find terms that appear together
- **TF-IDF Scoring**: Weight terms by importance
- **Contextual Relevance**: Assess semantic relationships

### Entity Recognition Integration
[Source: architecture.md#ai-ml-stack]
```typescript
import { LanguageServiceClient } from '@google-cloud/language';

class EntityAnalyzer {
  private client = new LanguageServiceClient();
  
  async extractEntities(content: string): Promise<Entity[]> {
    const [result] = await this.client.analyzeEntities({
      document: {
        content: content,
        type: 'PLAIN_TEXT',
      },
    });
    
    return result.entities.map(entity => ({
      name: entity.name,
      type: entity.type,
      salience: entity.salience,
      mentions: entity.mentions
    }));
  }
}
```

### Database Schema for SEO Metrics
[Source: architecture.md#database-schema]
```sql
-- Extend competitor_analysis table
ALTER TABLE competitor_analysis ADD COLUMN seo_metrics JSONB;

-- SEO metrics structure
{
  "word_count": 1500,
  "keyword_density": {
    "primary": 2.5,
    "variations": [
      {"term": "seo content", "density": 1.2},
      {"term": "content optimization", "density": 0.8}
    ]
  },
  "heading_optimization": {
    "total_headings": 12,
    "optimized_headings": 8,
    "optimization_score": 66.7
  },
  "lsi_keywords": [
    {"term": "search engine", "frequency": 15, "relevance": 0.85},
    {"term": "content marketing", "frequency": 8, "relevance": 0.72}
  ],
  "entities": [
    {"name": "Google", "type": "ORGANIZATION", "salience": 0.45},
    {"name": "SEO", "type": "OTHER", "salience": 0.38}
  ]
}
```

### Content Structure Analysis
[Source: PRD.md#functional-requirements]
- **Topic Flow Mapping**: Analyze content progression
- **Section Analysis**: Identify content organization patterns
- **Coherence Scoring**: Measure content logical flow
- **Pattern Recognition**: Identify successful content structures

### Meta Tag Analysis System
[Source: PRD.md#functional-requirements]
```typescript
class MetaAnalyzer {
  extractMetaTags(html: string): MetaTags {
    const $ = cheerio.load(html);
    
    return {
      title: $('title').text(),
      description: $('meta[name="description"]').attr('content'),
      keywords: $('meta[name="keywords"]').attr('content'),
      ogTitle: $('meta[property="og:title"]').attr('content'),
      ogDescription: $('meta[property="og:description"]').attr('content'),
      twitterTitle: $('meta[name="twitter:title"]').attr('content'),
      canonicalUrl: $('link[rel="canonical"]').attr('href')
    };
  }
}
```

### SEO Scoring Algorithms
[Source: PRD.md#functional-requirements]
- **Keyword Optimization Score**: Based on density and placement
- **Content Quality Score**: Length, structure, readability
- **Technical SEO Score**: Meta tags, headings, structure
- **Competitive Score**: Comparison with top competitors

### Heading Optimization Analysis
[Source: PRD.md#functional-requirements]
```typescript
class HeadingAnalyzer {
  analyzeHeadingOptimization(headings: Heading[], keyword: string): HeadingAnalysis {
    const optimizedHeadings = headings.filter(h => 
      h.text.toLowerCase().includes(keyword.toLowerCase())
    );
    
    return {
      totalHeadings: headings.length,
      optimizedHeadings: optimizedHeadings.length,
      optimizationScore: (optimizedHeadings.length / headings.length) * 100,
      headingDistribution: this.analyzeHeadingDistribution(headings),
      keywordPlacement: this.analyzeKeywordPlacement(optimizedHeadings)
    };
  }
}
```

### File Locations
[Source: architecture.md#frontend-application-structure]
- SEO analysis: `lib/seo/seo-analyzer.ts`
- Keyword analysis: `lib/seo/keyword-analyzer.ts`
- Entity recognition: `lib/seo/entity-analyzer.ts`
- API endpoints: `app/api/seo/`
- Data models: `types/seo.ts`

### Required Dependencies
- @google-cloud/language (entity recognition)
- natural (text processing and NLP)
- cheerio (HTML parsing)
- stemmer (keyword stemming)
- stopword (stop word removal)

### Environment Variables
- GOOGLE_CLOUD_PROJECT_ID
- GOOGLE_CLOUD_KEY_FILE
- OPENAI_API_KEY (for advanced NLP)

### Performance Optimization
- **Batch Processing**: Analyze multiple pages concurrently
- **Caching**: Cache analysis results for repeated requests
- **Streaming**: Process large content in chunks
- **Parallel Analysis**: Run different metrics calculations simultaneously

### Accuracy Considerations
[Source: PRD.md#non-functional-requirements]
- **99.9% Accuracy**: Keyword density calculations within 0.1% variance
- **Comprehensive Analysis**: All relevant SEO metrics captured
- **Consistent Results**: Reproducible analysis across runs
- **Quality Validation**: Verify analysis accuracy against manual checks

### Security Considerations
[Source: architecture.md#security-implementation]
- **Content Sanitization**: Clean analyzed content for security
- **API Rate Limiting**: Prevent abuse of analysis endpoints
- **Data Privacy**: Secure handling of competitor content
- **Access Control**: Restrict analysis to authorized users

### Testing Standards
- Unit tests for all analysis algorithms
- Integration tests for Google NLP API
- Accuracy tests against known benchmarks
- Performance tests for large content analysis
- Mock external services in tests
- Validate metrics calculations with manual verification

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-16 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

### Debug Log References

### Completion Notes List

### File List
- src/lib/seo/seo-analyzer.ts (Created)
- src/lib/seo/keyword-analyzer.ts (Created)
- src/lib/seo/entity-analyzer.ts (Created)
- src/lib/seo/word-count.ts (Created)
- src/lib/seo/heading-analyzer.ts (Created)
- src/lib/seo/lsi-keyword-extractor.ts (Created)
- src/lib/seo/meta-tag-analyzer.ts (Created)
- src/types/seo.ts (Created)
- src/app/api/seo/analyze/route.ts (Created)

## QA Results
