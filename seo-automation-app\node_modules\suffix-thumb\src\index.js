import convert from './convert/index.js'
// import compress from './compress/index.js'
// import uncompress from './compress/uncompress.js'
import reverse from './reverse/index.js'
// import test from './test/index.js'
// import old from './learn/index.js'
import validate from './validate/index.js'
// import classify from './classify/index.js'
// import fingerprint from './fingerprint/index.js'

import learn from './learn/index.js'
import compress from './compress/pack.js'
import uncompress from './compress/unpack.js'
import test from './test/index.js'
// import again from './again/index.js'


export { learn, convert, compress, uncompress, reverse, validate, test }
