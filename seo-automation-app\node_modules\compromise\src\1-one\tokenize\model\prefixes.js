// dashed prefixes that are not independent words
//  'mid-century', 'pre-history'
export default [
  'anti',
  'bi',
  'co',
  'contra',
  'de',
  'extra',
  'infra',
  'inter',
  'intra',
  'macro',
  'micro',
  'mis',
  'mono',
  'multi',
  'peri',
  'pre',
  'pro',
  'proto',
  'pseudo',
  're',
  'sub',
  'supra',
  'trans',
  'tri',
  'un',
  'out', //out-lived
  'ex',//ex-wife

  // 'counter',
  // 'mid',
  // 'out',
  // 'non',
  // 'over',
  // 'post',
  // 'semi',
  // 'super', //'super-cool'
  // 'ultra', //'ulta-cool'
  // 'under',
  // 'whole',
].reduce((h, str) => {
  h[str] = true
  return h
}, {})