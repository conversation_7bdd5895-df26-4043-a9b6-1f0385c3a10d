//nouns with irregular plural/singular forms
//used in nouns.toPlural(), and also in the lexicon.

export default {
  // -a
  addendum: 'addenda',
  corpus: 'corpora',
  criterion: 'criteria',
  curriculum: 'curricula',
  genus: 'genera',
  memorandum: 'memoranda',
  opus: 'opera',
  ovum: 'ova',
  phenomenon: 'phenomena',
  referendum: 'referenda',

  // -ae
  alga: 'algae',
  alumna: 'alumnae',
  antenna: 'antennae',
  formula: 'formulae',
  larva: 'larvae',
  nebula: 'nebulae',
  vertebra: 'vertebrae',

  // -is
  analysis: 'analyses',
  axis: 'axes',
  diagnosis: 'diagnoses',
  parenthesis: 'parentheses',
  prognosis: 'prognoses',
  synopsis: 'synopses',
  thesis: 'theses',
  neurosis: 'neuroses',
  // -x
  appendix: 'appendices',
  index: 'indices',
  matrix: 'matrices',
  ox: 'oxen',
  sex: 'sexes',

  // -i
  alumnus: 'alumni',
  bacillus: 'bacilli',
  cactus: 'cacti',
  fungus: 'fungi',
  hippopotamus: 'hippopotami',
  libretto: 'libretti',
  modulus: 'moduli',
  nucleus: 'nuclei',
  octopus: 'octopi',
  radius: 'radii',
  stimulus: 'stimuli',
  syllabus: 'syllabi',

  // -ie
  cookie: 'cookies',
  calorie: 'calories',
  auntie: 'aunties',
  movie: 'movies',
  pie: 'pies',
  rookie: 'rookies',
  tie: 'ties',
  zombie: 'zombies',

  // -f
  leaf: 'leaves',
  loaf: 'loaves',
  thief: 'thieves',

  // ee-
  foot: 'feet',
  goose: 'geese',
  tooth: 'teeth',

  // -eaux
  beau: 'beaux',
  chateau: 'chateaux',
  tableau: 'tableaux',

  // -ses
  bus: 'buses',
  gas: 'gases',
  circus: 'circuses',
  crisis: 'crises',
  virus: 'viruses',
  database: 'databases',
  excuse: 'excuses',
  abuse: 'abuses',

  avocado: 'avocados',
  barracks: 'barracks',
  child: 'children',
  clothes: 'clothes',
  echo: 'echoes',
  embargo: 'embargoes',
  epoch: 'epochs',
  deer: 'deer',
  halo: 'halos',
  man: 'men',
  woman: 'women',
  mosquito: 'mosquitoes',
  mouse: 'mice',
  person: 'people',
  quiz: 'quizzes',
  rodeo: 'rodeos',
  shoe: 'shoes',
  sombrero: 'sombreros',
  stomach: 'stomachs',
  tornado: 'tornados',
  tuxedo: 'tuxedos',
  volcano: 'volcanoes',

}
