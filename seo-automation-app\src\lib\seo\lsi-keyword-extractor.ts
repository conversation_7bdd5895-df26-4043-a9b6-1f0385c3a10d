import { stopwords, WordTokenizer, PorterStemmer } from 'natural';

export interface LsiKeyword {
  term: string;
  frequency: number;
  relevance: number; // Placeholder for now, can be improved with more context
}

export function extractLsiKeywords(text: string, mainKeyword?: string): LsiKeyword[] {
  if (!text) {
    return [];
  }

  const tokenizer = new WordTokenizer();
  const words = tokenizer.tokenize(text.toLowerCase());

  // Filter out stopwords and short words
  const filteredWords = words.filter(word => 
    word.length > 2 && !stopwords.includes(word)
  );

  // Calculate word frequencies
  const wordFrequencies: { [key: string]: number } = {};
  for (const word of filteredWords) {
    wordFrequencies[word] = (wordFrequencies[word] || 0) + 1;
  }

  // Simple co-occurrence for relevance (can be improved)
  const lsiKeywords: LsiKeyword[] = Object.keys(wordFrequencies).map(term => {
    let relevance = 0.5; // Base relevance
    if (mainKeyword && text.toLowerCase().includes(`${term} ${mainKeyword.toLowerCase()}`)) {
      relevance += 0.2; // Boost if co-occurs with main keyword
    }
    if (mainKeyword && text.toLowerCase().includes(`${mainKeyword.toLowerCase()} ${term}`)) {
      relevance += 0.2; // Boost if co-occurs with main keyword
    }
    return {
      term,
      frequency: wordFrequencies[term],
      relevance: Math.min(1, relevance),
    };
  });

  // Sort by frequency and then relevance, take top N
  lsiKeywords.sort((a, b) => {
    if (b.frequency !== a.frequency) {
      return b.frequency - a.frequency;
    }
    return b.relevance - a.relevance;
  });

  return lsiKeywords.slice(0, 20); // Return top 20 LSI keywords
}