{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-drop-client-page-plugin.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { STRING_LITERAL_DROP_BUNDLE } from '../../../shared/lib/constants'\n\nexport const ampFirstEntryNamesMap: WeakMap<webpack.Compilation, string[]> =\n  new WeakMap()\n\nconst PLUGIN_NAME = 'DropAmpFirstPagesPlugin'\n\n// Prevents outputting client pages when they are not needed\nexport class DropClientPage implements webpack.WebpackPluginInstance {\n  ampPages = new Set()\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(\n      PLUGIN_NAME,\n      (compilation: any, { normalModuleFactory }: any) => {\n        // Recursively look up the issuer till it ends up at the root\n        function findEntryModule(mod: any): webpack.Module | null {\n          const queue = new Set([mod])\n          for (const module of queue) {\n            const incomingConnections =\n              compilation.moduleGraph.getIncomingConnections(module)\n\n            for (const incomingConnection of incomingConnections) {\n              if (!incomingConnection.originModule) return module\n              queue.add(incomingConnection.originModule)\n            }\n          }\n\n          return null\n        }\n\n        function handler(parser: any) {\n          function markAsAmpFirst() {\n            const entryModule = findEntryModule(parser.state.module)\n\n            if (!entryModule) {\n              return\n            }\n\n            // @ts-ignore buildInfo exists on Module\n            entryModule.buildInfo.NEXT_ampFirst = true\n          }\n\n          parser.hooks.preDeclarator.tap(PLUGIN_NAME, (declarator: any) => {\n            if (declarator?.id?.name === STRING_LITERAL_DROP_BUNDLE) {\n              markAsAmpFirst()\n            }\n          })\n        }\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/auto')\n          .tap(PLUGIN_NAME, handler)\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/esm')\n          .tap(PLUGIN_NAME, handler)\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/dynamic')\n          .tap(PLUGIN_NAME, handler)\n\n        if (!ampFirstEntryNamesMap.has(compilation)) {\n          ampFirstEntryNamesMap.set(compilation, [])\n        }\n\n        const ampFirstEntryNamesItem = ampFirstEntryNamesMap.get(\n          compilation\n        ) as string[]\n\n        compilation.hooks.seal.tap(PLUGIN_NAME, () => {\n          for (const [name, entryData] of compilation.entries) {\n            for (const dependency of entryData.dependencies) {\n              const module = compilation.moduleGraph.getModule(dependency)\n              if (module?.buildInfo?.NEXT_ampFirst) {\n                ampFirstEntryNamesItem.push(name)\n                compilation.entries.delete(name)\n              }\n            }\n          }\n        })\n      }\n    )\n  }\n}\n"], "names": ["STRING_LITERAL_DROP_BUNDLE", "ampFirstEntryNamesMap", "WeakMap", "PLUGIN_NAME", "DropClientPage", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "findEntryModule", "mod", "queue", "Set", "module", "incomingConnections", "moduleGraph", "getIncomingConnections", "incomingConnection", "originModule", "add", "handler", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryModule", "state", "buildInfo", "NEXT_ampFirst", "preDeclarator", "declarator", "id", "name", "for", "has", "set", "ampFirstEntryNamesItem", "get", "seal", "entryData", "entries", "dependency", "dependencies", "getModule", "push", "delete", "ampPages"], "mappings": "AACA,SAASA,0BAA0B,QAAQ,gCAA+B;AAE1E,OAAO,MAAMC,wBACX,IAAIC,UAAS;AAEf,MAAMC,cAAc;AAEpB,4DAA4D;AAC5D,OAAO,MAAMC;IAGXC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BN,aACA,CAACK,aAAkB,EAAEE,mBAAmB,EAAO;YAC7C,6DAA6D;YAC7D,SAASC,gBAAgBC,GAAQ;gBAC/B,MAAMC,QAAQ,IAAIC,IAAI;oBAACF;iBAAI;gBAC3B,KAAK,MAAMG,UAAUF,MAAO;oBAC1B,MAAMG,sBACJR,YAAYS,WAAW,CAACC,sBAAsB,CAACH;oBAEjD,KAAK,MAAMI,sBAAsBH,oBAAqB;wBACpD,IAAI,CAACG,mBAAmBC,YAAY,EAAE,OAAOL;wBAC7CF,MAAMQ,GAAG,CAACF,mBAAmBC,YAAY;oBAC3C;gBACF;gBAEA,OAAO;YACT;YAEA,SAASE,QAAQC,MAAW;gBAC1B,SAASC;oBACP,MAAMC,cAAcd,gBAAgBY,OAAOG,KAAK,CAACX,MAAM;oBAEvD,IAAI,CAACU,aAAa;wBAChB;oBACF;oBAEA,wCAAwC;oBACxCA,YAAYE,SAAS,CAACC,aAAa,GAAG;gBACxC;gBAEAL,OAAOhB,KAAK,CAACsB,aAAa,CAACpB,GAAG,CAACN,aAAa,CAAC2B;wBACvCA;oBAAJ,IAAIA,CAAAA,+BAAAA,iBAAAA,WAAYC,EAAE,qBAAdD,eAAgBE,IAAI,MAAKhC,4BAA4B;wBACvDwB;oBACF;gBACF;YACF;YAEAd,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BU,GAAG,CAAC,mBACJxB,GAAG,CAACN,aAAamB;YAEpBZ,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BU,GAAG,CAAC,kBACJxB,GAAG,CAACN,aAAamB;YAEpBZ,oBAAoBH,KAAK,CAACgB,MAAM,CAC7BU,GAAG,CAAC,sBACJxB,GAAG,CAACN,aAAamB;YAEpB,IAAI,CAACrB,sBAAsBiC,GAAG,CAAC1B,cAAc;gBAC3CP,sBAAsBkC,GAAG,CAAC3B,aAAa,EAAE;YAC3C;YAEA,MAAM4B,yBAAyBnC,sBAAsBoC,GAAG,CACtD7B;YAGFA,YAAYD,KAAK,CAAC+B,IAAI,CAAC7B,GAAG,CAACN,aAAa;gBACtC,KAAK,MAAM,CAAC6B,MAAMO,UAAU,IAAI/B,YAAYgC,OAAO,CAAE;oBACnD,KAAK,MAAMC,cAAcF,UAAUG,YAAY,CAAE;4BAE3C3B;wBADJ,MAAMA,SAASP,YAAYS,WAAW,CAAC0B,SAAS,CAACF;wBACjD,IAAI1B,2BAAAA,oBAAAA,OAAQY,SAAS,qBAAjBZ,kBAAmBa,aAAa,EAAE;4BACpCQ,uBAAuBQ,IAAI,CAACZ;4BAC5BxB,YAAYgC,OAAO,CAACK,MAAM,CAACb;wBAC7B;oBACF;gBACF;YACF;QACF;IAEJ;;aA1EAc,WAAW,IAAIhC;;AA2EjB", "ignoreList": [0]}