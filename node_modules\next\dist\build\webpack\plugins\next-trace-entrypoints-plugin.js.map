{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport type { Span } from '../../../trace'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport swcLoader, { type SWCLoaderOptions } from '../loaders/next-swc-loader'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { getCompilationSpan } from '../utils'\nimport { isClientComponentEntryModule } from '../loaders/utils'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n    absolutePathByEntryName: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private compilerType: CompilerNameValues\n  private swcLoaderConfig: {\n    loader: string\n    options: Partial<SWCLoaderOptions>\n  }\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n    swcLoaderConfig,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n    swcLoaderConfig: TraceEntryPointsPlugin['swcLoaderConfig']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.compilerType = compilerType\n    this.swcLoaderConfig = swcLoaderConfig\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(compilation: webpack.Compilation, span: Span) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of entrypoint\n          .getEntrypointChunk()\n          .getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/') && this.appDir) {\n          const appDirRelativeEntryPath =\n            this.buildTraceContext.entriesTrace?.absolutePathByEntryName[\n              entrypoint.name\n            ]?.replace(this.appDir, '')\n\n          const entryIsStaticMetadataRoute =\n            appDirRelativeEntryPath &&\n            isMetadataRouteFile(appDirRelativeEntryPath, [], true)\n\n          // Include the client reference manifest in the trace, but not for\n          // static metadata routes, for which we don't generate those.\n          if (!entryIsStaticMetadataRoute) {\n            entryFiles.add(\n              nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n            )\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        compilation.emitAsset(\n          traceOutputName,\n          new sources.RawSource(\n            JSON.stringify({\n              version: TRACE_OUTPUT_VERSION,\n              files: finalFiles,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n            const absolutePathByEntryName = new Map<string, string>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                            absolutePathByEntryName.set(name, absolutePath)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readOriginalSource = (path: string) => {\n              return new Promise<string | Buffer>((resolve) => {\n                compilation.inputFileSystem.readFile(path, (err, result) => {\n                  if (err) {\n                    // we can't throw here as that crashes build un-necessarily\n                    return resolve('')\n                  }\n                  resolve(result || '')\n                })\n              })\n            }\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n\n              try {\n                // fallback to reading raw source file, this may fail\n                // due to unsupported syntax but best effort attempt\n                let usingOriginalSource = false\n                if (!source || isClientComponentEntryModule(mod)) {\n                  source = await readOriginalSource(path)\n                  usingOriginalSource = true\n                }\n                const sourceString = source.toString()\n\n                // If this is a client component we need to trace the\n                // original transpiled source not the client proxy which is\n                // applied before this plugin is run due to the\n                // client-module-loader\n                if (\n                  usingOriginalSource &&\n                  // don't attempt transpiling CSS or image imports\n                  path.match(/\\.(tsx|ts|js|cjs|mjs|jsx)$/)\n                ) {\n                  let transformResolve: (result: string) => void\n                  let transformReject: (error: unknown) => void\n                  const transformPromise = new Promise<string>(\n                    (resolve, reject) => {\n                      transformResolve = resolve\n                      transformReject = reject\n                    }\n                  )\n\n                  // TODO: should we apply all loaders except the\n                  // client-module-loader?\n                  swcLoader.apply(\n                    {\n                      resourcePath: path,\n                      getOptions: () => {\n                        return this.swcLoaderConfig.options\n                      },\n                      async: () => {\n                        return (err: unknown, result: string) => {\n                          if (err) {\n                            return transformReject(err)\n                          }\n                          return transformResolve(result)\n                        }\n                      },\n                    },\n                    [sourceString, undefined]\n                  )\n                  source = await transformPromise\n                }\n              } catch {\n                /* non-fatal */\n              }\n\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              absolutePathByEntryName: Object.fromEntries(\n                absolutePathByEntryName\n              ),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const compilationSpan =\n        getCompilationSpan(compilation) || getCompilationSpan(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (_, callback: any) => {\n            this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["TRACE_IGNORES", "TraceEntryPointsPlugin", "getFilesMapFromReasons", "PLUGIN_NAME", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "swcLoaderConfig", "buildTraceContext", "entryTraces", "tracingRoot", "createTraceAssets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "nodePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "appDirRelativeEntryPath", "entriesTrace", "absolutePathByEntryName", "replace", "entryIsStaticMetadataRoute", "isMetadataRouteFile", "CLIENT_REFERENCE_MANIFEST", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "emitAsset", "sources", "RawSource", "JSON", "stringify", "version", "TRACE_OUTPUT_VERSION", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "getModuleBuildInfo", "route", "absolutePath", "getPageFilePath", "absolutePagePath", "request", "curMap", "readOriginalSource", "resolve", "inputFileSystem", "readFile", "err", "result", "mod", "source", "originalSource", "buffer", "usingOriginalSource", "isClientComponentEntryModule", "sourceString", "toString", "match", "transformResolve", "transformReject", "transformPromise", "reject", "sw<PERSON><PERSON><PERSON><PERSON>", "apply", "resourcePath", "getOptions", "options", "async", "undefined", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "chunks", "depModArray", "ignores", "isIgnoreMatcher", "picomatch", "contains", "dot", "traceEntryCount", "nodeFileTrace", "base", "id", "job", "isCjs", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "compiler", "tap", "compilationSpan", "getCompilationSpan", "link", "e", "isError", "code", "stats", "traceFn", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "_", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resolveExternal", "resRequest"], "mappings": ";;;;;;;;;;;;;;;;IA0BaA,aAAa;eAAbA;;IAuGAC,sBAAsB;eAAtBA;;IA9EGC,sBAAsB;eAAtBA;;;6DAnDK;gEAED;qBACU;2BAMvB;yBAC0B;+BAI1B;kEAEe;oCACa;yBACH;iCACA;sEACiB;iCACb;uBACD;wBACU;;;;;;AAE7C,MAAMC,cAAc;AACb,MAAMH,gBAAgB;IAC3B;IACA;CACD;AAED,MAAMI,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEO,SAASL,uBACdQ,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA2BO,MAAMZ;IAiBXkC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EACrBC,eAAe,EAWhB,CAAE;aApCIC,oBAAuC,CAAC;QAqC7C,IAAI,CAACT,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACQ,WAAW,GAAG,IAAIhC;QACvB,IAAI,CAAC4B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACM,WAAW,GAAGJ,yBAAyBP;QAC5C,IAAI,CAACG,YAAY,GAAGA;QACpB,IAAI,CAACK,eAAe,GAAGA;IACzB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMI,kBAAkB1C,WAAgC,EAAE2C,IAAU,EAAE;QACpE,MAAMC,aAAa5C,YAAY6C,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAIzC;YAC1B,MAAM0C,gBAAgB,IAAIrC;YAC1B,MAAMsC,oBAAoB,IAAI3C;YAE9B,MAAM4C,cAAc,CAACzC,OACnB,CAACb,cAAcuD,IAAI,CAAC,CAACC;oBACnB,OAAO3C,KAAK4C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAcxD,YAAYyD,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI9C;gBAEvB,KAAK,MAAM+C,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAMnD,QAAQiD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYjC;4BAC3CuC,cAAclC,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;oBACA,KAAK,MAAMrD,QAAQiD,MAAMO,cAAc,CAAE;wBACvC,IAAIf,YAAYzC,OAAO;4BACrB,MAAMqD,WAAWC,aAAQ,CAACC,IAAI,CAACtB,YAAYjC;4BAC3CuC,cAAclC,GAAG,CAACgD;4BAClBL,WAAW3C,GAAG,CAACgD;wBACjB;oBACF;gBACF;gBACAf,cAAc9B,GAAG,CAACqC,YAAYG;gBAC9BR,kBAAkBhC,GAAG,CAACqC,WAAWY,IAAI,IAAI,IAAI;uBAAIT;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACpB,iBAAiB,CAAC8B,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIrB;qBAAc;oBACzBsB,kBAAkB,IAAI,CAAC/B,WAAW;oBAClCgC,YAAY,IAAI,CAAC3C,OAAO;gBAC1B;gBACAc;gBACAO,mBAAmBuB,OAAOC,WAAW,CAACxB;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAMyB,eAAe,IAAI,CAAC3C,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAACuB,YAAYG,WAAW,IAAIV,cAAe;oBA2C1C;gBA1CV,MAAM4B,kBAAkB,GAAGD,eAAepB,WAAWY,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBb,aAAQ,CAACc,OAAO,CACtCd,aAAQ,CAACC,IAAI,CAACtB,YAAYiC;gBAG5B,8CAA8C;gBAC9ClB,WAAWqB,MAAM,CACff,aAAQ,CAACC,IAAI,CAACtB,YAAY,GAAGgC,eAAepB,WAAWY,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIZ,WAAWY,IAAI,CAACa,UAAU,CAAC,WAAW,IAAI,CAAClD,MAAM,EAAE;wBAEnD,8EAAA;oBADF,MAAMmD,2BACJ,uCAAA,IAAI,CAAC3C,iBAAiB,CAAC4C,YAAY,sBAAnC,+EAAA,qCAAqCC,uBAAuB,CAC1D5B,WAAWY,IAAI,CAChB,qBAFD,6EAEGiB,OAAO,CAAC,IAAI,CAACtD,MAAM,EAAE;oBAE1B,MAAMuD,6BACJJ,2BACAK,IAAAA,oCAAmB,EAACL,yBAAyB,EAAE,EAAE;oBAEnD,kEAAkE;oBAClE,6DAA6D;oBAC7D,IAAI,CAACI,4BAA4B;wBAC/B3B,WAAW3C,GAAG,CACZiD,aAAQ,CAACC,IAAI,CACXtB,YACAgC,cACApB,WAAWY,IAAI,CAACiB,OAAO,CAAC,QAAQ,OAC9B,MACAG,oCAAyB,GACzB;oBAGR;gBACF;gBAEA,MAAMC,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAI9E,IAAI;2BACN8C;2BACC,EAAA,wBAAA,IAAI,CAACnB,WAAW,CAACtB,GAAG,CAACsC,WAAWY,IAAI,sBAApC,sBAAuCwB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAOlF;wBACM;oBAAjB,MAAMmF,YAAW,wBAAA,IAAI,CAACtD,WAAW,CAACtB,GAAG,CAACsC,WAAWY,IAAI,sBAApC,sBAAuClD,GAAG,CAACP;oBAE5D,MAAMoF,eAAe9B,aAAQ,CAC1B+B,QAAQ,CAAClB,iBAAiBnE,MAC1B0E,OAAO,CAAC,OAAO;oBAElB,IAAI1E,MAAM;wBACR,IAAI,EAACmF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGF/F,YAAYmG,SAAS,CACnBtB,iBACA,IAAIuB,gBAAO,CAACC,SAAS,CACnBC,KAAKC,SAAS,CAAC;oBACbC,SAASC,+BAAoB;oBAC7B1C,OAAO0B;gBACT;YAGN;QACF;IACF;IAEAiB,iBACE1G,WAAgC,EAChC2G,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACA9G,YAAY+G,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCpH,aACA,OAAOqH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2B5D,UAAU,CAAC;YACxC,MAAMqE,kBACHpE,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMqE,eAAe,IAAI7G;gBACzB,MAAM8G,cAAc,IAAI9G;gBACxB,MAAM+G,oBAAoB,IAAI/G;gBAC9B,MAAM4E,0BAA0B,IAAI5E;gBAEpC,MAAMgH,YAAY,IAAIhH;gBAEtB,MAAM4G,kBACHrE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACoB,MAAMqD,MAAM,IAAIzH,YAAY0H,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBvD,wBAAAA,KAAMiB,OAAO,CAAC,OAAO;wBAE5C,MAAMuC,SAASD,eAAe1C,UAAU,CAAC;wBACzC,MAAM4C,QACJ,IAAI,CAAC3F,aAAa,IAAIyF,eAAe1C,UAAU,CAAC;wBAElD,IAAI4C,SAASD,QAAQ;4BACnB,KAAK,MAAM3H,OAAOwH,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAAC7H,KAAK;gCACV,MAAM8H,WAAWhI,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAI8H,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBC,IAAAA,sCAAkB,EAACH;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBE,KAAK,EAAE;wCACzB,MAAMC,eAAeC,IAAAA,wBAAe,EAAC;4CACnCC,kBACEL,gBAAgBE,KAAK,CAACG,gBAAgB;4CACxCxG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZoG,aAAanD,UAAU,CAAC,IAAI,CAACjD,QAAQ,KACtC,IAAI,CAACD,MAAM,IACVqG,aAAanD,UAAU,CAAC,IAAI,CAAClD,MAAM,GACrC;4CACAuF,YAAYnG,GAAG,CAACiH,cAAcL;4CAC9BV,aAAalG,GAAG,CAACiH,cAAchE;4CAC/BgB,wBAAwBjE,GAAG,CAACiD,MAAMgE;wCACpC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIL,SAASQ,OAAO,EAAE;wCACpB,IAAIC,SAASjB,kBAAkBrG,GAAG,CAACkD;wCAEnC,IAAI,CAACoE,QAAQ;4CACXA,SAAS,IAAIhI;4CACb+G,kBAAkBpG,GAAG,CAACiD,MAAMoE;wCAC9B;wCACAhB,UAAUrG,GAAG,CAAC4G,SAASQ,OAAO,EAAER;wCAChCS,OAAOrH,GAAG,CAAC4G,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAalG,GAAG,CAAC4G,SAASC,QAAQ,EAAE5D;oCACpCkD,YAAYnG,GAAG,CAAC4G,SAASC,QAAQ,EAAED;oCAEnC,IAAIS,SAASjB,kBAAkBrG,GAAG,CAACkD;oCAEnC,IAAI,CAACoE,QAAQ;wCACXA,SAAS,IAAIhI;wCACb+G,kBAAkBpG,GAAG,CAACiD,MAAMoE;oCAC9B;oCACAhB,UAAUrG,GAAG,CAAC4G,SAASC,QAAQ,EAAED;oCACjCS,OAAOrH,GAAG,CAAC4G,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMU,qBAAqB,CAAC3F;oBAC1B,OAAO,IAAI4C,QAAyB,CAACgD;wBACnC1I,YAAY2I,eAAe,CAACC,QAAQ,CAAC9F,MAAM,CAAC+F,KAAKC;4BAC/C,IAAID,KAAK;gCACP,2DAA2D;gCAC3D,OAAOH,QAAQ;4BACjB;4BACAA,QAAQI,UAAU;wBACpB;oBACF;gBACF;gBAEA,MAAMF,WAAW,OACf9F;wBAM8BiG,qBAAAA;oBAJ9B,MAAMA,MAAMvB,UAAUtG,GAAG,CAAC4B,SAASwE,YAAYpG,GAAG,CAAC4B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAIkG,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAE7D,IAAI;wBACF,qDAAqD;wBACrD,oDAAoD;wBACpD,IAAIC,sBAAsB;wBAC1B,IAAI,CAACH,UAAUI,IAAAA,oCAA4B,EAACL,MAAM;4BAChDC,SAAS,MAAMP,mBAAmB3F;4BAClCqG,sBAAsB;wBACxB;wBACA,MAAME,eAAeL,OAAOM,QAAQ;wBAEpC,qDAAqD;wBACrD,2DAA2D;wBAC3D,+CAA+C;wBAC/C,uBAAuB;wBACvB,IACEH,uBACA,iDAAiD;wBACjDrG,KAAKyG,KAAK,CAAC,+BACX;4BACA,IAAIC;4BACJ,IAAIC;4BACJ,MAAMC,mBAAmB,IAAIhE,QAC3B,CAACgD,SAASiB;gCACRH,mBAAmBd;gCACnBe,kBAAkBE;4BACpB;4BAGF,+CAA+C;4BAC/C,wBAAwB;4BACxBC,sBAAS,CAACC,KAAK,CACb;gCACEC,cAAchH;gCACdiH,YAAY;oCACV,OAAO,IAAI,CAACzH,eAAe,CAAC0H,OAAO;gCACrC;gCACAC,OAAO;oCACL,OAAO,CAACpB,KAAcC;wCACpB,IAAID,KAAK;4CACP,OAAOY,gBAAgBZ;wCACzB;wCACA,OAAOW,iBAAiBV;oCAC1B;gCACF;4BACF,GACA;gCAACO;gCAAca;6BAAU;4BAE3BlB,SAAS,MAAMU;wBACjB;oBACF,EAAE,OAAM;oBACN,aAAa,GACf;oBAEA,OAAOV,UAAU;gBACnB;gBAEA,MAAMmB,aAAaC,MAAMC,IAAI,CAAC/C,YAAY1B,IAAI;gBAE9C,MAAM0E,sBAAsB,OAAOvB,KAAUjI;oBAC3C,IAAI,CAACiI,OAAO,CAACA,IAAIjB,YAAY,EAAE;oBAE/B,KAAK,MAAM7H,OAAO8I,IAAIjB,YAAY,CAAE;wBAClC,MAAMyC,SAASxK,wBAAwBC,aAAaC;wBAEpD,IAAIsK,CAAAA,0BAAAA,OAAQvC,QAAQ,KAAI,CAACR,UAAUtG,GAAG,CAACqJ,OAAOvC,QAAQ,GAAG;4BACvDR,UAAUrG,GAAG,CAACoJ,OAAOvC,QAAQ,EAAEuC;4BAC/B,MAAMD,oBAAoBC,QAAQzJ;wBACpC;oBACF;gBACF;gBACA,MAAM0J,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAM1C,SAAS0C,WAAY;oBAC9B,MAAMG,oBAAoBhD,YAAYpG,GAAG,CAACuG,QAAQA;oBAClD,MAAMgD,YAAYpD,aAAanG,GAAG,CAACuG;oBACnC,MAAMiD,kBAAkBnD,kBAAkBrG,GAAG,CAACuJ;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAetE,IAAI,IAAIwE,gBAAgB9E,IAAI;oBAC7C;gBACF;gBAEA,MAAMpB,mBAAmB,IAAI,CAAC/B,WAAW;gBACzC,MAAMkI,SAAS;uBAAIH;iBAAe;gBAElC,IAAI,CAACjI,iBAAiB,CAAC4C,YAAY,GAAG;oBACpCb,QAAQ;wBACNA,QAAQ;wBACRC,OAAOoG;wBACPnG;wBACAC,YAAY,IAAI,CAAC3C,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpB8I,aAAaR,MAAMC,IAAI,CAAC7C,UAAU5B,IAAI;oBACtCyB,cAAc3C,OAAOC,WAAW,CAAC0C;oBACjCjC,yBAAyBV,OAAOC,WAAW,CACzCS;oBAEFxC,YAAY5C,YAAY6C,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAI1C;gBACJ,IAAIC;gBACJ,MAAMwK,UAAU;uBACXnL;uBACA,IAAI,CAACyC,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAM2I,kBAAkBC,IAAAA,kBAAS,EAACF,SAAS;oBACzCG,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAM3K,WAAW,CAACwC;oBAChB,OAAOgI,gBAAgBhI;gBACzB;gBAEA,MAAMsE,kBACHrE,UAAU,CAAC,0BAA0B;oBACpCmI,iBAAiBV,eAAe9I,MAAM,GAAG;gBAC3C,GACCsB,YAAY,CAAC;oBACZ,MAAM8F,SAAS,MAAMqC,IAAAA,kBAAa,EAACX,gBAAgB;wBACjDY,MAAM,IAAI,CAAC3I,WAAW;wBACtBgC,YAAY,IAAI,CAAC3C,OAAO;wBACxB8G;wBACA/B;wBACAC;wBACA4B,SAAS9B,YACL,OAAOyE,IAAIvK,QAAQwK,KAAKC;4BACtB,OAAO3E,UAAUyE,IAAIvK,QAAQwK,KAAK,CAACC;wBACrC,IACArB;wBACJsB,QAAQlL;wBACRmL,cAAc;oBAChB;oBACA,aAAa;oBACbrL,WAAW0I,OAAO1I,QAAQ;oBAC1B0I,OAAO4C,WAAW,CAACC,OAAO,CAAC,CAAChL,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAUyI,OAAOzI,OAAO;gBAC1B;gBAEF,MAAM+G,kBACHrE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAMzC,iBAAiBX,uBACrBQ,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAOsD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAE9B;wBACvC,MAAM4J,SAAS/C,UAAUtG,GAAG,CAACP;wBAC7B,MAAMiL,WAAUvL,eAAAA,QACba,GAAG,CAAC+C,aAAQ,CAAC+B,QAAQ,CAAC,IAAI,CAACvD,WAAW,EAAE9B,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACiK,WACDxB,MAAMyB,OAAO,CAACtB,0BAAAA,OAAQuB,OAAO,KAC7BvB,OAAOuB,OAAO,CAACpK,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAM+F,SAAS0C,WAAY;4BAeJ5J;wBAd1B,MAAMkK,YAAYpD,aAAanG,GAAG,CAACuG;wBACnC,MAAMsE,kBAAkB9H,aAAQ,CAAC+B,QAAQ,CACvC,IAAI,CAACvD,WAAW,EAChBgF;wBAEF,MAAMiD,kBAAkBnD,kBAAkBrG,GAAG,CAACuJ;wBAC9C,MAAMuB,YAAY,IAAIxL;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClBwL,UAAU7K,GAAG,CAACsG,OAAO;4BACnBxB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAAChG,KAAKgM,KAAK,IAAI1L,EAAAA,sBAAAA,eACvBW,GAAG,CAAC6K,qCADmBxL,oBAEtBmH,OAAO,OAAM,EAAE,CAAE;4BACnBsE,UAAU7K,GAAG,CAAC8C,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAExC,MAAM;gCAClDgG,SAASgG,KAAK7K,OAAO;4BACvB;wBACF;wBAEA,IAAIsJ,iBAAiB;4BACnB,KAAK,MAAMwB,cAAcxB,gBAAgB9E,IAAI,GAAI;oCAOrBrF;gCAN1B,MAAM4L,uBAAuBlI,aAAQ,CAAC+B,QAAQ,CAC5C,IAAI,CAACvD,WAAW,EAChByJ;gCAEFF,UAAU7K,GAAG,CAAC+K,YAAY;oCAAEjG,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAAChG,KAAKgM,KAAK,IAAI1L,EAAAA,uBAAAA,eACvBW,GAAG,CAACiL,0CADmB5L,qBAEtBmH,OAAO,OAAM,EAAE,CAAE;oCACnBsE,UAAU7K,GAAG,CAAC8C,aAAQ,CAACC,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAExC,MAAM;wCAClDgG,SAASgG,KAAK7K,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAACoB,WAAW,CAACrB,GAAG,CAACsJ,WAAWuB;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAMjF,YACN,CAAC0B,MAAQ1B,SAAS0B;QAExB;IAEJ;IAEAgB,MAAMwC,QAA0B,EAAE;QAChCA,SAAStF,KAAK,CAAC/G,WAAW,CAACsM,GAAG,CAACzM,aAAa,CAACG;YAC3C,MAAMuM,kBACJC,IAAAA,yBAAkB,EAACxM,gBAAgBwM,IAAAA,yBAAkB,EAACH;YACxD,MAAM1F,6BAA6B4F,gBAAgBxJ,UAAU,CAC3D;YAGF,MAAM8D,WAAW,OAAO/D;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAI4C,QAAQ,CAACgD,SAASiB;;wBAE/B3J,YAAY2I,eAAe,CACxB9B,QAAQ,CACX/D,MAAM,CAAC+F,KAAK4D;4BACZ,IAAI5D,KAAK,OAAOc,OAAOd;4BACvBH,QAAQ+D;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACEC,IAAAA,gBAAO,EAACD,MACPA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YACA,MAAM5F,OAAO,OAAOhE;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAI4C,QAAQ,CAACgD,SAASiB;;wBAC/B3J,YAAY2I,eAAe,CAAC7B,IAAI,CAChChE,MACA,CAAC+F,KAAKgE;4BACJ,IAAIhE,KAAK,OAAOc,OAAOd;4BACvBH,QAAQmE;wBACV;oBAEJ;gBACF,EAAE,OAAOH,GAAG;oBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEA/F,2BAA2BmG,OAAO,CAAC;gBACjC9M,YAAY+G,KAAK,CAACgG,aAAa,CAAC9F,QAAQ,CACtC;oBACE7C,MAAMvE;oBACNmN,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACC,GAAGjG;oBACF,IAAI,CAACzE,iBAAiB,CAAC1C,aAAa2G,4BACjCyF,IAAI,CAAC,IAAMjF,YACXkG,KAAK,CAAC,CAACxE,MAAQ1B,SAAS0B;gBAC7B;gBAGF,IAAIyE,WAAWtN,YAAYuN,eAAe,CAACrM,GAAG,CAAC;gBAE/C,SAASsM,WAAWpJ,IAAY;oBAC9B,MAAMqJ,WAAWrJ,KAAKsJ,KAAK,CAAC;oBAC5B,IAAItJ,IAAI,CAAC,EAAE,KAAK,OAAOqJ,SAAS/L,MAAM,GAAG,GACvC,OAAO+L,SAAS/L,MAAM,GAAG,IAAI+L,SAASE,KAAK,CAAC,GAAG,GAAGzJ,IAAI,CAAC,OAAO;oBAChE,OAAOuJ,SAAS/L,MAAM,GAAG+L,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjB5D;oBAEA,MAAM6D,cAAcP,SAASQ,WAAW,CAAC9D;oBAEzC,OAAO,CACLlJ,QACAyH,SACA+C,MAEA,IAAI5F,QAA2B,CAACgD,SAASiB;4BACvC,MAAMoE,UAAU9J,aAAQ,CAACc,OAAO,CAACjE;4BAEjC+M,YAAYnF,OAAO,CACjB,CAAC,GACDqF,SACAxF,SACA;gCACEyF,kBAAkBhO,YAAYgO,gBAAgB;gCAC9CC,qBAAqBjO,YAAYiO,mBAAmB;gCACpDC,qBAAqBlO,YAAYkO,mBAAmB;4BACtD,GACA,OAAOrF,KAAUC,QAASqF;gCACxB,IAAItF,KAAK,OAAOc,OAAOd;gCAEvB,IAAI,CAACC,QAAQ;oCACX,OAAOa,OAAO,qBAA6B,CAA7B,IAAIyE,MAAM,qBAAV,qBAAA;+CAAA;oDAAA;sDAAA;oCAA4B;gCAC5C;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAItF,OAAOnH,QAAQ,CAAC,QAAQmH,OAAOnH,QAAQ,CAAC,MAAM;oCAChDmH,SAASqF,CAAAA,8BAAAA,WAAYrL,IAAI,KAAIgG;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAOnH,QAAQ,CAAC,iBAAiB;wCACnC,IAAI0M,cAAcvF,OACfzD,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACpB,aAAQ,CAACqK,UAAU,CAAC/F,YACrBA,QAAQ5G,QAAQ,CAAC,SACjBwM,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBf;4CAFhBa,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BhG,QAAQoF,KAAK,CAACH,EAAAA,cAAAA,WAAWjF,6BAAXiF,YAAqB9L,MAAM,KAAI,KAC7CuC,aAAQ,CAACuK,GAAG,GACZ,cAAa,EAEZnJ,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMoJ,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYV,KAAK,CAAC,GAAGgB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAM/C,IAAIwD,MAAM,CAACD,qBAAqB;gDACxC,MAAMvD,IAAIyD,QAAQ,CAChB,MAAMzD,IAAI0D,QAAQ,CAACH,qBACnB,WACA/N;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAOmO,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACAvG,QAAQ;oCAACI;oCAAQkB,QAAQkF,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGC,mCAAoB;oBACvBC,gBAAgBnF;oBAChBoF,SAASpF;oBACTqF,YAAYrF;gBACd;gBACA,MAAMsF,2BAA2B;oBAC/B,GAAGL,mBAAmB;oBACtBM,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAGC,uCAAwB;oBAC3BN,gBAAgBnF;oBAChBoF,SAASpF;oBACTqF,YAAYrF;gBACd;gBACA,MAAM0F,2BAA2B;oBAC/B,GAAGF,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAM7I,YAAY,OAChB2B,SACAzH,QACAwK,KACAuE;oBAEA,MAAM9B,UAAU9J,aAAQ,CAACc,OAAO,CAACjE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEgP,GAAG,EAAE,GAAG,MAAMC,IAAAA,gCAAe,EACnC,IAAI,CAACjO,OAAO,EACZ,IAAI,CAACM,YAAY,EACjB2L,SACAxF,SACAsH,gBACA,CAAC7F,UAAY,CAACoD,GAAW4C;4BACvB,OAAOpC,WAAW5D,SAASlJ,QAAQkP,YAAY1E;wBACjD,GACApB,WACAA,WACAwF,qBACAP,qBACAS,0BACAJ;oBAGF,IAAI,CAACM,KAAK;wBACR,MAAM,qBAAwD,CAAxD,IAAI1B,MAAM,CAAC,kBAAkB,EAAE7F,QAAQ,MAAM,EAAEzH,QAAQ,GAAvD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAuD;oBAC/D;oBACA,OAAOgP,IAAIzK,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACqB,gBAAgB,CACnB1G,aACA2G,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF", "ignoreList": [0]}