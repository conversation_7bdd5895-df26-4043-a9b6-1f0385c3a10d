
import { NextRequest, NextResponse } from 'next/server';
import { analyzeSeo } from '@/lib/seo/seo-analyzer';
import { SeoMetricsRepository } from '@/repositories/seo/seo-metrics.repository';
import { SeoAnalysisResult } from '@/types/seo';

export async function POST(request: NextRequest) {
  try {
    const { text, html, keyword, headings, competitorAnalysisId, competitors } = await request.json();

    if (!text || !html || !keyword || !headings || !competitorAnalysisId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    const analysis: SeoAnalysisResult = await analyzeSeo(text, html, keyword, headings, competitors);

    const seoMetricsRepo = new SeoMetricsRepository();
    await seoMetricsRepo.create({
      competitorAnalysisId,
      keyword,
      location: 'unknown', // Location should ideally come from the request as well
      metrics: analysis,
    });

    return NextResponse.json(analysis);
  } catch (error) {
    console.error('Error in SEO analysis:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
