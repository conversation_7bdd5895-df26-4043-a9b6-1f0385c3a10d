
import { NextRequest, NextResponse } from 'next/server';
import { analyzeSeo } from '@/lib/seo/seo-analyzer';

export async function POST(request: NextRequest) {
  try {
    const { text, html, keyword, headings } = await request.json();

    if (!text || !html || !keyword || !headings) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    const analysis = await analyzeSeo(text, html, keyword, headings);

    return NextResponse.json(analysis);
  } catch (error) {
    console.error('Error in SEO analysis:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
