!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(n="undefined"!=typeof globalThis?globalThis:n||self).efrt=t()}(this,(function(){"use strict";const n=36,t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",e=t.split("").reduce((function(n,t,e){return n[t]=e,n}),{});var o=function(t){if(void 0!==e[t])return e[t];let o=0,s=1,r=n,c=1;for(;s<t.length;o+=r,s++,r*=n);for(let e=t.length-1;e>=0;e--,c*=n){let n=t.charCodeAt(e)-48;n>10&&(n-=7),o+=n*c}return o};const s=function(n,t,e){const s=o(t);return s<n.symCount?n.syms[s]:e+s+1-n.symCount},r=function(n){const t={nodes:n.split(";"),syms:[],symCount:0};return n.match(":")&&function(n){const t=new RegExp("([0-9A-Z]+):([0-9A-Z]+)");for(let e=0;e<n.nodes.length;e++){const s=t.exec(n.nodes[e]);if(!s){n.symCount=e;break}n.syms[o(s[1])]=o(s[2])}n.nodes=n.nodes.slice(n.symCount,n.nodes.length)}(t),function(n){const t=[],e=(o,r)=>{let c=n.nodes[o];"!"===c[0]&&(t.push(r),c=c.slice(1));const u=c.split(/([A-Z0-9,]+)/g);for(let c=0;c<u.length;c+=2){const i=u[c],f=u[c+1];if(!i)continue;const l=r+i;if(","===f||void 0===f){t.push(l);continue}const d=s(n,f,o);e(d,l)}};return e(0,""),t}(t)};return function(n){if(!n)return{};const t=n.split("|").reduce(((n,t)=>{const e=t.split("¦");return n[e[0]]=e[1],n}),{}),e={};return Object.keys(t).forEach((function(n){const o=r(t[n]);"true"===n&&(n=!0);for(let t=0;t<o.length;t++){const s=o[t];!0===e.hasOwnProperty(s)?!1===Array.isArray(e[s])?e[s]=[e[s],n]:e[s].push(n):e[s]=n}})),e}}));
