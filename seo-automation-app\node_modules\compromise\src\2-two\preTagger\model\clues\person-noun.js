import person from './_person.js'
import noun from './_noun.js'

// 'babling brook' vs 'brook sheilds'

const clue = {
  beforeTags: Object.assign({}, noun.beforeTags, person.beforeTags),
  afterTags: Object.assign({}, noun.afterTags, person.afterTags),
  beforeWords: Object.assign({}, noun.beforeWords, person.beforeWords, { i: 'Infinitive', we: 'Infinitive' }),
  afterWords: Object.assign({}, noun.afterWords, person.afterWords),
}
export default clue