{"version": 3, "sources": ["../../../../src/build/webpack/plugins/memory-with-gc-cache-plugin.ts"], "sourcesContent": ["/*\nThis plugin is based on the internal one in webpack but heavily modified to use a different caching heuristic.\nhttps://github.com/webpack/webpack/blob/853bfda35a0080605c09e1bdeb0103bcb9367a10/lib/cache/MemoryWithGcCachePlugin.js#L15\n\nhttps://github.com/webpack/webpack/blob/main/LICENSE\nCopyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n/*\nThe change in this plugin compared to the built-in one in webpack is that this plugin always cleans up after 5 compilations.\nThe built-in plugin only cleans up \"total modules / max generations\".\nThe default for max generations is 5, so 1/5th of the modules would be marked for deletion.\nThis plugin instead always checks the cache and decreases the time to live of all entries. That way memory is cleaned up earlier.\n*/\n\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { Compiler } from 'next/dist/compiled/webpack/webpack'\n\n// Webpack doesn't expose Etag as a type so get it this way instead.\ntype Etag = Parameters<typeof webpack.Cache.prototype.get>[1]\n\n/**\n * Entry in the memory cache\n */\ninterface CacheEntry {\n  /**\n   * Webpack provided etag\n   */\n  etag: Etag\n  /**\n   * Webpack provided data\n   */\n  data: unknown | null\n  /**\n   * Number of compilations left before the cache item is evicted.\n   */\n  ttl: number\n}\n\n// Used to hook into the memory stage of the webpack caching\nconst CACHE_STAGE_MEMORY = -10 // TODO: Somehow webpack.Cache.STAGE_MEMORY doesn't work.\n\nconst PLUGIN_NAME = 'NextJsMemoryWithGcCachePlugin'\n\nexport class MemoryWithGcCachePlugin {\n  /**\n   * Maximum number of compilations to keep the cache entry around for when it's not used.\n   * We keep the modules for a few more compilations so that if you comment out a package and bring it back it doesn't need a full compile again.\n   */\n  private maxGenerations: number\n  constructor({ maxGenerations }: { maxGenerations: number }) {\n    this.maxGenerations = maxGenerations\n  }\n  apply(compiler: Compiler) {\n    const maxGenerations = this.maxGenerations\n\n    /**\n     * The memory cache\n     */\n    const cache = new Map<string, CacheEntry>()\n\n    /**\n     * Cache cleanup implementation\n     */\n    function decreaseTTLAndEvict() {\n      for (const [identifier, entry] of cache) {\n        // Decrease item time to live\n        entry.ttl--\n\n        // if ttl is 0 or below, evict entry from the cache\n        if (entry.ttl <= 0) {\n          cache.delete(identifier)\n        }\n      }\n    }\n    compiler.hooks.afterDone.tap(PLUGIN_NAME, decreaseTTLAndEvict)\n    compiler.cache.hooks.store.tap(\n      { name: PLUGIN_NAME, stage: CACHE_STAGE_MEMORY },\n      (identifier, etag, data) => {\n        cache.set(identifier, { etag, data, ttl: maxGenerations })\n      }\n    )\n    compiler.cache.hooks.get.tap(\n      { name: PLUGIN_NAME, stage: CACHE_STAGE_MEMORY },\n      (identifier, etag, gotHandlers) => {\n        const cacheEntry = cache.get(identifier)\n        // Item found\n        if (cacheEntry !== undefined) {\n          // When cache entry is hit we reset the counter.\n          cacheEntry.ttl = maxGenerations\n          // Handles `null` separately as it doesn't have an etag.\n          if (cacheEntry.data === null) {\n            return null\n          }\n\n          return cacheEntry.etag === etag ? cacheEntry.data : null\n        }\n\n        // Handle case where other cache does have the identifier, puts it into the memory cache\n        gotHandlers.push((result, callback) => {\n          cache.set(identifier, {\n            // Handles `null` separately as it doesn't have an etag.\n            etag: result === null ? null : etag,\n            data: result,\n            ttl: maxGenerations,\n          })\n          return callback()\n        })\n\n        // No item found\n        return undefined\n      }\n    )\n    compiler.cache.hooks.shutdown.tap(\n      { name: PLUGIN_NAME, stage: CACHE_STAGE_MEMORY },\n      () => {\n        cache.clear()\n      }\n    )\n  }\n}\n"], "names": ["MemoryWithGcCachePlugin", "CACHE_STAGE_MEMORY", "PLUGIN_NAME", "constructor", "maxGenerations", "apply", "compiler", "cache", "Map", "decreaseTTLAndEvict", "identifier", "entry", "ttl", "delete", "hooks", "afterDone", "tap", "store", "name", "stage", "etag", "data", "set", "get", "gotHandlers", "cacheEntry", "undefined", "push", "result", "callback", "shutdown", "clear"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,GAEA;;;;;AAKA;;;;+BA+BaA;;;eAAAA;;;AALb,4DAA4D;AAC5D,MAAMC,qBAAqB,CAAC,GAAG,yDAAyD;;AAExF,MAAMC,cAAc;AAEb,MAAMF;IAMXG,YAAY,EAAEC,cAAc,EAA8B,CAAE;QAC1D,IAAI,CAACA,cAAc,GAAGA;IACxB;IACAC,MAAMC,QAAkB,EAAE;QACxB,MAAMF,iBAAiB,IAAI,CAACA,cAAc;QAE1C;;KAEC,GACD,MAAMG,QAAQ,IAAIC;QAElB;;KAEC,GACD,SAASC;YACP,KAAK,MAAM,CAACC,YAAYC,MAAM,IAAIJ,MAAO;gBACvC,6BAA6B;gBAC7BI,MAAMC,GAAG;gBAET,mDAAmD;gBACnD,IAAID,MAAMC,GAAG,IAAI,GAAG;oBAClBL,MAAMM,MAAM,CAACH;gBACf;YACF;QACF;QACAJ,SAASQ,KAAK,CAACC,SAAS,CAACC,GAAG,CAACd,aAAaO;QAC1CH,SAASC,KAAK,CAACO,KAAK,CAACG,KAAK,CAACD,GAAG,CAC5B;YAAEE,MAAMhB;YAAaiB,OAAOlB;QAAmB,GAC/C,CAACS,YAAYU,MAAMC;YACjBd,MAAMe,GAAG,CAACZ,YAAY;gBAAEU;gBAAMC;gBAAMT,KAAKR;YAAe;QAC1D;QAEFE,SAASC,KAAK,CAACO,KAAK,CAACS,GAAG,CAACP,GAAG,CAC1B;YAAEE,MAAMhB;YAAaiB,OAAOlB;QAAmB,GAC/C,CAACS,YAAYU,MAAMI;YACjB,MAAMC,aAAalB,MAAMgB,GAAG,CAACb;YAC7B,aAAa;YACb,IAAIe,eAAeC,WAAW;gBAC5B,gDAAgD;gBAChDD,WAAWb,GAAG,GAAGR;gBACjB,wDAAwD;gBACxD,IAAIqB,WAAWJ,IAAI,KAAK,MAAM;oBAC5B,OAAO;gBACT;gBAEA,OAAOI,WAAWL,IAAI,KAAKA,OAAOK,WAAWJ,IAAI,GAAG;YACtD;YAEA,wFAAwF;YACxFG,YAAYG,IAAI,CAAC,CAACC,QAAQC;gBACxBtB,MAAMe,GAAG,CAACZ,YAAY;oBACpB,wDAAwD;oBACxDU,MAAMQ,WAAW,OAAO,OAAOR;oBAC/BC,MAAMO;oBACNhB,KAAKR;gBACP;gBACA,OAAOyB;YACT;YAEA,gBAAgB;YAChB,OAAOH;QACT;QAEFpB,SAASC,KAAK,CAACO,KAAK,CAACgB,QAAQ,CAACd,GAAG,CAC/B;YAAEE,MAAMhB;YAAaiB,OAAOlB;QAAmB,GAC/C;YACEM,MAAMwB,KAAK;QACb;IAEJ;AACF", "ignoreList": [0]}