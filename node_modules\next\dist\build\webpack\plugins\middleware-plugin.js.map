{"version": 3, "sources": ["../../../../src/build/webpack/plugins/middleware-plugin.ts"], "sourcesContent": ["import type {\n  AssetBinding,\n  EdgeMiddlewareMeta,\n} from '../loaders/get-module-build-info'\nimport type { EdgeSSRMeta } from '../loaders/get-module-build-info'\nimport type { MiddlewareMatcher } from '../../analysis/get-page-static-info'\nimport { getNamedMiddlewareRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport path from 'path'\nimport {\n  EDGE_RUNTIME_WEBPACK,\n  EDGE_UNSUPPORTED_NODE_APIS,\n  MIDDLEWARE_BUILD_MANIFEST,\n  CLIENT_REFERENCE_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  DYNAMIC_CSS_MANIFEST,\n} from '../../../shared/lib/constants'\nimport type { MiddlewareConfig } from '../../analysis/get-page-static-info'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport { traceGlobals } from '../../../trace/shared'\nimport { EVENT_BUILD_FEATURE_USAGE } from '../../../telemetry/events'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport {\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../../../lib/constants'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport { getDynamicCodeEvaluationError } from './wellknown-errors-plugin/parse-dynamic-code-evaluation-error'\nimport { getModuleReferencesInOrder } from '../utils'\n\nconst KNOWN_SAFE_DYNAMIC_PACKAGES =\n  require('../../../lib/known-edge-safe-packages.json') as string[]\n\nexport interface EdgeFunctionDefinition {\n  files: string[]\n  name: string\n  page: string\n  matchers: MiddlewareMatcher[]\n  env: Record<string, string>\n  wasm?: AssetBinding[]\n  assets?: AssetBinding[]\n  regions?: string[] | string\n}\n\nexport interface MiddlewareManifest {\n  version: 3\n  sortedMiddleware: string[]\n  middleware: { [page: string]: EdgeFunctionDefinition }\n  functions: { [page: string]: EdgeFunctionDefinition }\n}\n\ninterface EntryMetadata {\n  edgeMiddleware?: EdgeMiddlewareMeta\n  edgeApiFunction?: EdgeMiddlewareMeta\n  edgeSSR?: EdgeSSRMeta\n  wasmBindings: Map<string, string>\n  assetBindings: Map<string, string>\n  regions?: string[] | string\n}\n\nconst NAME = 'MiddlewarePlugin'\nconst MANIFEST_VERSION = 3\n\n/**\n * Checks the value of usingIndirectEval and when it is a set of modules it\n * check if any of the modules is actually being used. If the value is\n * simply truthy it will return true.\n */\nfunction isUsingIndirectEvalAndUsedByExports(args: {\n  module: webpack.Module\n  moduleGraph: webpack.ModuleGraph\n  runtime: any\n  usingIndirectEval: true | Set<string>\n  wp: typeof webpack\n}): boolean {\n  const { moduleGraph, runtime, module, usingIndirectEval, wp } = args\n  if (typeof usingIndirectEval === 'boolean') {\n    return usingIndirectEval\n  }\n\n  const exportsInfo = moduleGraph.getExportsInfo(module)\n  for (const exportName of usingIndirectEval) {\n    if (exportsInfo.getUsed(exportName, runtime) !== wp.UsageState.Unused) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction getEntryFiles(\n  entryFiles: string[],\n  meta: EntryMetadata,\n  hasInstrumentationHook: boolean,\n  opts: Options\n) {\n  const files: string[] = []\n  if (meta.edgeSSR) {\n    if (meta.edgeSSR.isServerComponent) {\n      files.push(`server/${SERVER_REFERENCE_MANIFEST}.js`)\n      if (opts.sriEnabled) {\n        files.push(`server/${SUBRESOURCE_INTEGRITY_MANIFEST}.js`)\n      }\n      files.push(\n        ...entryFiles\n          .filter(\n            (file) =>\n              file.startsWith('app/') && !file.endsWith('.hot-update.js')\n          )\n          .map(\n            (file) =>\n              'server/' +\n              file.replace(/\\.js$/, '_' + CLIENT_REFERENCE_MANIFEST + '.js')\n          )\n      )\n    }\n    if (!opts.dev && !meta.edgeSSR.isAppDir) {\n      files.push(`server/${DYNAMIC_CSS_MANIFEST}.js`)\n    }\n\n    files.push(\n      `server/${MIDDLEWARE_BUILD_MANIFEST}.js`,\n      `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n      `server/${NEXT_FONT_MANIFEST}.js`,\n      `server/${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n  }\n\n  if (hasInstrumentationHook) {\n    files.push(`server/edge-${INSTRUMENTATION_HOOK_FILENAME}.js`)\n  }\n\n  files.push(\n    ...entryFiles\n      .filter((file) => !file.endsWith('.hot-update.js'))\n      .map((file) => 'server/' + file)\n  )\n\n  return files\n}\n\nfunction getCreateAssets(params: {\n  compilation: webpack.Compilation\n  metadataByEntry: Map<string, EntryMetadata>\n  opts: Options\n}) {\n  const { compilation, metadataByEntry, opts } = params\n  return () => {\n    const middlewareManifest: MiddlewareManifest = {\n      version: MANIFEST_VERSION,\n      middleware: {},\n      functions: {},\n      sortedMiddleware: [],\n    }\n\n    const hasInstrumentationHook = compilation.entrypoints.has(\n      INSTRUMENTATION_HOOK_FILENAME\n    )\n\n    // we only emit this entry for the edge runtime since it doesn't have access to a routes manifest\n    // and we don't need to provide the entire route manifest, just the interception routes.\n    const interceptionRewrites = JSON.stringify(\n      opts.rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n    compilation.emitAsset(\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`,\n      new sources.RawSource(\n        `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n          interceptionRewrites\n        )}`\n      ) as unknown as webpack.sources.RawSource\n    )\n\n    for (const entrypoint of compilation.entrypoints.values()) {\n      if (!entrypoint.name) {\n        continue\n      }\n\n      // There should always be metadata for the entrypoint.\n      const metadata = metadataByEntry.get(entrypoint.name)\n      const page =\n        metadata?.edgeMiddleware?.page ||\n        metadata?.edgeSSR?.page ||\n        metadata?.edgeApiFunction?.page\n      if (!page) {\n        continue\n      }\n\n      const matcherSource = metadata.edgeSSR?.isAppDir\n        ? normalizeAppPath(page)\n        : page\n\n      const catchAll = !metadata.edgeSSR && !metadata.edgeApiFunction\n\n      const { namedRegex } = getNamedMiddlewareRegex(matcherSource, {\n        catchAll,\n      })\n      const matchers = metadata?.edgeMiddleware?.matchers ?? [\n        {\n          regexp: namedRegex,\n          originalSource: page === '/' && catchAll ? '/:path*' : matcherSource,\n        },\n      ]\n\n      const isEdgeFunction = !!(metadata.edgeApiFunction || metadata.edgeSSR)\n      const edgeFunctionDefinition: EdgeFunctionDefinition = {\n        files: getEntryFiles(\n          entrypoint.getFiles(),\n          metadata,\n          hasInstrumentationHook,\n          opts\n        ),\n        name: entrypoint.name,\n        page: page,\n        matchers,\n        wasm: Array.from(metadata.wasmBindings, ([name, filePath]) => ({\n          name,\n          filePath,\n        })),\n        assets: Array.from(metadata.assetBindings, ([name, filePath]) => ({\n          name,\n          filePath,\n        })),\n        env: opts.edgeEnvironments,\n        ...(metadata.regions && { regions: metadata.regions }),\n      }\n\n      if (isEdgeFunction) {\n        middlewareManifest.functions[page] = edgeFunctionDefinition\n      } else {\n        middlewareManifest.middleware[page] = edgeFunctionDefinition\n      }\n    }\n\n    middlewareManifest.sortedMiddleware = getSortedRoutes(\n      Object.keys(middlewareManifest.middleware)\n    )\n\n    compilation.emitAsset(\n      MIDDLEWARE_MANIFEST,\n      new sources.RawSource(\n        JSON.stringify(middlewareManifest, null, 2)\n      ) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n\nfunction buildWebpackError({\n  message,\n  loc,\n  compilation,\n  entryModule,\n  parser,\n}: {\n  message: string\n  loc?: any\n  compilation: webpack.Compilation\n  entryModule?: webpack.Module\n  parser?: webpack.javascript.JavascriptParser\n}) {\n  const error = new compilation.compiler.webpack.WebpackError(message)\n  error.name = NAME\n  const module = entryModule ?? parser?.state.current\n  if (module) {\n    error.module = module\n  }\n  error.loc = loc\n  return error\n}\n\nfunction isInMiddlewareLayer(parser: webpack.javascript.JavascriptParser) {\n  const layer = parser.state.module?.layer\n  return layer === WEBPACK_LAYERS.middleware || layer === WEBPACK_LAYERS.apiEdge\n}\n\nfunction isNodeJsModule(moduleName: string) {\n  return (require('module') as typeof import('module')).builtinModules.includes(\n    moduleName\n  )\n}\n\nfunction isDynamicCodeEvaluationAllowed(\n  fileName: string,\n  middlewareConfig?: MiddlewareConfig,\n  rootDir?: string\n) {\n  // Some packages are known to use `eval` but are safe to use in the Edge\n  // Runtime because the dynamic code will never be executed.\n  if (\n    KNOWN_SAFE_DYNAMIC_PACKAGES.some((pkg) =>\n      fileName.includes(`/node_modules/${pkg}/`.replace(/\\//g, path.sep))\n    )\n  ) {\n    return true\n  }\n\n  const name = fileName.replace(rootDir ?? '', '')\n\n  return picomatch(middlewareConfig?.unstable_allowDynamic ?? [], {\n    dot: true,\n  })(name)\n}\n\nfunction buildUnsupportedApiError({\n  apiName,\n  loc,\n  ...rest\n}: {\n  apiName: string\n  loc: any\n  compilation: webpack.Compilation\n  parser: webpack.javascript.JavascriptParser\n}) {\n  return buildWebpackError({\n    message: `A Node.js API is used (${apiName} at line: ${loc.start.line}) which is not supported in the Edge Runtime.\nLearn more: https://nextjs.org/docs/api-reference/edge-runtime`,\n    loc,\n    ...rest,\n  })\n}\n\nfunction registerUnsupportedApiHooks(\n  parser: webpack.javascript.JavascriptParser,\n  compilation: webpack.Compilation\n) {\n  for (const expression of EDGE_UNSUPPORTED_NODE_APIS) {\n    const warnForUnsupportedApi = (node: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n      compilation.warnings.push(\n        buildUnsupportedApiError({\n          compilation,\n          parser,\n          apiName: expression,\n          ...node,\n        })\n      )\n      return true\n    }\n    parser.hooks.call.for(expression).tap(NAME, warnForUnsupportedApi)\n    parser.hooks.expression.for(expression).tap(NAME, warnForUnsupportedApi)\n    parser.hooks.callMemberChain\n      .for(expression)\n      .tap(NAME, warnForUnsupportedApi)\n    parser.hooks.expressionMemberChain\n      .for(expression)\n      .tap(NAME, warnForUnsupportedApi)\n  }\n\n  const warnForUnsupportedProcessApi = (node: any, [callee]: string[]) => {\n    if (!isInMiddlewareLayer(parser) || callee === 'env') {\n      return\n    }\n    compilation.warnings.push(\n      buildUnsupportedApiError({\n        compilation,\n        parser,\n        apiName: `process.${callee}`,\n        ...node,\n      })\n    )\n    return true\n  }\n\n  parser.hooks.callMemberChain\n    .for('process')\n    .tap(NAME, warnForUnsupportedProcessApi)\n  parser.hooks.expressionMemberChain\n    .for('process')\n    .tap(NAME, warnForUnsupportedProcessApi)\n}\n\nfunction getCodeAnalyzer(params: {\n  dev: boolean\n  compiler: webpack.Compiler\n  compilation: webpack.Compilation\n}) {\n  return (parser: webpack.javascript.JavascriptParser) => {\n    const {\n      dev,\n      compiler: { webpack: wp },\n      compilation,\n    } = params\n    const { hooks } = parser\n\n    /**\n     * For an expression this will check the graph to ensure it is being used\n     * by exports. Then it will store in the module buildInfo a boolean to\n     * express that it contains dynamic code and, if it is available, the\n     * module path that is using it.\n     */\n    const handleExpression = () => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      wp.optimize.InnerGraph.onUsage(parser.state, (used = true) => {\n        const buildInfo = getModuleBuildInfo(parser.state.module)\n        if (buildInfo.usingIndirectEval === true || used === false) {\n          return\n        }\n\n        if (!buildInfo.usingIndirectEval || used === true) {\n          buildInfo.usingIndirectEval = used\n          return\n        }\n\n        buildInfo.usingIndirectEval = new Set([\n          ...Array.from(buildInfo.usingIndirectEval),\n          ...Array.from(used),\n        ])\n      })\n    }\n\n    /**\n     * This expression handler allows to wrap a dynamic code expression with a\n     * function call where we can warn about dynamic code not being allowed\n     * but actually execute the expression.\n     */\n    const handleWrapExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      const { ConstDependency } = wp.dependencies\n      const dep1 = new ConstDependency(\n        '__next_eval__(function() { return ',\n        expr.range[0]\n      )\n      dep1.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep1)\n      const dep2 = new ConstDependency('})', expr.range[1])\n      dep2.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep2)\n\n      handleExpression()\n      return true\n    }\n\n    /**\n     * This expression handler allows to wrap a WebAssembly.compile invocation with a\n     * function call where we can warn about WASM code generation not being allowed\n     * but actually execute the expression.\n     */\n    const handleWrapWasmCompileExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      const { ConstDependency } = wp.dependencies\n      const dep1 = new ConstDependency(\n        '__next_webassembly_compile__(function() { return ',\n        expr.range[0]\n      )\n      dep1.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep1)\n      const dep2 = new ConstDependency('})', expr.range[1])\n      dep2.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep2)\n\n      handleExpression()\n    }\n\n    /**\n     * This expression handler allows to wrap a WebAssembly.instatiate invocation with a\n     * function call where we can warn about WASM code generation not being allowed\n     * but actually execute the expression.\n     *\n     * Note that we don't update `usingIndirectEval`, i.e. we don't abort a production build\n     * since we can't determine statically if the first parameter is a module (legit use) or\n     * a buffer (dynamic code generation).\n     */\n    const handleWrapWasmInstantiateExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      if (dev) {\n        const { ConstDependency } = wp.dependencies\n        const dep1 = new ConstDependency(\n          '__next_webassembly_instantiate__(function() { return ',\n          expr.range[0]\n        )\n        dep1.loc = expr.loc\n        parser.state.module.addPresentationalDependency(dep1)\n        const dep2 = new ConstDependency('})', expr.range[1])\n        dep2.loc = expr.loc\n        parser.state.module.addPresentationalDependency(dep2)\n      }\n    }\n\n    /**\n     * Handler to store original source location of static and dynamic imports into module's buildInfo.\n     */\n    const handleImport = (node: any) => {\n      if (isInMiddlewareLayer(parser) && node.source?.value && node?.loc) {\n        const { module, source } = parser.state\n        const buildInfo = getModuleBuildInfo(module)\n        if (!buildInfo.importLocByPath) {\n          buildInfo.importLocByPath = new Map()\n        }\n\n        const importedModule = node.source.value?.toString()\n        buildInfo.importLocByPath.set(importedModule, {\n          sourcePosition: {\n            ...node.loc.start,\n            source: module.identifier(),\n          },\n          sourceContent: source.toString(),\n        })\n\n        if (\n          !dev &&\n          isNodeJsModule(importedModule) &&\n          !SUPPORTED_NATIVE_MODULES.includes(importedModule)\n        ) {\n          compilation.warnings.push(\n            buildWebpackError({\n              message: `A Node.js module is loaded ('${importedModule}' at line ${node.loc.start.line}) which is not supported in the Edge Runtime.\nLearn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`,\n              compilation,\n              parser,\n              ...node,\n            })\n          )\n        }\n      }\n    }\n\n    /**\n     * A noop handler to skip analyzing some cases.\n     * Order matters: for it to work, it must be registered first\n     */\n    const skip = () => (isInMiddlewareLayer(parser) ? true : undefined)\n\n    for (const prefix of ['', 'global.']) {\n      hooks.expression.for(`${prefix}Function.prototype`).tap(NAME, skip)\n      hooks.expression.for(`${prefix}Function.bind`).tap(NAME, skip)\n      hooks.call.for(`${prefix}eval`).tap(NAME, handleWrapExpression)\n      hooks.call.for(`${prefix}Function`).tap(NAME, handleWrapExpression)\n      hooks.new.for(`${prefix}Function`).tap(NAME, handleWrapExpression)\n      hooks.call\n        .for(`${prefix}WebAssembly.compile`)\n        .tap(NAME, handleWrapWasmCompileExpression)\n      hooks.call\n        .for(`${prefix}WebAssembly.instantiate`)\n        .tap(NAME, handleWrapWasmInstantiateExpression)\n    }\n\n    hooks.importCall.tap(NAME, handleImport)\n    hooks.import.tap(NAME, handleImport)\n\n    if (!dev) {\n      // do not issue compilation warning on dev: invoking code will provide details\n      registerUnsupportedApiHooks(parser, compilation)\n    }\n  }\n}\n\nasync function codeAnalyzerBySwc(\n  compilation: webpack.Compilation,\n  modules: Iterable<webpack.Module>,\n  dev: boolean\n) {\n  const binding = require('../../swc') as typeof import('../../swc')\n  for (const module of modules) {\n    if (\n      module.layer !== WEBPACK_LAYERS.middleware &&\n      module.layer !== WEBPACK_LAYERS.apiEdge\n    ) {\n      continue\n    }\n    if (module.constructor.name !== 'NormalModule') {\n      continue\n    }\n    const normalModule = module as webpack.NormalModule\n    if (!normalModule.type.startsWith('javascript')) {\n      // Only analyze JavaScript modules\n      continue\n    }\n    const originalSource = normalModule.originalSource()\n    if (!originalSource) {\n      continue\n    }\n    const source = originalSource.source()\n    if (typeof source !== 'string') {\n      continue\n    }\n    const diagnostics = await binding.warnForEdgeRuntime(source, !dev)\n    for (const diagnostic of diagnostics) {\n      const webpackError = buildWebpackError({\n        message: diagnostic.message,\n        loc: diagnostic.loc,\n        compilation,\n        entryModule: module,\n      })\n      if (diagnostic.severity === 'Warning') {\n        compilation.warnings.push(webpackError)\n      } else {\n        compilation.errors.push(webpackError)\n      }\n    }\n  }\n}\n\nfunction getExtractMetadata(params: {\n  compilation: webpack.Compilation\n  compiler: webpack.Compiler\n  dev: boolean\n  metadataByEntry: Map<string, EntryMetadata>\n}): () => Promise<void> {\n  const { dev, compilation, metadataByEntry, compiler } = params\n  const { webpack: wp } = compiler\n  return async () => {\n    metadataByEntry.clear()\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n\n    for (const [entryName, entry] of compilation.entries) {\n      if (entry.options.runtime !== EDGE_RUNTIME_WEBPACK) {\n        // Only process edge runtime entries\n        continue\n      }\n      const entryDependency = entry.dependencies?.[0]\n      const resolvedModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)\n      if (!resolvedModule) {\n        continue\n      }\n      const { rootDir, route } = getModuleBuildInfo(resolvedModule)\n\n      const { moduleGraph } = compilation\n      const modules = new Set<webpack.NormalModule>()\n      const addEntriesFromDependency = (dependency: any) => {\n        const module = moduleGraph.getModule(dependency)\n        if (module) {\n          modules.add(module as webpack.NormalModule)\n        }\n      }\n\n      entry.dependencies.forEach(addEntriesFromDependency)\n      entry.includeDependencies.forEach(addEntriesFromDependency)\n\n      const entryMetadata: EntryMetadata = {\n        wasmBindings: new Map(),\n        assetBindings: new Map(),\n      }\n\n      if (route?.middlewareConfig?.regions) {\n        entryMetadata.regions = route.middlewareConfig.regions\n      }\n\n      if (route?.preferredRegion) {\n        const preferredRegion = route.preferredRegion\n        entryMetadata.regions =\n          // Ensures preferredRegion is always an array in the manifest.\n          typeof preferredRegion === 'string'\n            ? [preferredRegion]\n            : preferredRegion\n      }\n\n      let ogImageGenerationCount = 0\n\n      for (const module of modules) {\n        const buildInfo = getModuleBuildInfo(module)\n\n        /**\n         * Check if it uses the image generation feature.\n         */\n        if (!dev) {\n          const resource = module.resource\n          const hasOGImageGeneration =\n            resource &&\n            /[\\\\/]node_modules[\\\\/]@vercel[\\\\/]og[\\\\/]dist[\\\\/]index\\.(edge|node)\\.js$|[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js$/.test(\n              resource\n            )\n\n          if (hasOGImageGeneration) {\n            ogImageGenerationCount++\n          }\n        }\n\n        /**\n         * When building for production checks if the module is using `eval`\n         * and in such case produces a compilation error. The module has to\n         * be in use.\n         */\n        if (\n          !dev &&\n          buildInfo.usingIndirectEval &&\n          isUsingIndirectEvalAndUsedByExports({\n            module,\n            moduleGraph,\n            runtime: wp.util.runtime.getEntryRuntime(compilation, entryName),\n            usingIndirectEval: buildInfo.usingIndirectEval,\n            wp,\n          })\n        ) {\n          const id = module.identifier()\n          if (/node_modules[\\\\/]regenerator-runtime[\\\\/]runtime\\.js/.test(id)) {\n            continue\n          }\n          if (route?.middlewareConfig?.unstable_allowDynamic) {\n            telemetry?.record({\n              eventName: 'NEXT_EDGE_ALLOW_DYNAMIC_USED',\n              payload: {\n                file: route?.absolutePagePath.replace(rootDir ?? '', ''),\n                config: route?.middlewareConfig,\n                fileWithDynamicCode: module.userRequest.replace(\n                  rootDir ?? '',\n                  ''\n                ),\n              },\n            })\n          }\n          if (\n            !isDynamicCodeEvaluationAllowed(\n              module.userRequest,\n              route?.middlewareConfig,\n              rootDir\n            )\n          ) {\n            const message = `Dynamic Code Evaluation (e. g. 'eval', 'new Function', 'WebAssembly.compile') not allowed in Edge Runtime ${\n              typeof buildInfo.usingIndirectEval !== 'boolean'\n                ? `\\nUsed by ${Array.from(buildInfo.usingIndirectEval).join(\n                    ', '\n                  )}`\n                : ''\n            }\\nLearn More: https://nextjs.org/docs/messages/edge-dynamic-code-evaluation`\n            compilation.errors.push(\n              getDynamicCodeEvaluationError(\n                message,\n                module,\n                compilation,\n                compiler\n              )\n            )\n          }\n        }\n\n        /**\n         * The entry module has to be either a page or a middleware and hold\n         * the corresponding metadata.\n         */\n        if (buildInfo?.nextEdgeSSR) {\n          entryMetadata.edgeSSR = buildInfo.nextEdgeSSR\n        } else if (buildInfo?.nextEdgeMiddleware) {\n          entryMetadata.edgeMiddleware = buildInfo.nextEdgeMiddleware\n        } else if (buildInfo?.nextEdgeApiFunction) {\n          entryMetadata.edgeApiFunction = buildInfo.nextEdgeApiFunction\n        }\n\n        /**\n         * If the module is a WASM module we read the binding information and\n         * append it to the entry wasm bindings.\n         */\n        if (buildInfo?.nextWasmMiddlewareBinding) {\n          entryMetadata.wasmBindings.set(\n            buildInfo.nextWasmMiddlewareBinding.name,\n            buildInfo.nextWasmMiddlewareBinding.filePath\n          )\n        }\n\n        if (buildInfo?.nextAssetMiddlewareBinding) {\n          entryMetadata.assetBindings.set(\n            buildInfo.nextAssetMiddlewareBinding.name,\n            buildInfo.nextAssetMiddlewareBinding.filePath\n          )\n        }\n\n        /**\n         * Append to the list of modules to process outgoingConnections from\n         * the module that is being processed.\n         */\n        for (const conn of getModuleReferencesInOrder(module, moduleGraph)) {\n          if (conn.module) {\n            modules.add(conn.module as webpack.NormalModule)\n          }\n        }\n      }\n\n      telemetry?.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: {\n          featureName: 'vercelImageGeneration',\n          invocationCount: ogImageGenerationCount,\n        },\n      })\n      metadataByEntry.set(entryName, entryMetadata)\n    }\n  }\n}\n\n// These values will be replaced again in edge runtime deployment build.\n// `buildId` represents BUILD_ID to be externalized in env vars.\n// `encryptionKey` represents server action encryption key to be externalized in env vars.\ntype EdgeRuntimeEnvironments = Record<string, string> & {\n  __NEXT_BUILD_ID: string\n  NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: string\n}\n\ninterface Options {\n  dev: boolean\n  sriEnabled: boolean\n  rewrites: CustomRoutes['rewrites']\n  edgeEnvironments: EdgeRuntimeEnvironments\n}\n\nexport default class MiddlewarePlugin {\n  private readonly dev: Options['dev']\n  private readonly sriEnabled: Options['sriEnabled']\n  private readonly rewrites: Options['rewrites']\n  private readonly edgeEnvironments: EdgeRuntimeEnvironments\n\n  constructor({ dev, sriEnabled, rewrites, edgeEnvironments }: Options) {\n    this.dev = dev\n    this.sriEnabled = sriEnabled\n    this.rewrites = rewrites\n    this.edgeEnvironments = edgeEnvironments\n  }\n\n  public apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(NAME, (compilation, params) => {\n      // parser hooks aren't available in rspack\n      if (process.env.NEXT_RSPACK) {\n        compilation.hooks.finishModules.tapPromise(NAME, async (modules) => {\n          await codeAnalyzerBySwc(compilation, modules, this.dev)\n        })\n      } else {\n        const { hooks } = params.normalModuleFactory\n        /**\n         * This is the static code analysis phase.\n         */\n        const codeAnalyzer = getCodeAnalyzer({\n          dev: this.dev,\n          compiler,\n          compilation,\n        })\n\n        hooks.parser.for('javascript/auto').tap(NAME, codeAnalyzer)\n        hooks.parser.for('javascript/dynamic').tap(NAME, codeAnalyzer)\n        hooks.parser.for('javascript/esm').tap(NAME, codeAnalyzer)\n      }\n\n      /**\n       * Extract all metadata for the entry points in a Map object.\n       */\n      const metadataByEntry = new Map<string, EntryMetadata>()\n      compilation.hooks.finishModules.tapPromise(\n        NAME,\n        getExtractMetadata({\n          compilation,\n          compiler,\n          dev: this.dev,\n          metadataByEntry,\n        })\n      )\n\n      /**\n       * Emit the middleware manifest.\n       */\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'NextJsMiddlewareManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        getCreateAssets({\n          compilation,\n          metadataByEntry,\n          opts: {\n            sriEnabled: this.sriEnabled,\n            rewrites: this.rewrites,\n            edgeEnvironments: this.edgeEnvironments,\n            dev: this.dev,\n          },\n        })\n      )\n    })\n  }\n}\n\nexport const SUPPORTED_NATIVE_MODULES = [\n  'buffer',\n  'events',\n  'assert',\n  'util',\n  'async_hooks',\n] as const\n\nconst supportedEdgePolyfills = new Set<string>(SUPPORTED_NATIVE_MODULES)\n\nexport function getEdgePolyfilledModules() {\n  const records: Record<string, string> = {}\n  for (const mod of SUPPORTED_NATIVE_MODULES) {\n    records[mod] = `commonjs node:${mod}`\n    records[`node:${mod}`] = `commonjs node:${mod}`\n  }\n  return records\n}\n\nexport async function handleWebpackExternalForEdgeRuntime({\n  request,\n  context,\n  contextInfo,\n  getResolve,\n}: {\n  request: string\n  context: string\n  contextInfo: any\n  getResolve: () => any\n}) {\n  if (\n    (contextInfo.issuerLayer === WEBPACK_LAYERS.middleware ||\n      contextInfo.issuerLayer === WEBPACK_LAYERS.apiEdge) &&\n    isNodeJsModule(request) &&\n    !supportedEdgePolyfills.has(request)\n  ) {\n    // allows user to provide and use their polyfills, as we do with buffer.\n    try {\n      await getResolve()(context, request)\n    } catch {\n      return `root globalThis.__import_unsupported('${request}')`\n    }\n  }\n}\n"], "names": ["SUPPORTED_NATIVE_MODULES", "MiddlewarePlugin", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "KNOWN_SAFE_DYNAMIC_PACKAGES", "require", "NAME", "MANIFEST_VERSION", "isUsingIndirectEvalAndUsedByExports", "args", "moduleGraph", "runtime", "module", "usingIndirectEval", "wp", "exportsInfo", "getExportsInfo", "exportName", "getUsed", "UsageState", "Unused", "getEntryFiles", "entryFiles", "meta", "hasInstrumentationHook", "opts", "files", "edgeSSR", "isServerComponent", "push", "SERVER_REFERENCE_MANIFEST", "sriEnabled", "SUBRESOURCE_INTEGRITY_MANIFEST", "filter", "file", "startsWith", "endsWith", "map", "replace", "CLIENT_REFERENCE_MANIFEST", "dev", "isAppDir", "DYNAMIC_CSS_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "NEXT_FONT_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "INSTRUMENTATION_HOOK_FILENAME", "getCreateAssets", "params", "compilation", "metadataByEntry", "middlewareManifest", "version", "middleware", "functions", "sortedMiddleware", "entrypoints", "has", "interceptionRewrites", "JSON", "stringify", "rewrites", "beforeFiles", "isInterceptionRouteRewrite", "emitAsset", "sources", "RawSource", "entrypoint", "values", "metadata", "name", "get", "page", "edgeMiddleware", "edgeApiFunction", "matcherSource", "normalizeAppPath", "catchAll", "namedRegex", "getNamedMiddlewareRegex", "matchers", "regexp", "originalSource", "isEdgeFunction", "edgeFunctionDefinition", "getFiles", "wasm", "Array", "from", "wasmBindings", "filePath", "assets", "assetBindings", "env", "edgeEnvironments", "regions", "getSortedRoutes", "Object", "keys", "MIDDLEWARE_MANIFEST", "buildWebpackError", "message", "loc", "entryModule", "parser", "error", "compiler", "webpack", "WebpackError", "state", "current", "isInMiddlewareLayer", "layer", "WEBPACK_LAYERS", "apiEdge", "isNodeJsModule", "moduleName", "builtinModules", "includes", "isDynamicCodeEvaluationAllowed", "fileName", "middlewareConfig", "rootDir", "some", "pkg", "path", "sep", "picomatch", "unstable_allowDynamic", "dot", "buildUnsupportedApiError", "apiName", "rest", "start", "line", "registerUnsupportedApiHooks", "expression", "EDGE_UNSUPPORTED_NODE_APIS", "warnForUnsupportedApi", "node", "warnings", "hooks", "call", "for", "tap", "callMemberChain", "expression<PERSON>ember<PERSON>hain", "warnForUnsupportedProcessApi", "callee", "getCodeAnalyzer", "handleExpression", "optimize", "InnerGraph", "onUsage", "used", "buildInfo", "getModuleBuildInfo", "Set", "handleWrapExpression", "expr", "ConstDependency", "dependencies", "dep1", "range", "addPresentationalDependency", "dep2", "handleWrapWasmCompileExpression", "handleWrapWasmInstantiateExpression", "handleImport", "source", "value", "importLocByPath", "Map", "importedModule", "toString", "set", "sourcePosition", "identifier", "sourceContent", "skip", "undefined", "prefix", "new", "importCall", "import", "codeAnalyzerBySwc", "modules", "binding", "constructor", "normalModule", "type", "diagnostics", "warnForEdgeRuntime", "diagnostic", "webpackError", "severity", "errors", "getExtractMetadata", "clear", "telemetry", "traceGlobals", "entryName", "entry", "entries", "route", "options", "EDGE_RUNTIME_WEBPACK", "entryDependency", "resolvedModule", "getResolvedModule", "addEntriesFromDependency", "dependency", "getModule", "add", "for<PERSON>ach", "includeDependencies", "entryMetadata", "preferredRegion", "ogImageGenerationCount", "resource", "hasOGImageGeneration", "test", "util", "getEntryRuntime", "id", "record", "eventName", "payload", "absolutePagePath", "config", "fileWithDynamicCode", "userRequest", "join", "getDynamicCodeEvaluationError", "nextEdgeSSR", "nextEdgeMiddleware", "nextEdgeApiFunction", "nextWasmMiddlewareBinding", "nextAssetMiddlewareBinding", "conn", "getModuleReferencesInOrder", "EVENT_BUILD_FEATURE_USAGE", "featureName", "invocationCount", "apply", "process", "NEXT_RSPACK", "finishModules", "tapPromise", "normalModuleFactory", "codeAnalyzer", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "supportedEdgePolyfills", "records", "mod", "request", "context", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er"], "mappings": ";;;;;;;;;;;;;;;;;IA03BaA,wBAAwB;eAAxBA;;IAzEb,OAuEC;eAvEoBC;;IAmFLC,wBAAwB;eAAxBA;;IASMC,mCAAmC;eAAnCA;;;4BAv4BkB;oCACL;uBACH;yBACC;kEACX;6DACL;2BAaV;wBAGsB;wBACa;0BACT;4BAI1B;oDAEoC;iDACG;wBACH;;;;;;AAE3C,MAAMC,8BACJC,QAAQ;AA6BV,MAAMC,OAAO;AACb,MAAMC,mBAAmB;AAEzB;;;;CAIC,GACD,SAASC,oCAAoCC,IAM5C;IACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAAA,OAAM,EAAEC,iBAAiB,EAAEC,EAAE,EAAE,GAAGL;IAChE,IAAI,OAAOI,sBAAsB,WAAW;QAC1C,OAAOA;IACT;IAEA,MAAME,cAAcL,YAAYM,cAAc,CAACJ;IAC/C,KAAK,MAAMK,cAAcJ,kBAAmB;QAC1C,IAAIE,YAAYG,OAAO,CAACD,YAAYN,aAAaG,GAAGK,UAAU,CAACC,MAAM,EAAE;YACrE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASC,cACPC,UAAoB,EACpBC,IAAmB,EACnBC,sBAA+B,EAC/BC,IAAa;IAEb,MAAMC,QAAkB,EAAE;IAC1B,IAAIH,KAAKI,OAAO,EAAE;QAChB,IAAIJ,KAAKI,OAAO,CAACC,iBAAiB,EAAE;YAClCF,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEC,oCAAyB,CAAC,GAAG,CAAC;YACnD,IAAIL,KAAKM,UAAU,EAAE;gBACnBL,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEG,yCAA8B,CAAC,GAAG,CAAC;YAC1D;YACAN,MAAMG,IAAI,IACLP,WACAW,MAAM,CACL,CAACC,OACCA,KAAKC,UAAU,CAAC,WAAW,CAACD,KAAKE,QAAQ,CAAC,mBAE7CC,GAAG,CACF,CAACH,OACC,YACAA,KAAKI,OAAO,CAAC,SAAS,MAAMC,oCAAyB,GAAG;QAGlE;QACA,IAAI,CAACd,KAAKe,GAAG,IAAI,CAACjB,KAAKI,OAAO,CAACc,QAAQ,EAAE;YACvCf,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEa,+BAAoB,CAAC,GAAG,CAAC;QAChD;QAEAhB,MAAMG,IAAI,CACR,CAAC,OAAO,EAAEc,oCAAyB,CAAC,GAAG,CAAC,EACxC,CAAC,OAAO,EAAEC,6CAAkC,CAAC,GAAG,CAAC,EACjD,CAAC,OAAO,EAAEC,6BAAkB,CAAC,GAAG,CAAC,EACjC,CAAC,OAAO,EAAEC,8CAAmC,CAAC,GAAG,CAAC;IAEtD;IAEA,IAAItB,wBAAwB;QAC1BE,MAAMG,IAAI,CAAC,CAAC,YAAY,EAAEkB,yCAA6B,CAAC,GAAG,CAAC;IAC9D;IAEArB,MAAMG,IAAI,IACLP,WACAW,MAAM,CAAC,CAACC,OAAS,CAACA,KAAKE,QAAQ,CAAC,mBAChCC,GAAG,CAAC,CAACH,OAAS,YAAYA;IAG/B,OAAOR;AACT;AAEA,SAASsB,gBAAgBC,MAIxB;IACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAE1B,IAAI,EAAE,GAAGwB;IAC/C,OAAO;QACL,MAAMG,qBAAyC;YAC7CC,SAAS9C;YACT+C,YAAY,CAAC;YACbC,WAAW,CAAC;YACZC,kBAAkB,EAAE;QACtB;QAEA,MAAMhC,yBAAyB0B,YAAYO,WAAW,CAACC,GAAG,CACxDX,yCAA6B;QAG/B,iGAAiG;QACjG,wFAAwF;QACxF,MAAMY,uBAAuBC,KAAKC,SAAS,CACzCpC,KAAKqC,QAAQ,CAACC,WAAW,CAAC9B,MAAM,CAAC+B,8DAA0B;QAE7Dd,YAAYe,SAAS,CACnB,GAAGnB,8CAAmC,CAAC,GAAG,CAAC,EAC3C,IAAIoB,gBAAO,CAACC,SAAS,CACnB,CAAC,2CAA2C,EAAEP,KAAKC,SAAS,CAC1DF,uBACC;QAIP,KAAK,MAAMS,cAAclB,YAAYO,WAAW,CAACY,MAAM,GAAI;gBAQvDC,0BACAA,mBACAA,2BAKoBA,oBASLA;YAvBjB,IAAI,CAACF,WAAWG,IAAI,EAAE;gBACpB;YACF;YAEA,sDAAsD;YACtD,MAAMD,WAAWnB,gBAAgBqB,GAAG,CAACJ,WAAWG,IAAI;YACpD,MAAME,OACJH,CAAAA,6BAAAA,2BAAAA,SAAUI,cAAc,qBAAxBJ,yBAA0BG,IAAI,MAC9BH,6BAAAA,oBAAAA,SAAU3C,OAAO,qBAAjB2C,kBAAmBG,IAAI,MACvBH,6BAAAA,4BAAAA,SAAUK,eAAe,qBAAzBL,0BAA2BG,IAAI;YACjC,IAAI,CAACA,MAAM;gBACT;YACF;YAEA,MAAMG,gBAAgBN,EAAAA,qBAAAA,SAAS3C,OAAO,qBAAhB2C,mBAAkB7B,QAAQ,IAC5CoC,IAAAA,0BAAgB,EAACJ,QACjBA;YAEJ,MAAMK,WAAW,CAACR,SAAS3C,OAAO,IAAI,CAAC2C,SAASK,eAAe;YAE/D,MAAM,EAAEI,UAAU,EAAE,GAAGC,IAAAA,mCAAuB,EAACJ,eAAe;gBAC5DE;YACF;YACA,MAAMG,WAAWX,CAAAA,6BAAAA,4BAAAA,SAAUI,cAAc,qBAAxBJ,0BAA0BW,QAAQ,KAAI;gBACrD;oBACEC,QAAQH;oBACRI,gBAAgBV,SAAS,OAAOK,WAAW,YAAYF;gBACzD;aACD;YAED,MAAMQ,iBAAiB,CAAC,CAAEd,CAAAA,SAASK,eAAe,IAAIL,SAAS3C,OAAO,AAAD;YACrE,MAAM0D,yBAAiD;gBACrD3D,OAAOL,cACL+C,WAAWkB,QAAQ,IACnBhB,UACA9C,wBACAC;gBAEF8C,MAAMH,WAAWG,IAAI;gBACrBE,MAAMA;gBACNQ;gBACAM,MAAMC,MAAMC,IAAI,CAACnB,SAASoB,YAAY,EAAE,CAAC,CAACnB,MAAMoB,SAAS,GAAM,CAAA;wBAC7DpB;wBACAoB;oBACF,CAAA;gBACAC,QAAQJ,MAAMC,IAAI,CAACnB,SAASuB,aAAa,EAAE,CAAC,CAACtB,MAAMoB,SAAS,GAAM,CAAA;wBAChEpB;wBACAoB;oBACF,CAAA;gBACAG,KAAKrE,KAAKsE,gBAAgB;gBAC1B,GAAIzB,SAAS0B,OAAO,IAAI;oBAAEA,SAAS1B,SAAS0B,OAAO;gBAAC,CAAC;YACvD;YAEA,IAAIZ,gBAAgB;gBAClBhC,mBAAmBG,SAAS,CAACkB,KAAK,GAAGY;YACvC,OAAO;gBACLjC,mBAAmBE,UAAU,CAACmB,KAAK,GAAGY;YACxC;QACF;QAEAjC,mBAAmBI,gBAAgB,GAAGyC,IAAAA,sBAAe,EACnDC,OAAOC,IAAI,CAAC/C,mBAAmBE,UAAU;QAG3CJ,YAAYe,SAAS,CACnBmC,8BAAmB,EACnB,IAAIlC,gBAAO,CAACC,SAAS,CACnBP,KAAKC,SAAS,CAACT,oBAAoB,MAAM;IAG/C;AACF;AAEA,SAASiD,kBAAkB,EACzBC,OAAO,EACPC,GAAG,EACHrD,WAAW,EACXsD,WAAW,EACXC,MAAM,EAOP;IACC,MAAMC,QAAQ,IAAIxD,YAAYyD,QAAQ,CAACC,OAAO,CAACC,YAAY,CAACP;IAC5DI,MAAMnC,IAAI,GAAGjE;IACb,MAAMM,UAAS4F,gBAAeC,0BAAAA,OAAQK,KAAK,CAACC,OAAO;IACnD,IAAInG,SAAQ;QACV8F,MAAM9F,MAAM,GAAGA;IACjB;IACA8F,MAAMH,GAAG,GAAGA;IACZ,OAAOG;AACT;AAEA,SAASM,oBAAoBP,MAA2C;QACxDA;IAAd,MAAMQ,SAAQR,uBAAAA,OAAOK,KAAK,CAAClG,MAAM,qBAAnB6F,qBAAqBQ,KAAK;IACxC,OAAOA,UAAUC,0BAAc,CAAC5D,UAAU,IAAI2D,UAAUC,0BAAc,CAACC,OAAO;AAChF;AAEA,SAASC,eAAeC,UAAkB;IACxC,OAAO,AAAChH,QAAQ,UAAsCiH,cAAc,CAACC,QAAQ,CAC3EF;AAEJ;AAEA,SAASG,+BACPC,QAAgB,EAChBC,gBAAmC,EACnCC,OAAgB;IAEhB,wEAAwE;IACxE,2DAA2D;IAC3D,IACEvH,4BAA4BwH,IAAI,CAAC,CAACC,MAChCJ,SAASF,QAAQ,CAAC,CAAC,cAAc,EAAEM,IAAI,CAAC,CAAC,CAACvF,OAAO,CAAC,OAAOwF,aAAI,CAACC,GAAG,KAEnE;QACA,OAAO;IACT;IAEA,MAAMxD,OAAOkD,SAASnF,OAAO,CAACqF,WAAW,IAAI;IAE7C,OAAOK,IAAAA,kBAAS,EAACN,CAAAA,oCAAAA,iBAAkBO,qBAAqB,KAAI,EAAE,EAAE;QAC9DC,KAAK;IACP,GAAG3D;AACL;AAEA,SAAS4D,yBAAyB,EAChCC,OAAO,EACP7B,GAAG,EACH,GAAG8B,MAMJ;IACC,OAAOhC,kBAAkB;QACvBC,SAAS,CAAC,uBAAuB,EAAE8B,QAAQ,UAAU,EAAE7B,IAAI+B,KAAK,CAACC,IAAI,CAAC;8DACZ,CAAC;QAC3DhC;QACA,GAAG8B,IAAI;IACT;AACF;AAEA,SAASG,4BACP/B,MAA2C,EAC3CvD,WAAgC;IAEhC,KAAK,MAAMuF,cAAcC,qCAA0B,CAAE;QACnD,MAAMC,wBAAwB,CAACC;YAC7B,IAAI,CAAC5B,oBAAoBP,SAAS;gBAChC;YACF;YACAvD,YAAY2F,QAAQ,CAAChH,IAAI,CACvBsG,yBAAyB;gBACvBjF;gBACAuD;gBACA2B,SAASK;gBACT,GAAGG,IAAI;YACT;YAEF,OAAO;QACT;QACAnC,OAAOqC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACP,YAAYQ,GAAG,CAAC3I,MAAMqI;QAC5ClC,OAAOqC,KAAK,CAACL,UAAU,CAACO,GAAG,CAACP,YAAYQ,GAAG,CAAC3I,MAAMqI;QAClDlC,OAAOqC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAACP,YACJQ,GAAG,CAAC3I,MAAMqI;QACblC,OAAOqC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAACP,YACJQ,GAAG,CAAC3I,MAAMqI;IACf;IAEA,MAAMS,+BAA+B,CAACR,MAAW,CAACS,OAAiB;QACjE,IAAI,CAACrC,oBAAoBP,WAAW4C,WAAW,OAAO;YACpD;QACF;QACAnG,YAAY2F,QAAQ,CAAChH,IAAI,CACvBsG,yBAAyB;YACvBjF;YACAuD;YACA2B,SAAS,CAAC,QAAQ,EAAEiB,QAAQ;YAC5B,GAAGT,IAAI;QACT;QAEF,OAAO;IACT;IAEAnC,OAAOqC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAAC,WACJC,GAAG,CAAC3I,MAAM8I;IACb3C,OAAOqC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAAC,WACJC,GAAG,CAAC3I,MAAM8I;AACf;AAEA,SAASE,gBAAgBrG,MAIxB;IACC,OAAO,CAACwD;QACN,MAAM,EACJjE,GAAG,EACHmE,UAAU,EAAEC,SAAS9F,EAAE,EAAE,EACzBoC,WAAW,EACZ,GAAGD;QACJ,MAAM,EAAE6F,KAAK,EAAE,GAAGrC;QAElB;;;;;KAKC,GACD,MAAM8C,mBAAmB;YACvB,IAAI,CAACvC,oBAAoBP,SAAS;gBAChC;YACF;YAEA3F,GAAG0I,QAAQ,CAACC,UAAU,CAACC,OAAO,CAACjD,OAAOK,KAAK,EAAE,CAAC6C,OAAO,IAAI;gBACvD,MAAMC,YAAYC,IAAAA,sCAAkB,EAACpD,OAAOK,KAAK,CAAClG,MAAM;gBACxD,IAAIgJ,UAAU/I,iBAAiB,KAAK,QAAQ8I,SAAS,OAAO;oBAC1D;gBACF;gBAEA,IAAI,CAACC,UAAU/I,iBAAiB,IAAI8I,SAAS,MAAM;oBACjDC,UAAU/I,iBAAiB,GAAG8I;oBAC9B;gBACF;gBAEAC,UAAU/I,iBAAiB,GAAG,IAAIiJ,IAAI;uBACjCtE,MAAMC,IAAI,CAACmE,UAAU/I,iBAAiB;uBACtC2E,MAAMC,IAAI,CAACkE;iBACf;YACH;QACF;QAEA;;;;KAIC,GACD,MAAMI,uBAAuB,CAACC;YAC5B,IAAI,CAAChD,oBAAoBP,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEwD,eAAe,EAAE,GAAGnJ,GAAGoJ,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,sCACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAK5D,GAAG,GAAGyD,KAAKzD,GAAG;YACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAK/D,GAAG,GAAGyD,KAAKzD,GAAG;YACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACC;YAEhDf;YACA,OAAO;QACT;QAEA;;;;KAIC,GACD,MAAMgB,kCAAkC,CAACP;YACvC,IAAI,CAAChD,oBAAoBP,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEwD,eAAe,EAAE,GAAGnJ,GAAGoJ,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,qDACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAK5D,GAAG,GAAGyD,KAAKzD,GAAG;YACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAK/D,GAAG,GAAGyD,KAAKzD,GAAG;YACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACC;YAEhDf;QACF;QAEA;;;;;;;;KAQC,GACD,MAAMiB,sCAAsC,CAACR;YAC3C,IAAI,CAAChD,oBAAoBP,SAAS;gBAChC;YACF;YAEA,IAAIjE,KAAK;gBACP,MAAM,EAAEyH,eAAe,EAAE,GAAGnJ,GAAGoJ,YAAY;gBAC3C,MAAMC,OAAO,IAAIF,gBACf,yDACAD,KAAKI,KAAK,CAAC,EAAE;gBAEfD,KAAK5D,GAAG,GAAGyD,KAAKzD,GAAG;gBACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACF;gBAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;gBACpDE,KAAK/D,GAAG,GAAGyD,KAAKzD,GAAG;gBACnBE,OAAOK,KAAK,CAAClG,MAAM,CAACyJ,2BAA2B,CAACC;YAClD;QACF;QAEA;;KAEC,GACD,MAAMG,eAAe,CAAC7B;gBACeA;YAAnC,IAAI5B,oBAAoBP,aAAWmC,eAAAA,KAAK8B,MAAM,qBAAX9B,aAAa+B,KAAK,MAAI/B,wBAAAA,KAAMrC,GAAG,GAAE;oBAO3CqC;gBANvB,MAAM,EAAEhI,QAAAA,OAAM,EAAE8J,MAAM,EAAE,GAAGjE,OAAOK,KAAK;gBACvC,MAAM8C,YAAYC,IAAAA,sCAAkB,EAACjJ;gBACrC,IAAI,CAACgJ,UAAUgB,eAAe,EAAE;oBAC9BhB,UAAUgB,eAAe,GAAG,IAAIC;gBAClC;gBAEA,MAAMC,kBAAiBlC,qBAAAA,KAAK8B,MAAM,CAACC,KAAK,qBAAjB/B,mBAAmBmC,QAAQ;gBAClDnB,UAAUgB,eAAe,CAACI,GAAG,CAACF,gBAAgB;oBAC5CG,gBAAgB;wBACd,GAAGrC,KAAKrC,GAAG,CAAC+B,KAAK;wBACjBoC,QAAQ9J,QAAOsK,UAAU;oBAC3B;oBACAC,eAAeT,OAAOK,QAAQ;gBAChC;gBAEA,IACE,CAACvI,OACD4E,eAAe0D,mBACf,CAAC9K,yBAAyBuH,QAAQ,CAACuD,iBACnC;oBACA5H,YAAY2F,QAAQ,CAAChH,IAAI,CACvBwE,kBAAkB;wBAChBC,SAAS,CAAC,6BAA6B,EAAEwE,eAAe,UAAU,EAAElC,KAAKrC,GAAG,CAAC+B,KAAK,CAACC,IAAI,CAAC;wEAC9B,CAAC;wBAC3DrF;wBACAuD;wBACA,GAAGmC,IAAI;oBACT;gBAEJ;YACF;QACF;QAEA;;;KAGC,GACD,MAAMwC,OAAO,IAAOpE,oBAAoBP,UAAU,OAAO4E;QAEzD,KAAK,MAAMC,UAAU;YAAC;YAAI;SAAU,CAAE;YACpCxC,MAAML,UAAU,CAACO,GAAG,CAAC,GAAGsC,OAAO,kBAAkB,CAAC,EAAErC,GAAG,CAAC3I,MAAM8K;YAC9DtC,MAAML,UAAU,CAACO,GAAG,CAAC,GAAGsC,OAAO,aAAa,CAAC,EAAErC,GAAG,CAAC3I,MAAM8K;YACzDtC,MAAMC,IAAI,CAACC,GAAG,CAAC,GAAGsC,OAAO,IAAI,CAAC,EAAErC,GAAG,CAAC3I,MAAMyJ;YAC1CjB,MAAMC,IAAI,CAACC,GAAG,CAAC,GAAGsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAAC3I,MAAMyJ;YAC9CjB,MAAMyC,GAAG,CAACvC,GAAG,CAAC,GAAGsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAAC3I,MAAMyJ;YAC7CjB,MAAMC,IAAI,CACPC,GAAG,CAAC,GAAGsC,OAAO,mBAAmB,CAAC,EAClCrC,GAAG,CAAC3I,MAAMiK;YACbzB,MAAMC,IAAI,CACPC,GAAG,CAAC,GAAGsC,OAAO,uBAAuB,CAAC,EACtCrC,GAAG,CAAC3I,MAAMkK;QACf;QAEA1B,MAAM0C,UAAU,CAACvC,GAAG,CAAC3I,MAAMmK;QAC3B3B,MAAM2C,MAAM,CAACxC,GAAG,CAAC3I,MAAMmK;QAEvB,IAAI,CAACjI,KAAK;YACR,8EAA8E;YAC9EgG,4BAA4B/B,QAAQvD;QACtC;IACF;AACF;AAEA,eAAewI,kBACbxI,WAAgC,EAChCyI,OAAiC,EACjCnJ,GAAY;IAEZ,MAAMoJ,UAAUvL,QAAQ;IACxB,KAAK,MAAMO,WAAU+K,QAAS;QAC5B,IACE/K,QAAOqG,KAAK,KAAKC,0BAAc,CAAC5D,UAAU,IAC1C1C,QAAOqG,KAAK,KAAKC,0BAAc,CAACC,OAAO,EACvC;YACA;QACF;QACA,IAAIvG,QAAOiL,WAAW,CAACtH,IAAI,KAAK,gBAAgB;YAC9C;QACF;QACA,MAAMuH,eAAelL;QACrB,IAAI,CAACkL,aAAaC,IAAI,CAAC5J,UAAU,CAAC,eAAe;YAE/C;QACF;QACA,MAAMgD,iBAAiB2G,aAAa3G,cAAc;QAClD,IAAI,CAACA,gBAAgB;YACnB;QACF;QACA,MAAMuF,SAASvF,eAAeuF,MAAM;QACpC,IAAI,OAAOA,WAAW,UAAU;YAC9B;QACF;QACA,MAAMsB,cAAc,MAAMJ,QAAQK,kBAAkB,CAACvB,QAAQ,CAAClI;QAC9D,KAAK,MAAM0J,cAAcF,YAAa;YACpC,MAAMG,eAAe9F,kBAAkB;gBACrCC,SAAS4F,WAAW5F,OAAO;gBAC3BC,KAAK2F,WAAW3F,GAAG;gBACnBrD;gBACAsD,aAAa5F;YACf;YACA,IAAIsL,WAAWE,QAAQ,KAAK,WAAW;gBACrClJ,YAAY2F,QAAQ,CAAChH,IAAI,CAACsK;YAC5B,OAAO;gBACLjJ,YAAYmJ,MAAM,CAACxK,IAAI,CAACsK;YAC1B;QACF;IACF;AACF;AAEA,SAASG,mBAAmBrJ,MAK3B;IACC,MAAM,EAAET,GAAG,EAAEU,WAAW,EAAEC,eAAe,EAAEwD,QAAQ,EAAE,GAAG1D;IACxD,MAAM,EAAE2D,SAAS9F,EAAE,EAAE,GAAG6F;IACxB,OAAO;QACLxD,gBAAgBoJ,KAAK;QACrB,MAAMC,YAAmCC,oBAAY,CAACjI,GAAG,CAAC;QAE1D,KAAK,MAAM,CAACkI,WAAWC,MAAM,IAAIzJ,YAAY0J,OAAO,CAAE;gBAK5BD,qBAyBpBE;YA7BJ,IAAIF,MAAMG,OAAO,CAACnM,OAAO,KAAKoM,+BAAoB,EAAE;gBAElD;YACF;YACA,MAAMC,mBAAkBL,sBAAAA,MAAMzC,YAAY,qBAAlByC,mBAAoB,CAAC,EAAE;YAC/C,MAAMM,iBACJ/J,YAAYxC,WAAW,CAACwM,iBAAiB,CAACF;YAC5C,IAAI,CAACC,gBAAgB;gBACnB;YACF;YACA,MAAM,EAAEtF,OAAO,EAAEkF,KAAK,EAAE,GAAGhD,IAAAA,sCAAkB,EAACoD;YAE9C,MAAM,EAAEvM,WAAW,EAAE,GAAGwC;YACxB,MAAMyI,UAAU,IAAI7B;YACpB,MAAMqD,2BAA2B,CAACC;gBAChC,MAAMxM,UAASF,YAAY2M,SAAS,CAACD;gBACrC,IAAIxM,SAAQ;oBACV+K,QAAQ2B,GAAG,CAAC1M;gBACd;YACF;YAEA+L,MAAMzC,YAAY,CAACqD,OAAO,CAACJ;YAC3BR,MAAMa,mBAAmB,CAACD,OAAO,CAACJ;YAElC,MAAMM,gBAA+B;gBACnC/H,cAAc,IAAImF;gBAClBhF,eAAe,IAAIgF;YACrB;YAEA,IAAIgC,0BAAAA,0BAAAA,MAAOnF,gBAAgB,qBAAvBmF,wBAAyB7G,OAAO,EAAE;gBACpCyH,cAAczH,OAAO,GAAG6G,MAAMnF,gBAAgB,CAAC1B,OAAO;YACxD;YAEA,IAAI6G,yBAAAA,MAAOa,eAAe,EAAE;gBAC1B,MAAMA,kBAAkBb,MAAMa,eAAe;gBAC7CD,cAAczH,OAAO,GACnB,8DAA8D;gBAC9D,OAAO0H,oBAAoB,WACvB;oBAACA;iBAAgB,GACjBA;YACR;YAEA,IAAIC,yBAAyB;YAE7B,KAAK,MAAM/M,WAAU+K,QAAS;gBAC5B,MAAM/B,YAAYC,IAAAA,sCAAkB,EAACjJ;gBAErC;;SAEC,GACD,IAAI,CAAC4B,KAAK;oBACR,MAAMoL,WAAWhN,QAAOgN,QAAQ;oBAChC,MAAMC,uBACJD,YACA,oJAAoJE,IAAI,CACtJF;oBAGJ,IAAIC,sBAAsB;wBACxBF;oBACF;gBACF;gBAEA;;;;SAIC,GACD,IACE,CAACnL,OACDoH,UAAU/I,iBAAiB,IAC3BL,oCAAoC;oBAClCI,QAAAA;oBACAF;oBACAC,SAASG,GAAGiN,IAAI,CAACpN,OAAO,CAACqN,eAAe,CAAC9K,aAAawJ;oBACtD7L,mBAAmB+I,UAAU/I,iBAAiB;oBAC9CC;gBACF,IACA;wBAKI+L;oBAJJ,MAAMoB,KAAKrN,QAAOsK,UAAU;oBAC5B,IAAI,uDAAuD4C,IAAI,CAACG,KAAK;wBACnE;oBACF;oBACA,IAAIpB,0BAAAA,2BAAAA,MAAOnF,gBAAgB,qBAAvBmF,yBAAyB5E,qBAAqB,EAAE;wBAClDuE,6BAAAA,UAAW0B,MAAM,CAAC;4BAChBC,WAAW;4BACXC,SAAS;gCACPlM,IAAI,EAAE2K,yBAAAA,MAAOwB,gBAAgB,CAAC/L,OAAO,CAACqF,WAAW,IAAI;gCACrD2G,MAAM,EAAEzB,yBAAAA,MAAOnF,gBAAgB;gCAC/B6G,qBAAqB3N,QAAO4N,WAAW,CAAClM,OAAO,CAC7CqF,WAAW,IACX;4BAEJ;wBACF;oBACF;oBACA,IACE,CAACH,+BACC5G,QAAO4N,WAAW,EAClB3B,yBAAAA,MAAOnF,gBAAgB,EACvBC,UAEF;wBACA,MAAMrB,UAAU,CAAC,0GAA0G,EACzH,OAAOsD,UAAU/I,iBAAiB,KAAK,YACnC,CAAC,UAAU,EAAE2E,MAAMC,IAAI,CAACmE,UAAU/I,iBAAiB,EAAE4N,IAAI,CACvD,OACC,GACH,GACL,2EAA2E,CAAC;wBAC7EvL,YAAYmJ,MAAM,CAACxK,IAAI,CACrB6M,IAAAA,8DAA6B,EAC3BpI,SACA1F,SACAsC,aACAyD;oBAGN;gBACF;gBAEA;;;SAGC,GACD,IAAIiD,6BAAAA,UAAW+E,WAAW,EAAE;oBAC1BlB,cAAc9L,OAAO,GAAGiI,UAAU+E,WAAW;gBAC/C,OAAO,IAAI/E,6BAAAA,UAAWgF,kBAAkB,EAAE;oBACxCnB,cAAc/I,cAAc,GAAGkF,UAAUgF,kBAAkB;gBAC7D,OAAO,IAAIhF,6BAAAA,UAAWiF,mBAAmB,EAAE;oBACzCpB,cAAc9I,eAAe,GAAGiF,UAAUiF,mBAAmB;gBAC/D;gBAEA;;;SAGC,GACD,IAAIjF,6BAAAA,UAAWkF,yBAAyB,EAAE;oBACxCrB,cAAc/H,YAAY,CAACsF,GAAG,CAC5BpB,UAAUkF,yBAAyB,CAACvK,IAAI,EACxCqF,UAAUkF,yBAAyB,CAACnJ,QAAQ;gBAEhD;gBAEA,IAAIiE,6BAAAA,UAAWmF,0BAA0B,EAAE;oBACzCtB,cAAc5H,aAAa,CAACmF,GAAG,CAC7BpB,UAAUmF,0BAA0B,CAACxK,IAAI,EACzCqF,UAAUmF,0BAA0B,CAACpJ,QAAQ;gBAEjD;gBAEA;;;SAGC,GACD,KAAK,MAAMqJ,QAAQC,IAAAA,kCAA0B,EAACrO,SAAQF,aAAc;oBAClE,IAAIsO,KAAKpO,MAAM,EAAE;wBACf+K,QAAQ2B,GAAG,CAAC0B,KAAKpO,MAAM;oBACzB;gBACF;YACF;YAEA4L,6BAAAA,UAAW0B,MAAM,CAAC;gBAChBC,WAAWe,iCAAyB;gBACpCd,SAAS;oBACPe,aAAa;oBACbC,iBAAiBzB;gBACnB;YACF;YACAxK,gBAAgB6H,GAAG,CAAC0B,WAAWe;QACjC;IACF;AACF;AAiBe,MAAMxN;IAMnB4L,YAAY,EAAErJ,GAAG,EAAET,UAAU,EAAE+B,QAAQ,EAAEiC,gBAAgB,EAAW,CAAE;QACpE,IAAI,CAACvD,GAAG,GAAGA;QACX,IAAI,CAACT,UAAU,GAAGA;QAClB,IAAI,CAAC+B,QAAQ,GAAGA;QAChB,IAAI,CAACiC,gBAAgB,GAAGA;IAC1B;IAEOsJ,MAAM1I,QAA0B,EAAE;QACvCA,SAASmC,KAAK,CAAC5F,WAAW,CAAC+F,GAAG,CAAC3I,MAAM,CAAC4C,aAAaD;YACjD,0CAA0C;YAC1C,IAAIqM,QAAQxJ,GAAG,CAACyJ,WAAW,EAAE;gBAC3BrM,YAAY4F,KAAK,CAAC0G,aAAa,CAACC,UAAU,CAACnP,MAAM,OAAOqL;oBACtD,MAAMD,kBAAkBxI,aAAayI,SAAS,IAAI,CAACnJ,GAAG;gBACxD;YACF,OAAO;gBACL,MAAM,EAAEsG,KAAK,EAAE,GAAG7F,OAAOyM,mBAAmB;gBAC5C;;SAEC,GACD,MAAMC,eAAerG,gBAAgB;oBACnC9G,KAAK,IAAI,CAACA,GAAG;oBACbmE;oBACAzD;gBACF;gBAEA4F,MAAMrC,MAAM,CAACuC,GAAG,CAAC,mBAAmBC,GAAG,CAAC3I,MAAMqP;gBAC9C7G,MAAMrC,MAAM,CAACuC,GAAG,CAAC,sBAAsBC,GAAG,CAAC3I,MAAMqP;gBACjD7G,MAAMrC,MAAM,CAACuC,GAAG,CAAC,kBAAkBC,GAAG,CAAC3I,MAAMqP;YAC/C;YAEA;;OAEC,GACD,MAAMxM,kBAAkB,IAAI0H;YAC5B3H,YAAY4F,KAAK,CAAC0G,aAAa,CAACC,UAAU,CACxCnP,MACAgM,mBAAmB;gBACjBpJ;gBACAyD;gBACAnE,KAAK,IAAI,CAACA,GAAG;gBACbW;YACF;YAGF;;OAEC,GACDD,YAAY4F,KAAK,CAAC8G,aAAa,CAAC3G,GAAG,CACjC;gBACE1E,MAAM;gBACNsL,OAAOjJ,gBAAO,CAACkJ,WAAW,CAACC,8BAA8B;YAC3D,GACA/M,gBAAgB;gBACdE;gBACAC;gBACA1B,MAAM;oBACJM,YAAY,IAAI,CAACA,UAAU;oBAC3B+B,UAAU,IAAI,CAACA,QAAQ;oBACvBiC,kBAAkB,IAAI,CAACA,gBAAgB;oBACvCvD,KAAK,IAAI,CAACA,GAAG;gBACf;YACF;QAEJ;IACF;AACF;AAEO,MAAMxC,2BAA2B;IACtC;IACA;IACA;IACA;IACA;CACD;AAED,MAAMgQ,yBAAyB,IAAIlG,IAAY9J;AAExC,SAASE;IACd,MAAM+P,UAAkC,CAAC;IACzC,KAAK,MAAMC,OAAOlQ,yBAA0B;QAC1CiQ,OAAO,CAACC,IAAI,GAAG,CAAC,cAAc,EAAEA,KAAK;QACrCD,OAAO,CAAC,CAAC,KAAK,EAAEC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAEA,KAAK;IACjD;IACA,OAAOD;AACT;AAEO,eAAe9P,oCAAoC,EACxDgQ,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,UAAU,EAMX;IACC,IACE,AAACD,CAAAA,YAAYE,WAAW,KAAKrJ,0BAAc,CAAC5D,UAAU,IACpD+M,YAAYE,WAAW,KAAKrJ,0BAAc,CAACC,OAAO,AAAD,KACnDC,eAAe+I,YACf,CAACH,uBAAuBtM,GAAG,CAACyM,UAC5B;QACA,wEAAwE;QACxE,IAAI;YACF,MAAMG,aAAaF,SAASD;QAC9B,EAAE,OAAM;YACN,OAAO,CAAC,sCAAsC,EAAEA,QAAQ,EAAE,CAAC;QAC7D;IACF;AACF", "ignoreList": [0]}