export default {
  tie: {
    game: { fallback: true, words: ['game', 'score', 'point'] },
    suit: { words: ['bow', 'wear', 'style'] },
  },
  chip: {
    computer: { words: ['speed', 'semiconductor'] },
    potato: { words: ['ruffles', 'spice', 'bag', 'salt'] },
    part: { words: ['block', 'off'] },
  },
  pitch: {
    throw: { words: ['baseball', 'strike', 'mound', 'pitcher', 'fastball'] },
    field: { words: ['football', 'striker', 'soccer', 'keeper'] },
  },
  stock: {
    finance: { words: ['purchase', 'price'] },
    soup: { words: ['vegetable', 'beef', 'soup'] },
    backup: { words: ['collection', 'basement'] },
  },
  fall: {
    drop: { fallback: true, words: ['stair', 'break', 'height', 'break'] },
    season: { words: ['october', 'leaf', 'september', 'color'] },
  },
  spring: {
    coil: { words: ['bounce', 'matress'] },
    season: { words: ['april', 'seed', 'sprout', 'march', 'cleaning'] },
  },
  march: {
    month: { words: ['spring', 'april', 'february'] },
    crowd: { fallback: true, words: ['protest', 'crowd', 'against'] },
  },
  model: {
    person: { words: ['swimsuit', 'makeup', 'fashion'] },
    concept: { words: ['mental', 'conceptual', 'theory'] },
    computer: { words: ['3d', 'render'] },
  },
  hook: {
    music: { words: ['chorus', 'melody', 'verse'] },
    shape: { fallback: true, words: ['fish', 'hook', 'lure'] },
  },
  ball: {
    dance: { words: ['musical', 'gown'] },
    object: { fallback: true, words: ['bounce', 'kick'] },
  },
  trip: {
    drug: { words: ['lsd', 'acid'] },
    travel: { fallback: true, words: ['vacation', 'drive', 'fly', 'pack'] },
  },
  course: {
    path: { words: ['mountain', 'trail', 'chart'] },
    school: { fallback: true, words: ['class', 'register', 'teach', 'teacher'] },
  },
  power: {
    political: { fallback: true, words: ['politics', 'election', 'seek', 'hungry'] },
    electrical: { words: ['consume', 'energy', 'appliance'] },
  },
  burn: {
    insult: { words: ['sick'] },
    fire: { fallback: true, words: ['degree', 'fire', 'heat'] },
  },
}
