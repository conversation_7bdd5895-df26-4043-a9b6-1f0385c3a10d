{"version": 3, "sources": ["../../src/build/next-dir-paths.ts"], "sourcesContent": ["import path from 'path'\n\nexport const NEXT_PROJECT_ROOT = path.join(__dirname, '..', '..')\nexport const NEXT_PROJECT_ROOT_DIST = path.join(NEXT_PROJECT_ROOT, 'dist')\nexport const NEXT_PROJECT_ROOT_DIST_CLIENT = path.join(\n  NEXT_PROJECT_ROOT_DIST,\n  'client'\n)\n"], "names": ["NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST", "NEXT_PROJECT_ROOT_DIST_CLIENT", "path", "join", "__dirname"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,iBAAiB;eAAjBA;;IACAC,sBAAsB;eAAtBA;;IACAC,6BAA6B;eAA7BA;;;6DAJI;;;;;;AAEV,MAAMF,oBAAoBG,aAAI,CAACC,IAAI,CAACC,WAAW,MAAM;AACrD,MAAMJ,yBAAyBE,aAAI,CAACC,IAAI,CAACJ,mBAAmB;AAC5D,MAAME,gCAAgCC,aAAI,CAACC,IAAI,CACpDH,wBACA", "ignoreList": [0]}