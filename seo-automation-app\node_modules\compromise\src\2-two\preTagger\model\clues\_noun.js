const n = 'Singular'
export default {
  beforeTags: {
    Determiner: n, //the date
    Possessive: n, //his date
    Acronym: n, //u.s. state
    // ProperNoun:n,
    Noun: n, //nasa funding
    Adjective: n, //whole bottles
    // Verb:true, //save storm victims
    PresentTense: n, //loves hiking
    Gerund: n, //uplifting victims
    PastTense: n, //saved storm victims
    Infinitive: n, //profess love
    Date: n, //9pm show
    Ordinal: n, //first date
    Demonym: n, //dutch map
  },
  afterTags: {
    Value: n, //date nine  -?
    Modal: n, //date would
    Copula: n, //fear is
    PresentTense: n, //babysitting sucks
    PastTense: n, //babysitting sucked
    // Noun:n, //talking therapy, planning process
    Demonym: n, //american touch
    Actor: n, //dance therapist
  },
  // ownTags: { ProperNoun: n },
  beforeWords: {
    the: n, //the brands
    with: n, //with cakes
    without: n, //
    // was:n, //was time  -- was working
    // is:n, //
    of: n, //of power
    for: n, //for rats
    any: n, //any rats
    all: n, //all tips
    on: n, //on time
    // thing-ish verbs
    cut: n, //cut spending
    cuts: n, //cut spending
    increase: n, // increase funding
    decrease: n, //
    raise: n, //
    drop: n, //
    // give: n,//give parents
    save: n, //
    saved: n, //
    saves: n, //
    make: n, //
    makes: n, //
    made: n, //
    minus: n, //minus laughing
    plus: n, //
    than: n, //more than age
    another: n, //
    versus: n, //
    neither: n, //
    about: n, //about claims
    // strong adjectives
    favorite: n, //
    best: n, //
    daily: n, //
    weekly: n, //
    linear: n, //
    binary: n, //
    mobile: n, //
    lexical: n, //
    technical: n, //
    computer: n, //
    scientific: n, //
    security: n, //
    government: n, //
    popular: n, //
    formal: n,
    no: n, //no worries
    more: n, //more details
    one: n, //one flood
    let: n, //let fear
    her: n, //her boots
    his: n, //
    their: n, //
    our: n, //
    us: n, //served us drinks
    sheer: n,

    monthly: n,
    yearly: n,
    current: n,
    previous: n,
    upcoming: n,
    last: n,
    next: n,
    main: n,
    initial: n,
    final: n,
    beginning: n,
    end: n,
    top: n,
    bottom: n,
    future: n,
    past: n,
    major: n,
    minor: n,
    side: n,
    central: n,
    peripheral: n,
    public: n,
    private: n,
  },
  afterWords: {
    of: n, //date of birth (preposition)
    system: n,
    aid: n,
    method: n,
    utility: n,
    tool: n,
    reform: n,
    therapy: n,
    philosophy: n,
    room: n,
    authority: n,
    says: n,
    said: n,
    wants: n,
    wanted: n,
    is: n,
    did: n,
    do: n,
    can: n, //parents can
    wise: n, //service-wise
    // they: n,//snakes they
  },
}
