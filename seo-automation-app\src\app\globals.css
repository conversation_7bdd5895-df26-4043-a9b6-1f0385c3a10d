@import "tailwindcss";

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --radius: var(--radius);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: system-ui, -apple-system, sans-serif;
}

/* Responsive Design System */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Mobile-first responsive grid */
.grid-responsive {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Touch-optimized interactions */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive typography */
.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-responsive-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-responsive-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .text-responsive-xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  .text-responsive-2xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  
  .text-responsive-3xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* Responsive spacing */
.spacing-responsive {
  padding: 1rem;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: 2rem;
  }
}

/* Mobile navigation transitions */
.mobile-nav-enter {
  transform: translateX(-100%);
  opacity: 0;
}

.mobile-nav-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.mobile-nav-exit {
  transform: translateX(0);
  opacity: 1;
}

.mobile-nav-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* Responsive content areas */
.content-responsive {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

@media (min-width: 768px) {
  .content-responsive {
    overflow-x: visible;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --ring: 0 0% 0%;
  }
  
  @media (prefers-color-scheme: dark) {
    :root {
      --border: 0 0% 100%;
      --input: 0 0% 100%;
      --ring: 0 0% 100%;
    }
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Accessibility Settings Classes */
/* High contrast mode */
.high-contrast {
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 0%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 90%;
  --secondary-foreground: 0 0% 0%;
  --muted: 0 0% 90%;
  --muted-foreground: 0 0% 20%;
  --accent: 0 0% 90%;
  --accent-foreground: 0 0% 0%;
  --border: 0 0% 0%;
  --input: 0 0% 0%;
  --ring: 0 0% 0%;
}

.high-contrast.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 0%;
  --card-foreground: 0 0% 100%;
  --popover: 0 0% 0%;
  --popover-foreground: 0 0% 100%;
  --primary: 0 0% 100%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 10%;
  --secondary-foreground: 0 0% 100%;
  --muted: 0 0% 10%;
  --muted-foreground: 0 0% 80%;
  --accent: 0 0% 10%;
  --accent-foreground: 0 0% 100%;
  --border: 0 0% 100%;
  --input: 0 0% 100%;
  --ring: 0 0% 100%;
}

/* Large text mode */
.large-text {
  font-size: 120%;
}

.large-text .text-responsive-sm {
  font-size: 1.05rem;
  line-height: 1.5rem;
}

.large-text .text-responsive-base {
  font-size: 1.2rem;
  line-height: 1.8rem;
}

.large-text .text-responsive-lg {
  font-size: 1.35rem;
  line-height: 2.1rem;
}

.large-text .text-responsive-xl {
  font-size: 1.5rem;
  line-height: 2.1rem;
}

.large-text .text-responsive-2xl {
  font-size: 1.8rem;
  line-height: 2.4rem;
}

.large-text .text-responsive-3xl {
  font-size: 2.25rem;
  line-height: 2.7rem;
}

/* Reduced motion override */
.reduced-motion *,
.reduced-motion ::before,
.reduced-motion ::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Enhanced focus indicators */
.focus-visible *:focus {
  outline: 3px solid hsl(var(--ring)) !important;
  outline-offset: 2px !important;
  border-radius: 4px !important;
}

.focus-visible *:focus:not(:focus-visible) {
  outline: none !important;
}

/* Font size classes */
.text-small {
  font-size: 90%;
}

.text-medium {
  font-size: 100%;
}

.text-large {
  font-size: 120%;
}

.text-xl {
  font-size: 140%;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Keyboard navigation enhancements */
.keyboard-nav *[tabindex="-1"]:focus {
  outline: none;
}

.keyboard-nav button:focus,
.keyboard-nav a:focus,
.keyboard-nav input:focus,
.keyboard-nav select:focus,
.keyboard-nav textarea:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Touch accessibility */
@media (pointer: coarse) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

/* Hover and focus states for accessibility */
.accessible-hover:hover,
.accessible-hover:focus {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Skip link styling */
.skip-link:focus {
  position: absolute !important;
  top: 1rem !important;
  left: 1rem !important;
  z-index: 9999 !important;
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.375rem !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}
