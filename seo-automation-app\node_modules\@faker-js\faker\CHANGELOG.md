# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [9.9.0](https://github.com/faker-js/faker/compare/v9.8.0...v9.9.0) (2025-07-01)


### New Locales

* **locale:** add word data to pt_br and pt_pt locales ([#3531](https://github.com/faker-js/faker/issues/3531)) ([a405ac8](https://github.com/faker-js/faker/commit/a405ac8740bcfb2ec5f84c06752484a2b332a90a))


### Features

* **location:** simple coordinate methods ([#3528](https://github.com/faker-js/faker/issues/3528)) ([d07d96d](https://github.com/faker-js/faker/commit/d07d96d01833085f2d3c5f9c851a572ebf8c47df))

## [9.8.0](https://github.com/faker-js/faker/compare/v9.7.0...v9.8.0) (2025-05-13)


### New Locales

* **locale:** Add additional Japanese last names to the locale data ([#3484](https://github.com/faker-js/faker/issues/3484)) ([72e66c3](https://github.com/faker-js/faker/commit/72e66c3a3a22738ce163b26dc5315c82f49032e7))
* **locale:** add Japanese date and month definitions ([#3492](https://github.com/faker-js/faker/issues/3492)) ([b70e793](https://github.com/faker-js/faker/commit/b70e7934b7b3181d8f3fbe98627985cd71fbfe26))
* **locale:** add Japanese science locale data including elements and units ([#3491](https://github.com/faker-js/faker/issues/3491)) ([54fd551](https://github.com/faker-js/faker/commit/54fd5519e92270926e75a914ccf98b4699fbb4f2))
* **locale:** add Japanese sex definitions for person locale ([#3495](https://github.com/faker-js/faker/issues/3495)) ([1dbd8fa](https://github.com/faker-js/faker/commit/1dbd8fa511e0c69ed73324a330f16f9aa1a6670a))
* **locale:** add vehicle locale data for Japanese ([#3490](https://github.com/faker-js/faker/issues/3490)) ([dfadb1d](https://github.com/faker-js/faker/commit/dfadb1da746e7bf82b59dc5314bb9f0735b0f37d))
* **locale:** add zh_CN book ([#3477](https://github.com/faker-js/faker/issues/3477)) ([786a3d0](https://github.com/faker-js/faker/commit/786a3d0bd8d72d2bf2cfc38358c45d2960848654))
* **locale:** add zh_CN food ([#3479](https://github.com/faker-js/faker/issues/3479)) ([6c883e7](https://github.com/faker-js/faker/commit/6c883e74b894f1b777291e67d5b2b7bc63e38a80))
* **locale:** update Japanese company categories ([#3489](https://github.com/faker-js/faker/issues/3489)) ([8c0953a](https://github.com/faker-js/faker/commit/8c0953a2617f3c8ddd95320af46063d83d115ddb))
* **locale:** update zh_CN animal ([#3480](https://github.com/faker-js/faker/issues/3480)) ([38ee7b8](https://github.com/faker-js/faker/commit/38ee7b81a82c466710586091e7046d7bfcbed337))
* **locale:** update zh_CN location ([#3481](https://github.com/faker-js/faker/issues/3481)) ([456f102](https://github.com/faker-js/faker/commit/456f10276bfbc1a654162e94e5815ca01ffa7fdd))
* **locale:** update zh_CN word ([#3478](https://github.com/faker-js/faker/issues/3478)) ([aa98867](https://github.com/faker-js/faker/commit/aa98867765093588ea2fbda2daa6cbce40b20d5f))


### Changed Locales

* **locale:** ko state data update ([#3487](https://github.com/faker-js/faker/issues/3487)) ([b611ec2](https://github.com/faker-js/faker/commit/b611ec2e519bc0be6d53e81fb333fcb15c3964f1))
* **locale:** normalize internet data ([#3502](https://github.com/faker-js/faker/issues/3502)) ([e6151e4](https://github.com/faker-js/faker/commit/e6151e4efd29f360b82ff25c4ddb2ffae8e30e86))
* **locale:** rename pt-BR streetSuffix to streetPrefix ([#3493](https://github.com/faker-js/faker/issues/3493)) ([7c23db3](https://github.com/faker-js/faker/commit/7c23db316e794aab2a9181e0f960e094bea71054))


### Bug Fixes

* **locale:** correct Japanese country names ([#3510](https://github.com/faker-js/faker/issues/3510)) ([046bb81](https://github.com/faker-js/faker/commit/046bb8155850b9fee0c22a0177e695c3ba0d3a1a))
* **locale:** correct the name of element Lv in Japanese ([#3509](https://github.com/faker-js/faker/issues/3509)) ([6a7ef4c](https://github.com/faker-js/faker/commit/6a7ef4c2ad97f50e9a47efd0fa7c22804fc9d4f1))
* **locale:** ko modified street_name to street_name_part ([#3485](https://github.com/faker-js/faker/issues/3485)) ([c15da8e](https://github.com/faker-js/faker/commit/c15da8efec3fc05d089bdf2dc1513183b614e04b))

## [9.7.0](https://github.com/faker-js/faker/compare/v9.6.0...v9.7.0) (2025-04-13)


### New Locales

* **locale:** Add bn_BD locale ([#3439](https://github.com/faker-js/faker/issues/3439)) ([fef0ad7](https://github.com/faker-js/faker/commit/fef0ad7859ff5994947c2c02afd6bdf13dffd234))
* **locale:** add cy locale, start with date ([#3462](https://github.com/faker-js/faker/issues/3462)) ([f70a6f7](https://github.com/faker-js/faker/commit/f70a6f7a656090ed900ea18b39961d0d5ff78be0))
* **locale:** add finance support for ja locale ([#3449](https://github.com/faker-js/faker/issues/3449)) ([b2c5298](https://github.com/faker-js/faker/commit/b2c5298c94f632681937fc6d62da2f84252b5c66))
* **locale:** add localize sex support for zh_CN & zh_TW ([#3450](https://github.com/faker-js/faker/issues/3450)) ([048c325](https://github.com/faker-js/faker/commit/048c32581bd5d15f333ef63e4ca968e02793373d))
* **locale:** add Tamil language support ([#3468](https://github.com/faker-js/faker/issues/3468)) ([cdf6dc4](https://github.com/faker-js/faker/commit/cdf6dc4a973b8e64069f8b8d98e6921188f08575))


### Bug Fixes

* **airline:** Air France and KLM Royal Dutch Airlines ([#3440](https://github.com/faker-js/faker/issues/3440)) ([8a2d168](https://github.com/faker-js/faker/commit/8a2d168f62307af8c4b04ab8ab4624786009671b))
* **iban:** more strict pattern for IE and PS ([#3464](https://github.com/faker-js/faker/issues/3464)) ([7b12056](https://github.com/faker-js/faker/commit/7b120567139b2ac84d052f42f1f352c6697f2aa2))
* **locale:** rename ja and zh_CN company affix files ([#3448](https://github.com/faker-js/faker/issues/3448)) ([1e551c5](https://github.com/faker-js/faker/commit/1e551c5f47e292d3dc6bbefae9eaf75664a052a1))
* **number:** don't ignore multipleOf in float when min=max ([#3417](https://github.com/faker-js/faker/issues/3417)) ([e4cc4e5](https://github.com/faker-js/faker/commit/e4cc4e50d1d4103c26f06fd2db0ca187dbb537cd))

## [9.6.0](https://github.com/faker-js/faker/compare/v9.5.1...v9.6.0) (2025-03-06)


### Features

* **finance:** add ISO 4217 numerical codes to Currency ([#3404](https://github.com/faker-js/faker/issues/3404)) ([ae9aec6](https://github.com/faker-js/faker/commit/ae9aec67b19f779e818be5550a8edc44f20d91d4))
* **number:** bigint multipleOf ([#3402](https://github.com/faker-js/faker/issues/3402)) ([7b4f85a](https://github.com/faker-js/faker/commit/7b4f85a2c00bf2adff2b88957bd6322100e6e541))

## [9.5.1](https://github.com/faker-js/faker/compare/v9.5.0...v9.5.1) (2025-02-28)


### Bug Fixes

* test before using Buffers ([#3400](https://github.com/faker-js/faker/issues/3400)) ([ec7c9a8](https://github.com/faker-js/faker/commit/ec7c9a8e607d63a008d06747f89c9512f5b3171e))

## [9.5.0](https://github.com/faker-js/faker/compare/v9.4.0...v9.5.0) (2025-02-10)


### Features

* **image:** add AI-generated avatars ([#3126](https://github.com/faker-js/faker/issues/3126)) ([9e13953](https://github.com/faker-js/faker/commit/9e1395380cf9baf9f0350c43cbbc303430e77dfb))

## [9.4.0](https://github.com/faker-js/faker/compare/v9.3.0...v9.4.0) (2025-01-15)


### Features

* **finance:** use fake patterns for transactionDescription ([#3202](https://github.com/faker-js/faker/issues/3202)) ([5ec4a6c](https://github.com/faker-js/faker/commit/5ec4a6c9ddc037ae189ee93f339fa52065ac2a26))
* **internet:** update to simplified modern user-agent list ([#3324](https://github.com/faker-js/faker/issues/3324)) ([3c7abb5](https://github.com/faker-js/faker/commit/3c7abb55e68fcbcf41560539a15845e7c8882765))
* **location:** add list of spoken languages ([#3333](https://github.com/faker-js/faker/issues/3333)) ([ff6dda9](https://github.com/faker-js/faker/commit/ff6dda94ddd312ebcff816cbb63e74df9857d091))


### Changed Locales

* **locale:** fix various locale data with trailing spaces ([#3329](https://github.com/faker-js/faker/issues/3329)) ([e5eec0e](https://github.com/faker-js/faker/commit/e5eec0ed848d298ccba1d6db9392a507c5ce2bc4))
* **locale:** improve product_name data in en and tr ([#3372](https://github.com/faker-js/faker/issues/3372)) ([773fc1f](https://github.com/faker-js/faker/commit/773fc1f654a2ab57d048443e54761f0f288e7427))


### Bug Fixes

* **animal:** re-moo-ved some incorrect cow data ([#3326](https://github.com/faker-js/faker/issues/3326)) ([47f835b](https://github.com/faker-js/faker/commit/47f835bd0dc257e5d7b9d7e7e06c08854474a3a7))
* basic wildcard range handling + add more tests ([#3322](https://github.com/faker-js/faker/issues/3322)) ([817f8a0](https://github.com/faker-js/faker/commit/817f8a01d93378e00c03cf73154fcec34fd5feef))
* **finance:** update Discover card number format to ensure accuracy ([#3336](https://github.com/faker-js/faker/issues/3336)) ([69c0063](https://github.com/faker-js/faker/commit/69c006344b8c54a9cd5f0adc36d49c88a223de4e))
* **image:** dataUri should return random type ([#3347](https://github.com/faker-js/faker/issues/3347)) ([eceb17d](https://github.com/faker-js/faker/commit/eceb17d25741c5fb1bd56b8151067eed3708a076))
* **locales:** update chemical element names in zh_CN ([#3371](https://github.com/faker-js/faker/issues/3371)) ([6ec6f84](https://github.com/faker-js/faker/commit/6ec6f84922a16a8489215ba152e683227f5c8262))
* **location:** fix bad uz street_name_part data ([#3328](https://github.com/faker-js/faker/issues/3328)) ([b6132cb](https://github.com/faker-js/faker/commit/b6132cbee67ae7e53f57ffb344688d3980f91d3d))
* **music:** fix truncated song names with commas ([#3327](https://github.com/faker-js/faker/issues/3327)) ([f36fc71](https://github.com/faker-js/faker/commit/f36fc71ac4cce6d5a6c9e6b16d7a22a98a01db74)), closes [#996](https://github.com/faker-js/faker/issues/996)
* **system:** semver parts should not be limited to 0-9 ([#3349](https://github.com/faker-js/faker/issues/3349)) ([c0d92b8](https://github.com/faker-js/faker/commit/c0d92b8fa856d0c841f5aee57c6e08627025447a))

## [9.3.0](https://github.com/faker-js/faker/compare/v9.2.0...v9.3.0) (2024-12-02)


### Features

* add initial seed parameter to constructors ([#3220](https://github.com/faker-js/faker/issues/3220)) ([1633c8d](https://github.com/faker-js/faker/commit/1633c8deb8f6f41151b39b62689ae3e02ab295bc))


### Changed Locales

* **locale:** improve zh_CN vehicle manufacturers ([#3254](https://github.com/faker-js/faker/issues/3254)) ([9abaed1](https://github.com/faker-js/faker/commit/9abaed1061b5fa9dc9f93822162610105a788296))
* **locale:** lowercase Mexican color names ([#3200](https://github.com/faker-js/faker/issues/3200)) ([0d85075](https://github.com/faker-js/faker/commit/0d850758d0ea0db45a9a4c8abda5c1e09796fb44))
* **locale:** sort person data ([#3269](https://github.com/faker-js/faker/issues/3269)) ([01e20e9](https://github.com/faker-js/faker/commit/01e20e9695575211ee63c7a937f4ae93a934ef32))
* **locale:** split en_AU_ocker first_names by sex ([#3270](https://github.com/faker-js/faker/issues/3270)) ([b0a5ad3](https://github.com/faker-js/faker/commit/b0a5ad38bb6e516ba4a6776342c0bb389ca17363))
* **locale:** split up Spanish generic first names ([#3279](https://github.com/faker-js/faker/issues/3279)) ([5d5fe30](https://github.com/faker-js/faker/commit/5d5fe30ab4f4df092a1ebac991cdf4a345f604fc))
* **locale:** update Polish city name ([#3306](https://github.com/faker-js/faker/issues/3306)) ([53441b7](https://github.com/faker-js/faker/commit/53441b77731f9d15bb7823de8050a18ecbbbe22a))


### Bug Fixes

* **internet:** ensure domainWord always returns a valid value in all locales ([#3253](https://github.com/faker-js/faker/issues/3253)) ([525fedc](https://github.com/faker-js/faker/commit/525fedc91bd02f53330cfb40fe228b148dcf562b))
* **locale:** add Isadora to female names in pt_BR for consistency ([#3282](https://github.com/faker-js/faker/issues/3282)) ([b390432](https://github.com/faker-js/faker/commit/b390432626811d2af32086b16151bc4c55634c60))
* **locale:** fix incorrect accents in it first_name ([#3281](https://github.com/faker-js/faker/issues/3281)) ([e0fb23e](https://github.com/faker-js/faker/commit/e0fb23ef81009f565676485d63b6fe97afd8a31e))

## [9.2.0](https://github.com/faker-js/faker/compare/v9.1.0...v9.2.0) (2024-11-03)


### Features

* **animal:** add petName method ([#3196](https://github.com/faker-js/faker/issues/3196)) ([c02beea](https://github.com/faker-js/faker/commit/c02beeadd49e48656a0307451517e9751e3118c3))
* **number:** add romanNumeral method ([#3070](https://github.com/faker-js/faker/issues/3070)) ([72937de](https://github.com/faker-js/faker/commit/72937de55c892c011846bc2b67dc0df61fbdf5a2))


### Changed Locales

* **locale:** improve Spanish color names ([#3230](https://github.com/faker-js/faker/issues/3230)) ([99d81be](https://github.com/faker-js/faker/commit/99d81bed28b36ab525340e32a7349799cbe88eca))

## [9.1.0](https://github.com/faker-js/faker/compare/v9.0.3...v9.1.0) (2024-10-26)


### New Locales

* **locale:** add books on pt_br ([#3218](https://github.com/faker-js/faker/issues/3218)) ([af1dbcd](https://github.com/faker-js/faker/commit/af1dbcdbf363a12c448834dff031a698c8925e2e))
* **locale:** add Swedish(sv) counties ([#3154](https://github.com/faker-js/faker/issues/3154)) ([67569d9](https://github.com/faker-js/faker/commit/67569d93992e6388647118e9e9e319cfdd6721ed))


### Features

* add book module ([#2949](https://github.com/faker-js/faker/issues/2949)) ([2f93d9d](https://github.com/faker-js/faker/commit/2f93d9da383638b6d232ff8b3cae827ea4c80150))
* **commerce:** more varied product descriptions ([#3174](https://github.com/faker-js/faker/issues/3174)) ([ba4ef9a](https://github.com/faker-js/faker/commit/ba4ef9a5a671ffc6ed10db7fed896dccda90dca1))
* **internet:** add jwt method ([#2936](https://github.com/faker-js/faker/issues/2936)) ([e3858f2](https://github.com/faker-js/faker/commit/e3858f221bb3b6e1278933c0b8e0d98da6854e52))
* **internet:** improve ipv4 method ([#2992](https://github.com/faker-js/faker/issues/2992)) ([a5a6c5b](https://github.com/faker-js/faker/commit/a5a6c5b4e1145b1e21e5d8e540bc6f55f97d3a84))
* **location:** add `continent` method ([#3162](https://github.com/faker-js/faker/issues/3162)) ([4056ab0](https://github.com/faker-js/faker/commit/4056ab09c64be40d41562284ec64e7531fbaff41))
* **string:** adds support for generating ULID ([#2524](https://github.com/faker-js/faker/issues/2524)) ([5b1c858](https://github.com/faker-js/faker/commit/5b1c8588f8a57be712e64434f7b17a8407a4f465))


### Changed Locales

* **locale:** enhance en vehicle manufacturers ([#3187](https://github.com/faker-js/faker/issues/3187)) ([858f8d0](https://github.com/faker-js/faker/commit/858f8d07b098418a7a2b7cb2e265eb9f4388ed3d))
* **locale:** modernise buzz- and catch-phrases ([#2930](https://github.com/faker-js/faker/issues/2930)) ([247c86f](https://github.com/faker-js/faker/commit/247c86f5b40916957123cb3fa1bd4356d8fa7452))
* **locale:** remove inaccessible files ([#3205](https://github.com/faker-js/faker/issues/3205)) ([89b695c](https://github.com/faker-js/faker/commit/89b695cbaf20206ebb4570d018af2fa99f720889))
* **locale:** trim excessive Croatian last names ([#3204](https://github.com/faker-js/faker/issues/3204)) ([18e59aa](https://github.com/faker-js/faker/commit/18e59aa8805b3413b6f8058b1a00ff5bcbfbc935))


### Bug Fixes

* **food:** use arrayElement instead of fake for adjective ([#3178](https://github.com/faker-js/faker/issues/3178)) ([a8dfa2f](https://github.com/faker-js/faker/commit/a8dfa2fcde59a1ec9e5beb2e93c690a8ec532cdf))
* **location:** fix US ZIP code anomalies for zipCode({state}) ([#3180](https://github.com/faker-js/faker/issues/3180)) ([df59724](https://github.com/faker-js/faker/commit/df597241003824279268c1dc9eae6c772c54760b))
* **location:** update valid ZIP ranges for FL and VA ([#3167](https://github.com/faker-js/faker/issues/3167)) ([e271d4a](https://github.com/faker-js/faker/commit/e271d4a545dd48e57285019e4f412358c49cad0d))

## [9.0.3](https://github.com/faker-js/faker/compare/v9.0.2...v9.0.3) (2024-09-26)


### Changed Locales

* **locale:** update french legal entity types ([#3142](https://github.com/faker-js/faker/issues/3142)) ([d6bceb6](https://github.com/faker-js/faker/commit/d6bceb662de6559446fedb11de53a632a525aba0))


### Bug Fixes

* **image:** fix dataUri with type svg-base64 in browsers ([#3144](https://github.com/faker-js/faker/issues/3144)) ([78b2a3a](https://github.com/faker-js/faker/commit/78b2a3a8b85679eeff27beccebcc404b0a5ff3cf))

## [9.0.2](https://github.com/faker-js/faker/compare/v9.0.1...v9.0.2) (2024-09-23)


### Bug Fixes

* **locale:** improve pt_PT location and person data ([#3020](https://github.com/faker-js/faker/issues/3020)) ([3e47ee7](https://github.com/faker-js/faker/commit/3e47ee7da671296e11ed2b219f85b06c9d05d677))

## [9.0.1](https://github.com/faker-js/faker/compare/v9.0.0...v9.0.1) (2024-09-14)


### Bug Fixes

* emit cts types ([#3093](https://github.com/faker-js/faker/issues/3093)) ([53ef42c](https://github.com/faker-js/faker/commit/53ef42cc192996a0e09bba9d5f5f8a70a7932275))

## [9.0.0](https://github.com/faker-js/faker/compare/v9.0.0-rc.1...v9.0.0) (2024-09-02)


No noteworthy changes to [9.0.0-rc.1](https://github.com/faker-js/faker/blob/next/CHANGELOG.md#900-rc1-2024-08-19)

For those upgrading from earlier versions, please refer to our [Migration Guide](https://v9.fakerjs.dev/guide/upgrading.html) for detailed instructions.

## [9.0.0-rc.1](https://github.com/faker-js/faker/compare/v9.0.0-rc.0...v9.0.0-rc.1) (2024-08-19)


### New Locales

* **locale:** implement company info on pt_PT locale ([#3022](https://github.com/faker-js/faker/issues/3022)) ([0fd0402](https://github.com/faker-js/faker/commit/0fd0402aa96818c8c3af1defc816ffcffadbb1fe))
* **locale:** implement more colors to pt_PT locale ([#3021](https://github.com/faker-js/faker/issues/3021)) ([a210619](https://github.com/faker-js/faker/commit/a210619c3cd6d802f8a80e3db68c96b444d15a64))
* **locale:** implement state info on sk locale ([#3054](https://github.com/faker-js/faker/issues/3054)) ([8c55493](https://github.com/faker-js/faker/commit/8c55493e73b1d09af781b1e14c21858e775b5d91))


### Features

* **location:** en_CA postcodes by state ([#3057](https://github.com/faker-js/faker/issues/3057)) ([b62c1b7](https://github.com/faker-js/faker/commit/b62c1b7a97fdb1ab5928d34b11bcad9e1158a60b))

## [9.0.0-rc.0](https://github.com/faker-js/faker/compare/v9.0.0-alpha.1...v9.0.0-rc.0) (2024-07-11)


### Changed Locales

* **locale:** group person entries by gender ([#2938](https://github.com/faker-js/faker/issues/2938)) ([e21fcaf](https://github.com/faker-js/faker/commit/e21fcaf2398dfd9aed54136524fabd39866dfca4))
* **locale:** rename company affix files ([#2975](https://github.com/faker-js/faker/issues/2975)) ([49d7119](https://github.com/faker-js/faker/commit/49d7119025d252eecb043647b7d4465352938157))

## [9.0.0-alpha.1](https://github.com/faker-js/faker/compare/v9.0.0-alpha.0...v9.0.0-alpha.1) (2024-06-22)


### ⚠ BREAKING CHANGES

* **date:** separate timeZone method (#2947)
* **locale:** use singular locale definition keys (#2932)
* **locale:** use snake case for all locale data (#2910)
* **locale:** remove location data index-value-binding (#2476)

### New Locales

* **locale:** add lorem for uz_UZ_latin  ([#2824](https://github.com/faker-js/faker/issues/2824)) ([6e6fa4c](https://github.com/faker-js/faker/commit/6e6fa4cd090e5d7dc60d2c0eed3a01013ab4a111))
* **locale:** extend Hebrew (he) for company ([#2921](https://github.com/faker-js/faker/issues/2921)) ([29df384](https://github.com/faker-js/faker/commit/29df38468fc90efaaf749a5c39a1b2c1f2e6bf46))


### Features

* **bitcoinAddress:** multiple bitcoin address types and testnet ([#2922](https://github.com/faker-js/faker/issues/2922)) ([3ae9393](https://github.com/faker-js/faker/commit/****************************************))
* **date:** separate timeZone method ([#2947](https://github.com/faker-js/faker/issues/2947)) ([d6924f7](https://github.com/faker-js/faker/commit/d6924f7fbb9403d106022f58a1bda3b159e88ae6))
* **music:** add album and artist methods ([#2620](https://github.com/faker-js/faker/issues/2620)) ([f682750](https://github.com/faker-js/faker/commit/f6827505712f48e448d4d4ef2dc3198e6967bbad))


### Changed Locales

* **locale:** activate data normalization for airline ([#2828](https://github.com/faker-js/faker/issues/2828)) ([d71662e](https://github.com/faker-js/faker/commit/d71662e153f6915e95af750419e0042e5c2c49d4))
* **locale:** activate data normalization for color ([#2837](https://github.com/faker-js/faker/issues/2837)) ([b754dc6](https://github.com/faker-js/faker/commit/b754dc6ac8be86a2b5c550d743899390997362a6))
* **locale:** normalize commerce data ([#2868](https://github.com/faker-js/faker/issues/2868)) ([425997f](https://github.com/faker-js/faker/commit/425997fdb2c9f7d746c688b0a0c5ba1f884550d0))
* **locale:** normalize company data ([#2889](https://github.com/faker-js/faker/issues/2889)) ([5ba44bc](https://github.com/faker-js/faker/commit/5ba44bc4a62b708b86cd50cda74977bc3b697240))
* **locale:** normalize database data ([#2873](https://github.com/faker-js/faker/issues/2873)) ([1eea7fe](https://github.com/faker-js/faker/commit/1eea7fe7caa3a41d5651ead3c4604a0fc99f0aba))
* **locale:** normalize date data ([#2902](https://github.com/faker-js/faker/issues/2902)) ([9226945](https://github.com/faker-js/faker/commit/922694553874beb312821c62c1c1fd3afc63a315))
* **locale:** normalize finance data ([#2915](https://github.com/faker-js/faker/issues/2915)) ([353e85c](https://github.com/faker-js/faker/commit/353e85cda53919c97fe9d190633b4c9c1401f213))
* **locale:** normalize hacker data ([#2866](https://github.com/faker-js/faker/issues/2866)) ([2d5fbe5](https://github.com/faker-js/faker/commit/2d5fbe5c559bbb086fb35f87209c3cebf9be89a2))
* **locale:** normalize music data ([#2857](https://github.com/faker-js/faker/issues/2857)) ([425e10f](https://github.com/faker-js/faker/commit/425e10fe1cfeff74b04a7ed931241625e951f33c))
* **locale:** normalize science data ([#2886](https://github.com/faker-js/faker/issues/2886)) ([bbdd998](https://github.com/faker-js/faker/commit/bbdd998787e40a96630d436f82aa86c67f76fb64))
* **locale:** normalize vehicle data ([#2874](https://github.com/faker-js/faker/issues/2874)) ([737de3a](https://github.com/faker-js/faker/commit/737de3ab7cefe6d66ac1465807a2fa9a5017f930))
* **locale:** remove discontinued credit card issuer "maestro" ([#2803](https://github.com/faker-js/faker/issues/2803)) ([cd5577c](https://github.com/faker-js/faker/commit/cd5577c546f17f6468b35c0c6164a666584ba67c))
* **locale:** remove location data index-value-binding ([#2476](https://github.com/faker-js/faker/issues/2476)) ([453ea97](https://github.com/faker-js/faker/commit/453ea970a4c7ed19e9d41fd026923f42dbd4e768))
* **locale:** unfreeze arrays ([#2928](https://github.com/faker-js/faker/issues/2928)) ([badaa6d](https://github.com/faker-js/faker/commit/badaa6d60593fe41a1afa0e9a8bef6ae5bb8a352))
* **locale:** use singular locale definition keys ([#2932](https://github.com/faker-js/faker/issues/2932)) ([a5ffca1](https://github.com/faker-js/faker/commit/a5ffca1c7863531d459f67cbab7bd9ba34d16904))
* **locale:** use snake case for all locale data ([#2910](https://github.com/faker-js/faker/issues/2910)) ([558b959](https://github.com/faker-js/faker/commit/558b959e0e2f791dab0ba2f500493ba7ddbd25e2))


### Bug Fixes

* **date:** fix birthdate ([#2829](https://github.com/faker-js/faker/issues/2829)) ([462c80e](https://github.com/faker-js/faker/commit/462c80e09703010709a52531b85d0d9c0bbd3448))
* **locale:** clean up Hungarian words ([#2816](https://github.com/faker-js/faker/issues/2816)) ([16ba43a](https://github.com/faker-js/faker/commit/16ba43a6a4d1c93ac588c6b4c20b8c2a40213bdb))
* **locale:** fix typo in Persian date data ([#2888](https://github.com/faker-js/faker/issues/2888)) ([042c871](https://github.com/faker-js/faker/commit/042c87162d85570ac722590c1f0c65f75421dbaf))
* **locale:** misc spelling errors in commerce & food ([#2809](https://github.com/faker-js/faker/issues/2809)) ([734a7f3](https://github.com/faker-js/faker/commit/734a7f3d55d1233d8182585bb79d2013f0ce289e))
* **locale:** remove Pizza from Greek words ([#2908](https://github.com/faker-js/faker/issues/2908)) ([80b4a23](https://github.com/faker-js/faker/commit/80b4a23b904db1abe1408480bbf56704d048b165))
* **person:** workaround for inherited first- and lastNames ([#2834](https://github.com/faker-js/faker/issues/2834)) ([016a5b6](https://github.com/faker-js/faker/commit/016a5b62f678013a4f9a50bda10dcf20405a6e55))
* **vehicle:** Make VIN number generation more accurate ([#2765](https://github.com/faker-js/faker/issues/2765)) ([1f7568a](https://github.com/faker-js/faker/commit/1f7568ab1a7010c7f9c713f4ac4c2d5e0cfb350b))

## [9.0.0-alpha.0](https://github.com/faker-js/faker/compare/v8.4.1...v9.0.0-alpha.0) (2024-04-12)


### ⚠ BREAKING CHANGES

* **locale:** remove unused global locale faker instance (#2789)
* **locale:** remove deprecated locale aliases (#2787)
* **date:** birthdate improvements (#2756)
* **date:** stricter error handling of between (#2719)
* **date:** fail on invalid dates (#2757)
* **location:** remove v8 deprecated location methods (#2753)
* remove v8 deprecated type aliases (#2754)
* **commerce:** remove v8 deprecated commerce method (#2752)
* **location:** remove default_country definitions (#2740)
* **helpers:** remove v8 deprecated helpers methods (#2729)
* **number:** remove v8 deprecated number parameter (#2738)
* **company:** remove v8 deprecated company methods (#2726)
* **phone:** add new style parameter (#2578)
* **finance:** remove v8 deprecated finance methods (#2727)
* **git:** remove v8 deprecated git method (#2716)
* **phone:** remove v8 deprecated phone format (#2712)
* **date:** remove v8 deprecated date methods (#2704)
* **helpers:** stricter checking for function signature passed to `multiple` (#2563)
* **internet:** remove v8 deprecated internet methods (#2699)
* **random:** remove v8 deprecated image methods (#2697)
* high precision random number generator (#2357)
* **person:** flatten jobs definitions (#2505)
* **datatype:** remove v8 deprecated datatype methods (#2694)
* **helpers:** use const generics where possible (#2685)
* **random:** remove deprecated random module (#2678)
* remove v8 deprecated faker class parts (#2682)
* **helpers:** remove v8 deprecated unique (#2661)
* switch to tsup (#2391)
* **image:** remove v8 deprecated providers (#2634)
* **image:** randomize defaults (#2472)
* **deps:** update dependency typescript to v5 (#1953)
* drop NodeJS v14 and v16 support (#2121)

### Features

* **animal:** add more simple animals to animal.type() ([#2786](https://github.com/faker-js/faker/issues/2786)) ([f47dfe5](https://github.com/faker-js/faker/commit/f47dfe5c58371b468c704cab7a7cd29f9a3e3379))
* **food:** new module ([#2484](https://github.com/faker-js/faker/issues/2484)) ([9aab7c2](https://github.com/faker-js/faker/commit/9aab7c2429b50c0f6e8bd1908344f9ff92912f5d))
* **helpers:** stricter checking for function signature passed to `multiple` ([#2563](https://github.com/faker-js/faker/issues/2563)) ([2b15f2e](https://github.com/faker-js/faker/commit/2b15f2ee7eeba7147c75a24d71042ee996966c92))
* **helpers:** use const generics where possible ([#2685](https://github.com/faker-js/faker/issues/2685)) ([c45537f](https://github.com/faker-js/faker/commit/c45537f6d4f3a28d8be1ebbe03567004c04af145))
* high precision random number generator ([#2357](https://github.com/faker-js/faker/issues/2357)) ([4ab0731](https://github.com/faker-js/faker/commit/4ab073164beab2bf690d7fffc7773e7b76d8e07e))
* **music:** add additional genres ([#2621](https://github.com/faker-js/faker/issues/2621)) ([7dae52b](https://github.com/faker-js/faker/commit/7dae52bfcd93c41ec9d2c4dd4d96a07f31c3dfc1))
* **number:** add multipleOf to faker.number.int ([#2586](https://github.com/faker-js/faker/issues/2586)) ([5ef8ef1](https://github.com/faker-js/faker/commit/5ef8ef1da13e83efc61702e64d8f75b611afb4e5))
* **phone:** add new style parameter ([#2578](https://github.com/faker-js/faker/issues/2578)) ([e130549](https://github.com/faker-js/faker/commit/e130549e82a3d59af46f2d595ed47fa9a39724a3))


### Bug Fixes

* **commerce:** return fractional prices ([#2458](https://github.com/faker-js/faker/issues/2458)) ([9348138](https://github.com/faker-js/faker/commit/9348138893bb95faa5037c653443fbd525ce2939))
* **internet:** add Hebrew username charMapping ([#2781](https://github.com/faker-js/faker/issues/2781)) ([5860783](https://github.com/faker-js/faker/commit/5860783e81d392bc235408b697668ab8ecdd2d7c))
* **person:** add dedicated first names in ko locale ([#2773](https://github.com/faker-js/faker/issues/2773)) ([47f008a](https://github.com/faker-js/faker/commit/47f008aff5aee08057ad5445d5b3dfbd1b196934))
* **person:** delete set of invalid names in en_NG ([#2764](https://github.com/faker-js/faker/issues/2764)) ([2884552](https://github.com/faker-js/faker/commit/2884552c0506f6244eeda56ee6a76067424959ca))
* restore support for moduleResolution Node* ([#2749](https://github.com/faker-js/faker/issues/2749)) ([2f07483](https://github.com/faker-js/faker/commit/2f074835e60240d8ac53e2d42fc7b5dac85d05b8))
* tree-shaking ([#2790](https://github.com/faker-js/faker/issues/2790)) ([fb65976](https://github.com/faker-js/faker/commit/fb65976acb1003f441a35afafbd87204eca0e2d7))


### infra

* drop NodeJS v14 and v16 support ([#2121](https://github.com/faker-js/faker/issues/2121)) ([1e0df8b](https://github.com/faker-js/faker/commit/1e0df8b81445d89b4a65bcab3a156b51ee1d1a38))


* **commerce:** remove v8 deprecated commerce method ([#2752](https://github.com/faker-js/faker/issues/2752)) ([8c80877](https://github.com/faker-js/faker/commit/8c80877f1691886e32c2b1eb799586c0c1b02b8b))
* **company:** remove v8 deprecated company methods ([#2726](https://github.com/faker-js/faker/issues/2726)) ([19bcf88](https://github.com/faker-js/faker/commit/****************************************))
* **datatype:** remove v8 deprecated datatype methods ([#2694](https://github.com/faker-js/faker/issues/2694)) ([682a427](https://github.com/faker-js/faker/commit/682a4276f13d7b8f48e1bd8aafcf011c7bd10390))
* **date:** birthdate improvements ([#2756](https://github.com/faker-js/faker/issues/2756)) ([b498d1f](https://github.com/faker-js/faker/commit/b498d1f794e6d682e9d9fedebff194664c2a3f1d))
* **date:** fail on invalid dates ([#2757](https://github.com/faker-js/faker/issues/2757)) ([3485e7e](https://github.com/faker-js/faker/commit/3485e7eece540b63059e97bd8166d1ffd88334de))
* **date:** remove v8 deprecated date methods ([#2704](https://github.com/faker-js/faker/issues/2704)) ([e868060](https://github.com/faker-js/faker/commit/e868060c87cbca3c1bf90465d61fc39a4eea1f9c))
* **date:** stricter error handling of between ([#2719](https://github.com/faker-js/faker/issues/2719)) ([da35c51](https://github.com/faker-js/faker/commit/da35c51d16eccd99a7001a5b055a24806168435d))
* **deps:** update dependency typescript to v5 ([#1953](https://github.com/faker-js/faker/issues/1953)) ([bc3ebb7](https://github.com/faker-js/faker/commit/bc3ebb793666c0d7e58cf428ba12465a7b290cbe))
* **finance:** remove v8 deprecated finance methods ([#2727](https://github.com/faker-js/faker/issues/2727)) ([a536a9d](https://github.com/faker-js/faker/commit/a536a9d79f410986e6bd6dc1bc4a72252a0cc8d9))
* **git:** remove v8 deprecated git method ([#2716](https://github.com/faker-js/faker/issues/2716)) ([b3afc8f](https://github.com/faker-js/faker/commit/b3afc8f351003cf9df9984fb5be6cd46e18f71d8))
* **helpers:** remove v8 deprecated helpers methods ([#2729](https://github.com/faker-js/faker/issues/2729)) ([1169a05](https://github.com/faker-js/faker/commit/1169a0576ba1469d7c05be0b8fd844bde8a1af13))
* **helpers:** remove v8 deprecated unique ([#2661](https://github.com/faker-js/faker/issues/2661)) ([4382fd9](https://github.com/faker-js/faker/commit/4382fd9313821bf542829859e97d4422acc9e284))
* **image:** randomize defaults ([#2472](https://github.com/faker-js/faker/issues/2472)) ([92207b7](https://github.com/faker-js/faker/commit/92207b7df15732a61e613a4376a9ce88bd4f7b0f))
* **image:** remove v8 deprecated providers ([#2634](https://github.com/faker-js/faker/issues/2634)) ([44d698e](https://github.com/faker-js/faker/commit/44d698e14f48a32259fd52a39d1e3028f84f3c06))
* **internet:** remove v8 deprecated internet methods ([#2699](https://github.com/faker-js/faker/issues/2699)) ([3024d9e](https://github.com/faker-js/faker/commit/3024d9e7adeadb1c317aee308c594d3e18090b1a))
* **location:** remove default_country definitions ([#2740](https://github.com/faker-js/faker/issues/2740)) ([a409b46](https://github.com/faker-js/faker/commit/a409b468f942d964ce22ff42b52f8a756e0ca815))
* **location:** remove v8 deprecated location methods ([#2753](https://github.com/faker-js/faker/issues/2753)) ([6dee178](https://github.com/faker-js/faker/commit/6dee178558b87b73bba1395c11d2ffe3d156dad1))
* **number:** remove v8 deprecated number parameter ([#2738](https://github.com/faker-js/faker/issues/2738)) ([a672d27](https://github.com/faker-js/faker/commit/a672d277b883a445a852f9b6db18818104e8de7e))
* **person:** flatten jobs definitions ([#2505](https://github.com/faker-js/faker/issues/2505)) ([0d4cba6](https://github.com/faker-js/faker/commit/0d4cba637cdd38e7757ecca1b7eae75d7a737fa8))
* **phone:** remove v8 deprecated phone format ([#2712](https://github.com/faker-js/faker/issues/2712)) ([660da4c](https://github.com/faker-js/faker/commit/660da4c401b36ebfe0c0f7975c265146d9224921))
* **random:** remove deprecated random module ([#2678](https://github.com/faker-js/faker/issues/2678)) ([64ff107](https://github.com/faker-js/faker/commit/64ff107b8a9cd0965a67f00fd30cded144c02fd6))
* **random:** remove v8 deprecated image methods ([#2697](https://github.com/faker-js/faker/issues/2697)) ([e99fba9](https://github.com/faker-js/faker/commit/e99fba9729729e8a3107a659f5e00b9f18047298))
* remove v8 deprecated faker class parts ([#2682](https://github.com/faker-js/faker/issues/2682)) ([a9dc701](https://github.com/faker-js/faker/commit/a9dc7017b4a2b2bf79c42fe947de6029fae5e937))
* remove v8 deprecated type aliases ([#2754](https://github.com/faker-js/faker/issues/2754)) ([2ec602b](https://github.com/faker-js/faker/commit/2ec602ba1be4e04f261a96717a3d3c90a15a4c01))
* switch to tsup ([#2391](https://github.com/faker-js/faker/issues/2391)) ([db88a15](https://github.com/faker-js/faker/commit/db88a1518e8d0ccfe191bd48a1ae8641a9a25303))


### New Locales

* **locale:** add location for uz_UZ_latin ([#2775](https://github.com/faker-js/faker/issues/2775)) ([80730e9](https://github.com/faker-js/faker/commit/80730e92abe87545e685674cafdcb1bc90f15fb1))
* **locale:** add uzbek locale (uz_UZ_latin) ([#2686](https://github.com/faker-js/faker/issues/2686)) ([f7471a2](https://github.com/faker-js/faker/commit/f7471a225f388c6bc0ec47ebad759ee1051e1106))
* **locale:** extend Uzbek locale for commerce ([#2750](https://github.com/faker-js/faker/issues/2750)) ([b6b18d7](https://github.com/faker-js/faker/commit/b6b18d7f97e232bd133f117172e02df35fa16fec))
* **locale:** extend Uzbek locale for date and color ([#2724](https://github.com/faker-js/faker/issues/2724)) ([776a625](https://github.com/faker-js/faker/commit/776a6258de07108548fb5918bdb8b572a278075a))
* **locale:** support street and street address for ja ([#2193](https://github.com/faker-js/faker/issues/2193)) ([501f4b3](https://github.com/faker-js/faker/commit/501f4b3d3a8056b8b7877f1d78575ade26b361ce))


### Changed Locales

* **locale:** normalize animal data ([#2791](https://github.com/faker-js/faker/issues/2791)) ([03b965a](https://github.com/faker-js/faker/commit/03b965abfde57be617a1ae7ba715604e149a803d))
* **locale:** remove deprecated locale aliases ([#2787](https://github.com/faker-js/faker/issues/2787)) ([03ac471](https://github.com/faker-js/faker/commit/03ac47198313126c3cc8cb3fb9ef184360bb79eb))
* **locale:** remove unused global locale faker instance ([#2789](https://github.com/faker-js/faker/issues/2789)) ([b505a70](https://github.com/faker-js/faker/commit/b505a70c8e52a3f64c6b7cb4e2888cf5d48aeefe))

### [8.4.1](https://github.com/faker-js/faker/compare/v8.4.0...v8.4.1) (2024-02-06)

* JSDocs improvements

## [8.4.0](https://github.com/faker-js/faker/compare/v8.3.1...v8.4.0) (2024-01-24)


### Features

* **helpers:** add support for complex intermediate types ([#2550](https://github.com/faker-js/faker/issues/2550)) ([24482a3](https://github.com/faker-js/faker/commit/24482a30042eec5b553b30d60985e89fd69a8660))
* **number:** add parameter `fractionDigits` in float ([#1855](https://github.com/faker-js/faker/issues/1855)) ([41d8778](https://github.com/faker-js/faker/commit/41d87789c7ff353acfd0f5ca88a99c0d1fd3b500))
* **person:** add job titles for fr ([#2531](https://github.com/faker-js/faker/issues/2531)) ([ba28ab6](https://github.com/faker-js/faker/commit/ba28ab6e59009cceb5b691ddd212e09df22b586e))


### Bug Fixes

* **finance:** correct VG IBAN format ([#2552](https://github.com/faker-js/faker/issues/2552)) ([b8049d1](https://github.com/faker-js/faker/commit/b8049d171086bce7b46eb617c88f44cfdd019e00))
* **internet:** username method to return value that always includes… ([#2506](https://github.com/faker-js/faker/issues/2506)) ([0ee1c67](https://github.com/faker-js/faker/commit/0ee1c6751ec97e10ca1436d2b100b4acb1ac90ec))
* **locale:** incomplete airline names in zh_CN ([#2558](https://github.com/faker-js/faker/issues/2558)) ([5525b55](https://github.com/faker-js/faker/commit/5525b55cc47fa97b55de52fccff7ab51633e639a))
* **number:** improve float generation for precisions of form 10^-n ([#2581](https://github.com/faker-js/faker/issues/2581)) ([39c715d](https://github.com/faker-js/faker/commit/39c715d916d69e83795932260f1681df3241db91))

### [8.3.1](https://github.com/faker-js/faker/compare/v8.3.0...v8.3.1) (2023-11-14)


### Bug Fixes

* remove `[@internal](https://github.com/internal)` from module parent classes ([#2548](https://github.com/faker-js/faker/issues/2548)) ([77f54ad](https://github.com/faker-js/faker/commit/77f54ad78f9d7952b3b315f72878267f18ed1e69))

## [8.3.0](https://github.com/faker-js/faker/compare/v8.2.0...v8.3.0) (2023-11-14)


### Features

* **person:** use fake patterns for jobTitle ([#2528](https://github.com/faker-js/faker/issues/2528)) ([b40ad45](https://github.com/faker-js/faker/commit/b40ad45ad3cca6769968a63264e6d925bf4b328a))


### Bug Fixes

* **date:** ensures correct range for birthdate ([#2535](https://github.com/faker-js/faker/issues/2535)) ([7ce8c28](https://github.com/faker-js/faker/commit/7ce8c285cb5b2d13e95a17be78ecbd52f3c7d9db))
* **finance:** maskedNumber has incorrect defaults ([#2494](https://github.com/faker-js/faker/issues/2494)) ([e0ba50b](https://github.com/faker-js/faker/commit/e0ba50b37e438503ed1899bff35afc92b4f8f49c))
* **locale:** improve Swedish phone numbers format ([#2520](https://github.com/faker-js/faker/issues/2520)) ([e4865df](https://github.com/faker-js/faker/commit/e4865df199359797397cc3c5455f3a1a9c179f93))


### Changed Locales

* **locale:** remove fr_CH data which is identical to fr ([#2526](https://github.com/faker-js/faker/issues/2526)) ([fafcba4](https://github.com/faker-js/faker/commit/fafcba473f8a91eeb8230ebdc1ad5039b25091e1))


### New Locales

* **locale:** add person to fr_SN ([#2537](https://github.com/faker-js/faker/issues/2537)) ([ef965da](https://github.com/faker-js/faker/commit/ef965da48a8089e6bb19bcf260bfcd8af1a43799))
* **locale:** add Senegal locale ([#2525](https://github.com/faker-js/faker/issues/2525)) ([6df70bc](https://github.com/faker-js/faker/commit/6df70bce16500ab74a37f932f2e17a08f297430b))
* **locale:** add streets to location fr_SN ([#2536](https://github.com/faker-js/faker/issues/2536)) ([36fc517](https://github.com/faker-js/faker/commit/36fc517d17591c8ea1d5135d9a93c7591e3d1f74))
* **locale:** and location to fr_SN ([#2533](https://github.com/faker-js/faker/issues/2533)) ([f730125](https://github.com/faker-js/faker/commit/f730125ffb941fe936d6a18c775cbe3a99b312dc))

## [8.2.0](https://github.com/faker-js/faker/compare/v8.1.0...v8.2.0) (2023-10-14)


### Features

* support custom randomizer ([#2284](https://github.com/faker-js/faker/issues/2284)) ([5410239](https://github.com/faker-js/faker/commit/5410239245b4a6fe8c1976f8aa33c970923f9f40))


### Bug Fixes

* **docs:** revert filter code that breaks search in docs ([#2425](https://github.com/faker-js/faker/issues/2425)) ([c498c09](https://github.com/faker-js/faker/commit/c498c091f488db287684690ab4ff109b1589523f))
* **locale:** Dutch phone number ([#2400](https://github.com/faker-js/faker/issues/2400)) ([005369b](https://github.com/faker-js/faker/commit/005369b29c7ee290a870396ff0acc85e3f715e10))


### New Locales

* **locale:** add airline database science commerce and vehicle for zh_CN ([#2395](https://github.com/faker-js/faker/issues/2395)) ([9c96c0a](https://github.com/faker-js/faker/commit/9c96c0a131e0609a21dc5ee110cc407e76852373))
* **locale:** add street_name to `en_US`, `en_GB` and `en` ([#2371](https://github.com/faker-js/faker/issues/2371)) ([491d319](https://github.com/faker-js/faker/commit/491d3191213e2ceaaee46dcc50ac25c3995ba2d5))
* **locale:** add unionpay credit card for zh_CN ([#2338](https://github.com/faker-js/faker/issues/2338)) ([74eeccc](https://github.com/faker-js/faker/commit/74eecccd3af702d8a1d8072f94032ccb54293cb1))

## [8.1.0](https://github.com/faker-js/faker/compare/v8.0.2...v8.1.0) (2023-09-19)


### Features

* **color:** migrate hu human colors ([#2157](https://github.com/faker-js/faker/issues/2157)) ([19635a7](https://github.com/faker-js/faker/commit/****************************************))
* **commerce:** add method for generating ISBN-10 and ISBN-13 ([#2240](https://github.com/faker-js/faker/issues/2240)) ([cb4ef28](https://github.com/faker-js/faker/commit/cb4ef2846ad8d09cdc4ca5366b6057f9f6e38783))
* **image:** add image dataUri with base64 ([#2273](https://github.com/faker-js/faker/issues/2273)) ([869b9b4](https://github.com/faker-js/faker/commit/869b9b49e77848f2ad9677e54b4ab6db6cf5c58b))
* **location:** add en_IE postcodes ([#2149](https://github.com/faker-js/faker/issues/2149)) ([e92c313](https://github.com/faker-js/faker/commit/e92c313970a9830f3328b20504313fc9ba2e61a5))
* **location:** add ne postcodes ([#2148](https://github.com/faker-js/faker/issues/2148)) ([36d1d3a](https://github.com/faker-js/faker/commit/36d1d3a6260067708474d057281d71bf3f95daf1))
* **location:** add states for mk ([#2271](https://github.com/faker-js/faker/issues/2271)) ([1b3e5eb](https://github.com/faker-js/faker/commit/1b3e5ebd8a6a6d70b50c1acae11d4b86c2858a2b))
* **location:** add states for pt_PT ([#2269](https://github.com/faker-js/faker/issues/2269)) ([a4631db](https://github.com/faker-js/faker/commit/a4631dbb82e157bf5b9e63b26de47df396b91f50))
* **location:** add states for sr_RS_latin ([#2270](https://github.com/faker-js/faker/issues/2270)) ([1de471f](https://github.com/faker-js/faker/commit/1de471f83a52b2533bb95ea462c44289cb9ea497))
* **location:** es province should be county ([#2156](https://github.com/faker-js/faker/issues/2156)) ([bbda1d7](https://github.com/faker-js/faker/commit/bbda1d7e2ce0b0bd33a3cc78458a73cd79e3eca0))
* **location:** fi addresses ([#2146](https://github.com/faker-js/faker/issues/2146)) ([1da6785](https://github.com/faker-js/faker/commit/1da6785af373566ce6399e93ea0aca465e19e898))
* **location:** Support ISO 3166-1 numeric country codes ([#2325](https://github.com/faker-js/faker/issues/2325)) ([82b779d](https://github.com/faker-js/faker/commit/82b779da5e87fddd7b5a5564b7228ac54b44d349))
* **location:** update en county list ([#2238](https://github.com/faker-js/faker/issues/2238)) ([6bb4775](https://github.com/faker-js/faker/commit/6bb4775747288130c1bbca5fe02eccc549465682))
* **lorem:** seed AR lorem ([#2147](https://github.com/faker-js/faker/issues/2147)) ([6137801](https://github.com/faker-js/faker/commit/6137801ebfe2ff51ca82d52fcb2a63085bd17bcd))
* **metadata:** add method to access metadata ([#2143](https://github.com/faker-js/faker/issues/2143)) ([fd8cfe5](https://github.com/faker-js/faker/commit/fd8cfe53fd59b89f91a07327711faafdd821999e))
* split SimpleFaker class from Faker ([#2369](https://github.com/faker-js/faker/issues/2369)) ([d6a4f8c](https://github.com/faker-js/faker/commit/d6a4f8c2ddf9e957e875bc3fab77e496104d1320))


### Bug Fixes

* **helpers:** prevent uniqueArray from hanging ([#2239](https://github.com/faker-js/faker/issues/2239)) ([3dece09](https://github.com/faker-js/faker/commit/3dece0904933f9632afabdacd0f2b5b32d8bde2a))
* **image:** dataUri is not random ([#2316](https://github.com/faker-js/faker/issues/2316)) ([a7d25fa](https://github.com/faker-js/faker/commit/a7d25fa6224c686bca8b21cb7961910362008ffb))
* **locale:** invalid date definitions ([#2293](https://github.com/faker-js/faker/issues/2293)) ([3cecae9](https://github.com/faker-js/faker/commit/****************************************))
* **locale:** limited ja first names without passing sex ([#2190](https://github.com/faker-js/faker/issues/2190)) ([5de8874](https://github.com/faker-js/faker/commit/5de8874a8a3cdf2d1529a7d42b1f39ab6e45efe5))
* **locale:** remove continent from ja countries ([#2194](https://github.com/faker-js/faker/issues/2194)) ([cdd162a](https://github.com/faker-js/faker/commit/cdd162a6fed3588a0fd34dca8db46c078d4c7b22))
* **locale:** remove duplicated countries in `ja` locale ([#2189](https://github.com/faker-js/faker/issues/2189)) ([785a38a](https://github.com/faker-js/faker/commit/785a38acc5fdc0414cc54a10e8cd9b8bee669852))
* **location:** avoid hyphenated surnames in city patterns ([#2119](https://github.com/faker-js/faker/issues/2119)) ([7a4bb43](https://github.com/faker-js/faker/commit/7a4bb43f56c22578e63d02954586a001384d9af2))
* **location:** Czech postcode format ([#2268](https://github.com/faker-js/faker/issues/2268)) ([37898ca](https://github.com/faker-js/faker/commit/37898ca78bae0a516a03c4ecd053bfab523df2ed))
* **location:** Dutch postal codes can never start with '0' ([#2326](https://github.com/faker-js/faker/issues/2326)) ([f195e06](https://github.com/faker-js/faker/commit/f195e06905a8384965990e686440c3714976445c))
* **location:** Pad en_US ZIP codes left to 5 characters if needed ([#2278](https://github.com/faker-js/faker/issues/2278)) ([0ca1e44](https://github.com/faker-js/faker/commit/0ca1e442d6a882677fc73012fc481d48ca1905a5))
* remove unrelated from de_CH country codes ([#2304](https://github.com/faker-js/faker/issues/2304)) ([aa1bb13](https://github.com/faker-js/faker/commit/aa1bb131d874591e4d46671b1e82b23fc2a7ad74))
* **test:** fix imports for jsdocs example verification ([#2281](https://github.com/faker-js/faker/issues/2281)) ([2fe1308](https://github.com/faker-js/faker/commit/2fe13089c96f6a2826e60c45fa1acf20dde06d41))
* **test:** typedoc signature test issues ([#2280](https://github.com/faker-js/faker/issues/2280)) ([02fc7ca](https://github.com/faker-js/faker/commit/02fc7caf1cf757ae5901b8d37cd5d8d7ed123724))


### Changed Locales

* **locale:** cs_CZ state_abbr not applicable ([#2140](https://github.com/faker-js/faker/issues/2140)) ([e86f3b6](https://github.com/faker-js/faker/commit/e86f3b6b2619cc7fdf28213de0e6ff676a3e2d7e))


### New Locales

* **locale:** add bio, animal, word, music for zh_CN ([#2332](https://github.com/faker-js/faker/issues/2332)) ([8e4ea67](https://github.com/faker-js/faker/commit/8e4ea6790df4b987d1a8c6d32d1eee6db924bed3))
* **locale:** add company name to zh_CN ([#2203](https://github.com/faker-js/faker/issues/2203)) ([c3b540f](https://github.com/faker-js/faker/commit/c3b540fa501ba4729350562f38872df35f23818c))
* **locale:** add da person category ([#2331](https://github.com/faker-js/faker/issues/2331)) ([466b804](https://github.com/faker-js/faker/commit/466b8043577a14c54545a942c05ade343a9ca260))
* **locale:** add database for German (de) ([#2226](https://github.com/faker-js/faker/issues/2226)) ([ee12c32](https://github.com/faker-js/faker/commit/ee12c32fced6cdf6fef2c5993af84825fd88606f))
* **locale:** add direction to ja ([#2191](https://github.com/faker-js/faker/issues/2191)) ([2fa60de](https://github.com/faker-js/faker/commit/2fa60dec77551c055571ce3439f00c8898399d60))
* **locale:** add Dutch province abbreviations ([#2232](https://github.com/faker-js/faker/issues/2232)) ([4a15bd0](https://github.com/faker-js/faker/commit/4a15bd0d4eea5e5f6a7d1e6be0c2bf9e59feb069))
* **locale:** add Esperanto ([#2230](https://github.com/faker-js/faker/issues/2230)) ([d91057d](https://github.com/faker-js/faker/commit/d91057ddfd36b15af783ab6bcb1d679cf5202347))
* **locale:** add finance module for zh_CN ([#2370](https://github.com/faker-js/faker/issues/2370)) ([aea4c9b](https://github.com/faker-js/faker/commit/aea4c9bda8e75e928045bfca9e11e216a581ce05))
* **locale:** add GB subdivision codes ([#2233](https://github.com/faker-js/faker/issues/2233)) ([5eec65f](https://github.com/faker-js/faker/commit/5eec65f7179b7db68efe7740695b37958b360212))
* **locale:** add hacker for zh_CN ([#2337](https://github.com/faker-js/faker/issues/2337)) ([9176fcb](https://github.com/faker-js/faker/commit/9176fcbaa74cf0a341374bb44340e410255e0917))
* **locale:** add minimal da locale, start with location ([#2324](https://github.com/faker-js/faker/issues/2324)) ([c158b38](https://github.com/faker-js/faker/commit/c158b38b025b333c97cf174d516776b61015179f))
* **locale:** add person data for yo locale ([#2363](https://github.com/faker-js/faker/issues/2363)) ([76886ad](https://github.com/faker-js/faker/commit/76886ad96d694268856de83747d7c754b6504050))
* **locale:** add states for Croatia (hr) ([#2142](https://github.com/faker-js/faker/issues/2142)) ([8a6ce49](https://github.com/faker-js/faker/commit/8a6ce4978fada63f6364237e968f427e0a198135))
* **locale:** add states for Turkey (tr) ([#2231](https://github.com/faker-js/faker/issues/2231)) ([2afa23f](https://github.com/faker-js/faker/commit/2afa23f32f4bb52aeecb6189282b37c6c48f7c84))
* **locale:** add states to vi locale and fix city name ([#2128](https://github.com/faker-js/faker/issues/2128)) ([bc2aaab](https://github.com/faker-js/faker/commit/bc2aaab7637ea7a3e6e62e790cd4681ccaaccea0))
* **locale:** add word category for da ([#2359](https://github.com/faker-js/faker/issues/2359)) ([8e5bc22](https://github.com/faker-js/faker/commit/8e5bc222c67cad38fd406823f5aade91a409e12a))
* **locale:** added commerce and company data for da ([#2378](https://github.com/faker-js/faker/issues/2378)) ([604d52d](https://github.com/faker-js/faker/commit/604d52d9874db1763c185483ed182199f7a00f4f))
* **locale:** added word and music to Farsi ([#2210](https://github.com/faker-js/faker/issues/2210)) ([7687511](https://github.com/faker-js/faker/commit/7687511bddf9c7b9721bb2203f86d64dfbd1a9c4))
* **locale:** fix wide month and add music for zh_CN ([#2380](https://github.com/faker-js/faker/issues/2380)) ([501602b](https://github.com/faker-js/faker/commit/501602b6b3ddcb100eea7e2441e7f20093b9e183))
* **locale:** improve persons and locations for zh_CN ([#2318](https://github.com/faker-js/faker/issues/2318)) ([87d3423](https://github.com/faker-js/faker/commit/87d3423960d326494d1710946f29fb3930d1a300))
* **locale:** improve zh_TW ([#2320](https://github.com/faker-js/faker/issues/2320)) ([c5dfe27](https://github.com/faker-js/faker/commit/c5dfe27acd13003962a5cd93f9632392fda41399))
* **locale:** support color and date for zh_CN ([#2223](https://github.com/faker-js/faker/issues/2223)) ([6736cbf](https://github.com/faker-js/faker/commit/6736cbf9aeeb02a3eb080f0f85c37f141f6924fd))
* **locale:** update Dutch locale (nl) ([#2237](https://github.com/faker-js/faker/issues/2237)) ([12a511c](https://github.com/faker-js/faker/commit/12a511c9e4781929620dc2906f87661c5f58a480))

### [8.0.2](https://github.com/faker-js/faker/compare/v8.0.1...v8.0.2) (2023-05-27)


### Bug Fixes

* **git:** limit need for Intl to specific method ([#2172](https://github.com/faker-js/faker/issues/2172)) ([a5e73f8](https://github.com/faker-js/faker/commit/a5e73f8a11baeefcf03f344cb5e4dde096a0b364))
* **locale:** en_HK last_name_patterns should be last_name_pattern ([#2154](https://github.com/faker-js/faker/issues/2154)) ([03cc391](https://github.com/faker-js/faker/commit/03cc39154886a0708e13ee2db239c001ce20aa6e))
* **locale:** ro_MD city should be city_pattern ([#2155](https://github.com/faker-js/faker/issues/2155)) ([c173150](https://github.com/faker-js/faker/commit/c173150a96a95818b0c4fd6cb2d41daea7e9ad0a))

## [8.0.1](https://github.com/faker-js/faker/compare/v8.0.0...v8.0.1) (2023-05-15)


* **docs:** switch doc links to stable ([#2152](https://github.com/faker-js/faker/pull/2152)) ([8f741bd](https://github.com/faker-js/faker/commit/8f741bd4bda455517fd048b83d76bd8e6d89d78e))

## [8.0.0](https://github.com/faker-js/faker/compare/v8.0.0-beta.1...v8.0.0) (2023-05-11)


### Bug Fixes

* **docs:** fix faker v7 code example for locale migration ([#2125](https://github.com/faker-js/faker/issues/2125)) ([b570461](https://github.com/faker-js/faker/commit/b57046190754b651842dba14170730f3f7ebd8b5))
* **locales:** add real cities for zu_ZA and af_ZA, update en_ZA city list ([#2141](https://github.com/faker-js/faker/issues/2141)) ([ee8c97c](https://github.com/faker-js/faker/commit/ee8c97cf6e80025707d5cf458981a14be06406ed))

## [8.0.0-beta.1](https://github.com/faker-js/faker/compare/v8.0.0-beta.0...v8.0.0-beta.1) (2023-05-06)


### Features

* **helpers:** new method `objectEntry` ([#2123](https://github.com/faker-js/faker/issues/2123)) ([c092aa1](https://github.com/faker-js/faker/commit/c092aa1276a5c249de1ada47e807f12dd6de36f7))


### Bug Fixes

* **locale:** el county should be city_name ([#2106](https://github.com/faker-js/faker/issues/2106)) ([1cadfb9](https://github.com/faker-js/faker/commit/1cadfb97856da17047fcb5dcb504c3418eeed145))
* **locale:** remove incorrect and unused city_prefix/suffix for sk ([#2117](https://github.com/faker-js/faker/issues/2117)) ([e094a5a](https://github.com/faker-js/faker/commit/e094a5a7b00bed7da3610bdbb949cc00320d5094))
* **locale:** remove unrealistic fictional patterns in ne, ro, tr ([#2120](https://github.com/faker-js/faker/issues/2120)) ([d9ec87e](https://github.com/faker-js/faker/commit/d9ec87e6697ac3c814932c7e3ff6fa2e21d62f3a))
* **locale:** rename city_prefix to city_name for en_NG, en_ZA, en_AU_ocker ([#2111](https://github.com/faker-js/faker/issues/2111)) ([a0fb69d](https://github.com/faker-js/faker/commit/a0fb69d666c85c40b1f8620dbfa0516e9872397f))
* **location:** no leading zero on building number or secondary address ([#2032](https://github.com/faker-js/faker/issues/2032)) ([a8dc7e0](https://github.com/faker-js/faker/commit/a8dc7e07f6d5ee2ae38724ba5d503d7b88bd7147))
* **test:** fix failing latitude test ([#2116](https://github.com/faker-js/faker/issues/2116)) ([7f9e9df](https://github.com/faker-js/faker/commit/7f9e9df4221d27b0b41a8ca04ea2cf69e7065613))


### Changed Locales

* **locale:** add city_name to city_patterns ([#2104](https://github.com/faker-js/faker/issues/2104)) ([b72d52e](https://github.com/faker-js/faker/commit/b72d52e5d5501579f6b98629d020292ceaa78c60))


### New Locales

* **locale:** add ro_MD locale ([#2084](https://github.com/faker-js/faker/issues/2084)) ([08cf1d8](https://github.com/faker-js/faker/commit/08cf1d85c8ba29991e47dc4b764172abb2cc0705))

## [8.0.0-beta.0](https://github.com/faker-js/faker/compare/v8.0.0-alpha.2...v8.0.0-beta.0) (2023-04-29)


### ⚠ BREAKING CHANGES

* **location:** rename definition city to city_pattern (#2094)
* **location:** rename definition street to street_pattern (#2051)
* **helpers:** remove default value from arrayElement (#2045)
* **location:** zip code state (#1874)
* **finance:** simplify account implementation (#1992)
* **locale:** move title to metadata (#1978)

### Features

* add base locale ([#1748](https://github.com/faker-js/faker/issues/1748)) ([f890d62](https://github.com/faker-js/faker/commit/f890d627164c5436e2b2a3ac05239a1a1634015e))
* **date:** introduce anytime ([#2096](https://github.com/faker-js/faker/issues/2096)) ([3fc7bf1](https://github.com/faker-js/faker/commit/****************************************))
* **helpers:** add fromRegExp method ([#1569](https://github.com/faker-js/faker/issues/1569)) ([8516bfb](https://github.com/faker-js/faker/commit/8516bfb109741231a49ec8572176e5a12b4c1a37))
* introduce locale proxy ([#2004](https://github.com/faker-js/faker/issues/2004)) ([8a0bbf5](https://github.com/faker-js/faker/commit/8a0bbf5faa03c294d308a13fe210ba6aaeef6968))


### Bug Fixes

* add support for equals on locale proxies ([#2092](https://github.com/faker-js/faker/issues/2092)) ([4d0458c](https://github.com/faker-js/faker/commit/4d0458c96071917c8c3bb85fa61544caf8ff1763))
* export AircraftType correctly as type ([#2077](https://github.com/faker-js/faker/issues/2077)) ([16d611f](https://github.com/faker-js/faker/commit/16d611f7ec1669480014d42549e2fabc6f8716a0))
* **image:** use seeded picsum ([#1968](https://github.com/faker-js/faker/issues/1968)) ([b5bfca4](https://github.com/faker-js/faker/commit/b5bfca4043903340dee9ed3cd8965bcdbee85211))
* **locale:** es cities should be in city_name not city_prefix, remove extraneous city_suffix ([#2109](https://github.com/faker-js/faker/issues/2109)) ([789c8a2](https://github.com/faker-js/faker/commit/789c8a207c941a3f28e4185b94a9c0c317151e2c))
* **locale:** fix broken street address reference in fr_CH ([#2007](https://github.com/faker-js/faker/issues/2007)) ([cbbc076](https://github.com/faker-js/faker/commit/cbbc0760c0121ce31997c0da1ce9d56189d42d31))
* **locale:** fix en_HK city_pattern ([#2105](https://github.com/faker-js/faker/issues/2105)) ([75e4892](https://github.com/faker-js/faker/commit/75e4892b7d5c0d074ba141bd11b622cb9a328eb7))
* **locales:** remove wrong phone number in Portugal ([#2029](https://github.com/faker-js/faker/issues/2029)) ([89c4cf3](https://github.com/faker-js/faker/commit/89c4cf38d7614b2f7d74415455283a151bf82d6c))
* script error ([#2085](https://github.com/faker-js/faker/issues/2085)) ([4ca3b75](https://github.com/faker-js/faker/commit/4ca3b75c21cb795bb791335b4a316f435b3dab7f))
* **types:** locale proxy ([#2099](https://github.com/faker-js/faker/issues/2099)) ([971f363](https://github.com/faker-js/faker/commit/971f36371bf924b334cb7766fd87afa7b484119a))
* typo in Faker jsdocs ([#2008](https://github.com/faker-js/faker/issues/2008)) ([9d904a3](https://github.com/faker-js/faker/commit/9d904a3308db512b88692731b0390d8caf59c211))


### Changed Locales

* **locale:** move title to metadata ([#1978](https://github.com/faker-js/faker/issues/1978)) ([c5eb72c](https://github.com/faker-js/faker/commit/c5eb72c7dbc03f1258062048aa0e1270719fd9e3))
* **locale:** rename cz to cs_CZ ([#2013](https://github.com/faker-js/faker/issues/2013)) ([a95fc51](https://github.com/faker-js/faker/commit/a95fc51279cb942fa9608334ffea52e57732cd3f))
* **locale:** rename ge to ka_GE ([#2010](https://github.com/faker-js/faker/issues/2010)) ([1f763f8](https://github.com/faker-js/faker/commit/1f763f89aad5d5b6fe42cfd01f9953e2de092cc5))
* **locale:** use null as not applicable ([#2078](https://github.com/faker-js/faker/issues/2078)) ([acb9cf5](https://github.com/faker-js/faker/commit/acb9cf5a81ec42e64a59f4708345f1af63147a34))


### New Locales

* **locale:** add additional metadata properties ([#2025](https://github.com/faker-js/faker/issues/2025)) ([a49aa0d](https://github.com/faker-js/faker/commit/a49aa0d7831a949728b6831b96f15c4971f1e1d9))
* **locale:** add af_ZA and zu_ZA states ([#1983](https://github.com/faker-js/faker/issues/1983)) ([6ba5093](https://github.com/faker-js/faker/commit/6ba509392365103adcc4b5d6ca62fc74204a39ec))
* **locale:** add city names for th ([#2075](https://github.com/faker-js/faker/issues/2075)) ([ed19bef](https://github.com/faker-js/faker/commit/ed19befb04dbb7a8c5b59b1c015c6ccbce109869))
* **locale:** add en_HK locale ([#2083](https://github.com/faker-js/faker/issues/2083)) ([698fd7d](https://github.com/faker-js/faker/commit/698fd7d909740bb9a9b7e9dfe822ef8632e3d4c6))
* **locale:** add en_US location.county data ([#1996](https://github.com/faker-js/faker/issues/1996)) ([34b743a](https://github.com/faker-js/faker/commit/34b743ab8a859669a0f7b37910c2cbb9e294b534))
* **locale:** add fi states ([#1986](https://github.com/faker-js/faker/issues/1986)) ([88e561a](https://github.com/faker-js/faker/commit/88e561a5490003a41665eb007c5c28ff0800ae09))
* **locale:** add hu states ([#1990](https://github.com/faker-js/faker/issues/1990)) ([3ae3691](https://github.com/faker-js/faker/commit/3ae36919d052cbbcf21bf3f5cb16088668544f02))
* **locale:** add hyphenated names to English locales ([#1872](https://github.com/faker-js/faker/issues/1872)) ([17f0488](https://github.com/faker-js/faker/commit/17f0488b1b85820f989c89d70ea883de4fa19506))
* **locale:** add locale TH ([#1922](https://github.com/faker-js/faker/issues/1922)) ([3c176b2](https://github.com/faker-js/faker/commit/3c176b23c908cea57a1a0a6c3b7fca86a39989ab))
* **locale:** de color human ([#1997](https://github.com/faker-js/faker/issues/1997)) ([2675ec2](https://github.com/faker-js/faker/commit/2675ec20fe28ebf89fc8b5b939c9ae7fbde7559f))
* **locale:** extend Hebrew locale data ([#1721](https://github.com/faker-js/faker/issues/1721)) ([54c2cf8](https://github.com/faker-js/faker/commit/54c2cf82269da03c067b1ee1c158b84282501c15))


* **finance:** simplify account implementation ([#1992](https://github.com/faker-js/faker/issues/1992)) ([de078de](https://github.com/faker-js/faker/commit/de078de89bf1b52de153f88352cf534811673886))
* **helpers:** remove default value from arrayElement ([#2045](https://github.com/faker-js/faker/issues/2045)) ([0564446](https://github.com/faker-js/faker/commit/05644468208601f842081d8ce4545f9d37b55461))
* **location:** rename definition city to city_pattern ([#2094](https://github.com/faker-js/faker/issues/2094)) ([8cd1965](https://github.com/faker-js/faker/commit/8cd196562be43d378bc82d0dfe8b003fd84c404f))
* **location:** rename definition street to street_pattern ([#2051](https://github.com/faker-js/faker/issues/2051)) ([3529ef7](https://github.com/faker-js/faker/commit/3529ef7a97513286ef9ecfdfb3a460bd9723f69f))
* **location:** zip code state ([#1874](https://github.com/faker-js/faker/issues/1874)) ([8574125](https://github.com/faker-js/faker/commit/857412514e94d1d7f30aab9a9e01c24aa05d7dee))

## [8.0.0-alpha.2](https://github.com/faker-js/faker/compare/v8.0.0-alpha.1...v8.0.0-alpha.2) (2023-03-24)


### Bug Fixes

* **locale:** split zh_TW first_names by gender ([#1941](https://github.com/faker-js/faker/issues/1941)) ([4e97200](https://github.com/faker-js/faker/commit/4e97200b26ace515b2226c322c40c36fa7573c4f))
* revert types field ([#1966](https://github.com/faker-js/faker/issues/1966)) ([9dcf83f](https://github.com/faker-js/faker/commit/9dcf83f9b0b7353da6083da476d30dab74a52a50))


### New Locales

* **locale:** add Serbian (Latin) language ([#1801](https://github.com/faker-js/faker/issues/1801)) ([d2046e8](https://github.com/faker-js/faker/commit/d2046e8b08c582d74bbd0d03eb8e64db9e721400))

## [8.0.0-alpha.1](https://github.com/faker-js/faker/compare/v8.0.0-alpha.0...v8.0.0-alpha.1) (2023-03-20)


### ⚠ BREAKING CHANGES

* remove dynamic locale switching support (#1735)
* **company:** rename bs to buzz (#1860)
* **location:** countryCode use ISO-3166-1 (#1720)
* **number:** default to high precision float (#1675)
* **locale:** remove unused separator entry (#1731)

### Features

* **airline:** add airline module ([#1699](https://github.com/faker-js/faker/issues/1699)) ([579c9ad](https://github.com/faker-js/faker/commit/579c9adb5affa01ee5ee384942f56d662a21022e))
* **company:** add realistic Hungarian company names ([#1758](https://github.com/faker-js/faker/issues/1758)) ([2e6b136](https://github.com/faker-js/faker/commit/2e6b136c61a7cfc799c72a86a38ca4a505d94e3c))
* **date:** introduce faker.defaultRefDate and setDefaultRefDate ([#1757](https://github.com/faker-js/faker/issues/1757)) ([3a44d5f](https://github.com/faker-js/faker/commit/3a44d5fa48e8b28a7b9422a18262e39af1d1cb91))
* **finance:** currency object ([#1809](https://github.com/faker-js/faker/issues/1809)) ([59157a4](https://github.com/faker-js/faker/commit/59157a429a1bcde3f78bd9009f6de02358365ba2))
* **helpers:** add length range support in `arrayElements` ([#1772](https://github.com/faker-js/faker/issues/1772)) ([b86638d](https://github.com/faker-js/faker/commit/b86638d478fa21fafa9aaa3e247a08b479bf5a9d))
* **helpers:** add new faker.helpers.weightedArrayElement ([#1654](https://github.com/faker-js/faker/issues/1654)) ([59824e6](https://github.com/faker-js/faker/commit/59824e66ad1e14be2d61b8425e8f8e9547863565))
* **helpers:** allow empty string in fake ([#1679](https://github.com/faker-js/faker/issues/1679)) ([f0d2ffb](https://github.com/faker-js/faker/commit/f0d2ffb256e5aefb98d4f38905ef081c8e6af43e))
* **helpers:** new method enumValue ([#1920](https://github.com/faker-js/faker/issues/1920)) ([f2abf8b](https://github.com/faker-js/faker/commit/f2abf8b49439fc3c6197ecc9a16e212c9e64497a))
* **locales:** add mergeLocales utility ([#1707](https://github.com/faker-js/faker/issues/1707)) ([788fce0](https://github.com/faker-js/faker/commit/788fce048f0233552df47f3407c06d8f83c32b0c))
* **number:** add binary and octal random number generation ([#1708](https://github.com/faker-js/faker/issues/1708)) ([d3229fc](https://github.com/faker-js/faker/commit/d3229fcdf28b5a1abdbc44a7bcdde934bd472bf2))
* **number:** default to high precision float ([#1675](https://github.com/faker-js/faker/issues/1675)) ([1ebbead](https://github.com/faker-js/faker/commit/1ebbead194a9583dbd0f21f136c9d2bf8f84a50f))
* **person:** add Portuguese BR Language to western zodiac sign ([#1814](https://github.com/faker-js/faker/issues/1814)) ([fd96094](https://github.com/faker-js/faker/commit/fd960941a5434464736b236e73df9813bc22c86c))
* **person:** add short bio ([#1696](https://github.com/faker-js/faker/issues/1696)) ([2a2e054](https://github.com/faker-js/faker/commit/2a2e054a16a45bc5cd56bdd923b7f5140eb12699))
* **person:** split prefix into gendered versions ([#1665](https://github.com/faker-js/faker/issues/1665)) ([27dff93](https://github.com/faker-js/faker/commit/27dff93aa27d755874aa5022c78f17ff8e9cf7e0))
* provide enums for color values ([#1910](https://github.com/faker-js/faker/issues/1910)) ([a001090](https://github.com/faker-js/faker/commit/a0010900205ed8b1d1ba2adefe222c1cb41c1a70))
* **string:** add binary and octal random string generation ([#1710](https://github.com/faker-js/faker/issues/1710)) ([80e0a96](https://github.com/faker-js/faker/commit/80e0a9688099677a74614ccbedc9231b6382c4eb))
* **string:** generate string from characters ([#1717](https://github.com/faker-js/faker/issues/1717)) ([47b2cfc](https://github.com/faker-js/faker/commit/47b2cfc76b790647e398bf9883368a10b2ff5a68))
* **string:** nanoid ([#1716](https://github.com/faker-js/faker/issues/1716)) ([099e76c](https://github.com/faker-js/faker/commit/099e76ce0fb180beb5fd62d72a07c236e04cdca0))


### Bug Fixes

* consistent locale names ([#1884](https://github.com/faker-js/faker/issues/1884)) ([cf2ce68](https://github.com/faker-js/faker/commit/cf2ce6830d42a1a91c1a2defcc02d86c1a782cf8))
* correct types field ([#1878](https://github.com/faker-js/faker/issues/1878)) ([956a15f](https://github.com/faker-js/faker/commit/956a15fff5c3714485e8e612df62034a5f78c0c5))
* **currency:** modernise currency list for 2023 ([#1880](https://github.com/faker-js/faker/issues/1880)) ([e6f949b](https://github.com/faker-js/faker/commit/e6f949b73e781d7e3f1fb4adf8f87d66b4aa29a1))
* discord link ([#1891](https://github.com/faker-js/faker/issues/1891)) ([8a97cac](https://github.com/faker-js/faker/commit/8a97cac30f94bf7c29bfd6ffbad6ebf9c05fa2b1))
* **git:** commitEntry normalize user ([#1813](https://github.com/faker-js/faker/issues/1813)) ([667599d](https://github.com/faker-js/faker/commit/667599d8fb59c31166b897799f30788edc5f54d7))
* **helpers:** uniform distribution in helpers.arrayElements ([#1770](https://github.com/faker-js/faker/issues/1770)) ([2b84b33](https://github.com/faker-js/faker/commit/2b84b3389a2719e0f365055990a51673acd07ac9))
* **internet:** filter banned dots from email addresses ([#1883](https://github.com/faker-js/faker/issues/1883)) ([f8926c7](https://github.com/faker-js/faker/commit/f8926c7b1311e5c1e617289f9ee3760b522bea3f))
* **internet:** fix invalid emails in some locales ([#1746](https://github.com/faker-js/faker/issues/1746)) ([1e4e869](https://github.com/faker-js/faker/commit/1e4e8699e59f3b5b9c1e1d6ad9b89ee4cc254e95))
* **locale:** az company name_patterns return undefined in name ([#1914](https://github.com/faker-js/faker/issues/1914)) ([bb72a66](https://github.com/faker-js/faker/commit/bb72a6679b7005271e847324a2affbd187f65716))
* **locale:** correct capitalization of last_name in en_AU ([#1912](https://github.com/faker-js/faker/issues/1912)) ([4f14533](https://github.com/faker-js/faker/commit/4f14533418e23496fa463f08e8276cb230dc7643))
* **locale:** filter inappropriate words in the en locale ([#1745](https://github.com/faker-js/faker/issues/1745)) ([dfa647d](https://github.com/faker-js/faker/commit/dfa647dc07ea25f962c4df913bae2de082e8acc4))
* **locale:** fix pt_PT street addresses ([#1911](https://github.com/faker-js/faker/issues/1911)) ([8f0abd3](https://github.com/faker-js/faker/commit/8f0abd3e1018d3c814e9ade5e999457e15fee47f))
* **locale:** remove invalid city patterns from pt_BR ([#1913](https://github.com/faker-js/faker/issues/1913)) ([7ae5cdb](https://github.com/faker-js/faker/commit/7ae5cdb59d4c0e5550ea9f9c2b065360b0d359a5))
* **locale:** replace Ð with Đ in hr and vi ([#1810](https://github.com/faker-js/faker/issues/1810)) ([ff53587](https://github.com/faker-js/faker/commit/ff5358732fdbd224ef8c7e71a9591fa4fd68da3c))
* more discord links ([#1893](https://github.com/faker-js/faker/issues/1893)) ([f948e2e](https://github.com/faker-js/faker/commit/f948e2e8ccc93b31557f8c65f610f52dff28e2e4))
* **person:** avoid repeated last name via new last_name_patterns ([#1819](https://github.com/faker-js/faker/issues/1819)) ([0f98518](https://github.com/faker-js/faker/commit/0f98518f0906a9660dcdb8993b6cb983c5e4c92f))
* **person:** change fullName to use name patterns ([#1637](https://github.com/faker-js/faker/issues/1637)) ([1ae2f6f](https://github.com/faker-js/faker/commit/1ae2f6f489bcf7b317202877af3419ecc01bc1d0))
* **person:** improve to Dutch name generation ([#1778](https://github.com/faker-js/faker/issues/1778)) ([fbd0db5](https://github.com/faker-js/faker/commit/fbd0db533b5a46fe1175c02c65b3e61fca52d40d))
* **random:** prevent infinite do-while ([#1938](https://github.com/faker-js/faker/issues/1938)) ([256631d](https://github.com/faker-js/faker/commit/256631d6be4b2b40ae660a7e9052cde07a3da18c))
* **random:** remove deprecated method usage ([#1865](https://github.com/faker-js/faker/issues/1865)) ([25bd847](https://github.com/faker-js/faker/commit/25bd847545acb13291ac0a3704688793ca9a0933))


### Changed Locales

* **locale:** remove unused separator entry ([#1731](https://github.com/faker-js/faker/issues/1731)) ([0bc6c2f](https://github.com/faker-js/faker/commit/0bc6c2fbe65de9a9f706aa6f78f3b07940038461))


* **company:** rename bs to buzz ([#1860](https://github.com/faker-js/faker/issues/1860)) ([5f95aca](https://github.com/faker-js/faker/commit/5f95aca92158ce749215527c8d3691c19737e3f1))
* **location:** countryCode use ISO-3166-1 ([#1720](https://github.com/faker-js/faker/issues/1720)) ([a05c3e5](https://github.com/faker-js/faker/commit/a05c3e5fed805c5086a22841a81da0d86ab89386))
* remove dynamic locale switching support ([#1735](https://github.com/faker-js/faker/issues/1735)) ([9a35dc9](https://github.com/faker-js/faker/commit/9a35dc92260585683132172f10bcdec701ed661a))


### New Locales

* **locale:** add `state` and `state_abbr` to `cz` ([#1847](https://github.com/faker-js/faker/issues/1847)) ([f6c8ca3](https://github.com/faker-js/faker/commit/f6c8ca36b4e288697ae87a8bbdd027cd34947875))
* **locale:** add Luxembourg (French) locale ([#1693](https://github.com/faker-js/faker/issues/1693)) ([c66f7bd](https://github.com/faker-js/faker/commit/c66f7bd70f930c4a080cc24cc8d09baab260f6d0))
* **locale:** extend english company ([#1701](https://github.com/faker-js/faker/issues/1701)) ([f0e859b](https://github.com/faker-js/faker/commit/f0e859ba3033e54aefd71bf194da574593bc1631))

## [8.0.0-alpha.0](https://github.com/faker-js/faker/compare/v7.6.0...v8.0.0-alpha.0) (2022-12-24)


### ⚠ BREAKING CHANGES

* **company:** remove hardcoded name fallback pattern (#1451)
* **date:** unify signatures (#1534)
* **helpers:** rewrite shuffle (#1521)
* **image:** remove lorempixel (#1641)
* **image:** rewrite module (#1477)
* **internet:** ip now returns ipv4 and ipv6 (#1059)
* **locale:** change en_IND to en_IN (#1448)
* **location:** `latitude`/`longitude` returns number (#1064)
* **location:** `nearbyGPSCoordinate` returns number tuple (#1061)
* **location:** rename address module (#1469)
* **number:** change float default params (#1642)
* **number:** change int default params (#1643)
* **person:** rename name module (#1445)
* **string:** swap `allowLeadingZeros` default to `true` (#1602)
* cleanup deprecations (#1440)
* make mersenne internal (#1444)
* upgrade node minimum target (#1494)

### Features

* **datatype:** introduce probability option to boolean ([#1476](https://github.com/faker-js/faker/issues/1476)) ([838f836](https://github.com/faker-js/faker/commit/838f8369196baabb28529470c27125756e6f671e))
* **git:** add options.refDate to commitEntry ([#1512](https://github.com/faker-js/faker/issues/1512)) ([7c661c6](https://github.com/faker-js/faker/commit/7c661c63d4eb6345ef7aab1836dc7643d412dd0c))
* **helpers:** add rangeToNumber method and add range parameters ([#1486](https://github.com/faker-js/faker/issues/1486)) ([9cd716e](https://github.com/faker-js/faker/commit/9cd716e891d3bb8d9a8f9d43899d0dcd161e1832))
* **helpers:** fake from array ([#1453](https://github.com/faker-js/faker/issues/1453)) ([75a31f6](https://github.com/faker-js/faker/commit/75a31f620c880413c05f012f1924b2ad89fb950c))
* **helpers:** introduce `multiple` method ([#1545](https://github.com/faker-js/faker/issues/1545)) ([f06126a](https://github.com/faker-js/faker/commit/f06126a1ba8515d6e0b7733999d5cd2f8849be7a))
* **internet:** add options to url() ([#1480](https://github.com/faker-js/faker/issues/1480)) ([09e8356](https://github.com/faker-js/faker/commit/09e835664add0a342dd089f31bbae7d880198493))
* **internet:** ip now returns ipv4 and ipv6 ([#1059](https://github.com/faker-js/faker/issues/1059)) ([a90f2fe](https://github.com/faker-js/faker/commit/a90f2fe65c705a5593215b0a35945b77c1d575f1))
* **locale:** add dv locale ([#1506](https://github.com/faker-js/faker/issues/1506)) ([0b9989f](https://github.com/faker-js/faker/commit/0b9989fe0ec3dcb8e511060c1fe42bb6abe2b1ba))
* **locale:** add free email domains for zh_CN ([#1555](https://github.com/faker-js/faker/issues/1555)) ([f241928](https://github.com/faker-js/faker/commit/f24192868ef2475b633667c955e07e0a7a0ff681))
* **locale:** update Korean color, date, location, word, company ([#1492](https://github.com/faker-js/faker/issues/1492)) ([2ce8056](https://github.com/faker-js/faker/commit/2ce8056cc5f1fa874447da6077338eb793663179))
* **locale:** update location/country list ([#1676](https://github.com/faker-js/faker/issues/1676)) ([ce1fa2f](https://github.com/faker-js/faker/commit/ce1fa2f41df18f47be422d4f8dbcaf87b7d841a3))
* **locate:** add japanese company ([#1573](https://github.com/faker-js/faker/issues/1573)) ([6e1009a](https://github.com/faker-js/faker/commit/6e1009ae0ab6a185eaf808e2dd4d8dcab65c79b6))
* **location:** `latitude`/`longitude` returns number ([#1064](https://github.com/faker-js/faker/issues/1064)) ([dac6be3](https://github.com/faker-js/faker/commit/dac6be39fec509965e850ae1a47770c9224196f5))
* **location:** `nearbyGPSCoordinate` returns number tuple ([#1061](https://github.com/faker-js/faker/issues/1061)) ([4765336](https://github.com/faker-js/faker/commit/476533697141f0e88c9eca300d0b5040dd8a09d7))
* **number:** adjust hex default ([#1649](https://github.com/faker-js/faker/issues/1649)) ([7b50c2f](https://github.com/faker-js/faker/commit/7b50c2f0126ac865d4e4efea360b8ba71f0cb7c6))
* **number:** change float default params ([#1642](https://github.com/faker-js/faker/issues/1642)) ([671631b](https://github.com/faker-js/faker/commit/671631b0efa84ec4ff17827c7263a261de2d3fa0))
* **number:** change int default params ([#1643](https://github.com/faker-js/faker/issues/1643)) ([0b2fa1a](https://github.com/faker-js/faker/commit/0b2fa1ae96c6aabb7bcaae8d7f5f90df86d43786))
* **number:** move methods to new module ([#1122](https://github.com/faker-js/faker/issues/1122)) ([7d4d99f](https://github.com/faker-js/faker/commit/7d4d99f00bf1e29c14346bd6a9fab33c8e7d5743))
* **person:** add `zodiacSign()` ([#182](https://github.com/faker-js/faker/issues/182)) ([7e00d17](https://github.com/faker-js/faker/commit/7e00d1741495f763f986b3a5daf40943db4abc7d))
* **string:** add special() method ([#1634](https://github.com/faker-js/faker/issues/1634)) ([50fb72c](https://github.com/faker-js/faker/commit/50fb72ce3d7a911564ad5ff9f929ca5567a83757))
* **string:** move methods to new module ([#1155](https://github.com/faker-js/faker/issues/1155)) ([79858fe](https://github.com/faker-js/faker/commit/79858fea203bce7ada9e9bcc7751f6ab25123977))
* **word:** add sample method ([#714](https://github.com/faker-js/faker/issues/714)) ([3777c44](https://github.com/faker-js/faker/commit/3777c446e48a196ea4aae543c89a0944abf74d87))
* fake with multiple parameters ([#1459](https://github.com/faker-js/faker/issues/1459)) ([2eb2537](https://github.com/faker-js/faker/commit/2eb253732ab4ee62552e153aded4901ad03e23b7))


### Bug Fixes

* **company:** remove hardcoded name fallback pattern ([#1451](https://github.com/faker-js/faker/issues/1451)) ([b99ff71](https://github.com/faker-js/faker/commit/b99ff713306170e337e597714ffa1ac1ae17fc82))
* **date:** don't allow parameters <= 0 ([#1536](https://github.com/faker-js/faker/issues/1536)) ([dd58148](https://github.com/faker-js/faker/commit/dd58148a2d47de9942c3266a8cdfd8dead32c528))
* **date:** unify signatures ([#1534](https://github.com/faker-js/faker/issues/1534)) ([0cec571](https://github.com/faker-js/faker/commit/0cec57153d9e97b609714e8d6a96e2ccfcedfc11))
* **git:** adjust commitEntry to match `git log` output ([#1539](https://github.com/faker-js/faker/issues/1539)) ([99b6fb2](https://github.com/faker-js/faker/commit/99b6fb2c8d6d3e8ead53d7cc8d58bf615408860d))
* **helpers:** rewrite shuffle ([#1521](https://github.com/faker-js/faker/issues/1521)) ([a5de229](https://github.com/faker-js/faker/commit/a5de22926d1a5b6741216d2ba390880658d5f155))
* **image:** remove lorempixel ([#1641](https://github.com/faker-js/faker/issues/1641)) ([ab9fa1f](https://github.com/faker-js/faker/commit/ab9fa1fe5bab671c1888663151ae20374f409c4a))
* **image:** rewrite module ([#1477](https://github.com/faker-js/faker/issues/1477)) ([616b34d](https://github.com/faker-js/faker/commit/616b34def1781ad3c34b30f8f44fc9a8e7052a38))
* **internet:** userName, email and slugify return only ascii ([#1554](https://github.com/faker-js/faker/issues/1554)) ([4ed45fa](https://github.com/faker-js/faker/commit/4ed45fa33f80c59625a285d06abe31ce2f524357))
* **locale:** change country name from Holanda to official name ([#1669](https://github.com/faker-js/faker/issues/1669)) ([4ce378c](https://github.com/faker-js/faker/commit/4ce378ca4fde869caab29a8d688a816e4711e289))
* **locale:** change en_IND to en_IN ([#1448](https://github.com/faker-js/faker/issues/1448)) ([a7cd422](https://github.com/faker-js/faker/commit/a7cd422c6cbfe10f110e1fc53c88559198a97f08))
* **locale:** filter inappropriate words in the en locale ([#1633](https://github.com/faker-js/faker/issues/1633)) ([df881bd](https://github.com/faker-js/faker/commit/df881bd044dd62bb2b90e8bfaa58d24d95dfef3b))
* **locale:** improve en_GB location addresses ([#1620](https://github.com/faker-js/faker/issues/1620)) ([27d41ee](https://github.com/faker-js/faker/commit/27d41ee27f0a0ca1628d1da3785acd42557ba533))
* **locale:** mk (Macedonian) names should use cyrillic а ([#1557](https://github.com/faker-js/faker/issues/1557)) ([1796559](https://github.com/faker-js/faker/commit/1796559b16a17cbd2434a3274cf5bc760c0911f5))
* **location:** rename address module ([#1469](https://github.com/faker-js/faker/issues/1469)) ([b983ca1](https://github.com/faker-js/faker/commit/b983ca11922d3c6b07499adbe2089081a9a69083))
* **location:** use IANA standard for timezone ([#1678](https://github.com/faker-js/faker/issues/1678)) ([dd81fe1](https://github.com/faker-js/faker/commit/dd81fe1b22139b928dccca1fb359a8a69180a62e))
* **number:** values out of bounds ([#1648](https://github.com/faker-js/faker/issues/1648)) ([e4839a9](https://github.com/faker-js/faker/commit/e4839a9fc91d0ffc36c2015b34fcba33a6797bb4))
* **person:** Incorrect behavior of name.firstName() function ([#1610](https://github.com/faker-js/faker/issues/1610)) ([4ce8e98](https://github.com/faker-js/faker/commit/4ce8e98fcc19d99bf6df3abb3e24c4667f586076))
* **person:** rename name module ([#1445](https://github.com/faker-js/faker/issues/1445)) ([20f2236](https://github.com/faker-js/faker/commit/20f2236265467feb095cce5b5735bbadc07b9696))
* **string:** swap `allowLeadingZeros` default to `true` ([#1602](https://github.com/faker-js/faker/issues/1602)) ([c4b7ce8](https://github.com/faker-js/faker/commit/c4b7ce8648cbd5ac2b224942908bccf9914e08f9))
* ASCII-fy `domainWord()` ([#1520](https://github.com/faker-js/faker/issues/1520)) ([cf764b9](https://github.com/faker-js/faker/commit/cf764b925339d7917a641a8937735c35ed31c25a))
* broken `allowLeadingZeros` tests ([#1638](https://github.com/faker-js/faker/issues/1638)) ([97dbfa1](https://github.com/faker-js/faker/commit/97dbfa198644da98dc3f423acaaeb2976647e52b))
* cleanup deprecations ([#1440](https://github.com/faker-js/faker/issues/1440)) ([9c1437d](https://github.com/faker-js/faker/commit/9c1437d6034ef5537c079746761c4c71347f768b))
* first_name file in en_GH locale ([#1566](https://github.com/faker-js/faker/issues/1566)) ([dfd74f2](https://github.com/faker-js/faker/commit/dfd74f29311ef4d2a096b3751217897e86474a0f))
* make mersenne internal ([#1444](https://github.com/faker-js/faker/issues/1444)) ([5aa747f](https://github.com/faker-js/faker/commit/5aa747f7c0e6f4f67044d71139d2f2cf20256a32))
* upgrade node minimum target ([#1494](https://github.com/faker-js/faker/issues/1494)) ([226d421](https://github.com/faker-js/faker/commit/226d4215bd672a6e3f5c19975d0c303343ee4466))

## [7.6.0](https://github.com/faker-js/faker/compare/v7.5.0...v7.6.0) (2022-10-12)


### Features

* **finance:** branch code option in bic() ([#1378](https://github.com/faker-js/faker/issues/1378)) ([fe97c29](https://github.com/faker-js/faker/commit/fe97c29ef888b41582d4ad55753c7eb7e4bac6fc))
* **locale:** add first name for zh_CN ([#1376](https://github.com/faker-js/faker/issues/1376)) ([1d8ea82](https://github.com/faker-js/faker/commit/1d8ea82ec8ef7dacc14e747726299fea3e06f6cf))
* **locale:** add male first names starting with Z to id_ID ([#1424](https://github.com/faker-js/faker/issues/1424)) ([f921c14](https://github.com/faker-js/faker/commit/f921c14c5b60eed1742ad79809210597ecdcc98e))
* **locale:** add months, days and few other minor strings to es ([#1320](https://github.com/faker-js/faker/issues/1320)) ([ebb4e2c](https://github.com/faker-js/faker/commit/ebb4e2c37ab08396fcf2d757d1192bfd59e913fc))
* **locale:** add new Indonesia state ([#1400](https://github.com/faker-js/faker/issues/1400)) ([be87581](https://github.com/faker-js/faker/commit/be875818a8cdcf7eeab387b3d9fa232cdb5f7087))
* **locale:** extends id_ID company prefix ([#1399](https://github.com/faker-js/faker/issues/1399)) ([f410564](https://github.com/faker-js/faker/commit/f4105642bf32a8d13bafeaf3228138c984c98b2c))
* lorem null response fix ([#1407](https://github.com/faker-js/faker/issues/1407)) ([a6ce717](https://github.com/faker-js/faker/commit/a6ce71703b02f7c2c4f742106acff05d879c4384))
* **modules:** export module interfaces ([#932](https://github.com/faker-js/faker/issues/932)) ([b9884d0](https://github.com/faker-js/faker/commit/b9884d098d6e14001da36acfba6fbfebdcef8fea))
* **science:** add more units in en locale ([#1386](https://github.com/faker-js/faker/issues/1386)) ([8dcb584](https://github.com/faker-js/faker/commit/8dcb5841f0a6b0ba9eb1b1ebff123118647ab04b))


### Bug Fixes

* **finance:** adjust min length of generated bitcoin address ([#1384](https://github.com/faker-js/faker/issues/1384)) ([10aded2](https://github.com/faker-js/faker/commit/10aded248c5a7aaf57e1b82fad4fa15cfe8adce4))
* **finance:** fix typo in Belarusian Ruble's name ([#1395](https://github.com/faker-js/faker/issues/1395)) ([fe214c3](https://github.com/faker-js/faker/commit/fe214c3c2d4decf469bf3bd381c7f2dfb3fbb3cd))
* **finance:** Update Belarusian Ruble to new version ([#1392](https://github.com/faker-js/faker/issues/1392)) ([526906f](https://github.com/faker-js/faker/commit/526906ffaecd05c48264c30105593341b4da12be))
* **finance:** update Venezuelan bolívar name and code ([#1394](https://github.com/faker-js/faker/issues/1394)) ([0e65143](https://github.com/faker-js/faker/commit/0e65143a5f1c8a25b49dd0e661628a74d1e5a82f))
* **finance:** update Zambian Kwacha to new version ([#1393](https://github.com/faker-js/faker/issues/1393)) ([115e859](https://github.com/faker-js/faker/commit/115e859c0187aa17a5392892fe8b5bbe2ccdcc37))
* **image:** properly lock loremflickr.com images ([#1396](https://github.com/faker-js/faker/issues/1396)) ([2539e6a](https://github.com/faker-js/faker/commit/2539e6af93bd49860776afa57f11b29c2bc4d8f1))
* improve default seed initialization ([#1334](https://github.com/faker-js/faker/issues/1334)) ([925db3a](https://github.com/faker-js/faker/commit/925db3a0194fba6d5984203a69747265848227ef))
* **locale:** change id_ID name to `Indonesian` ([#1401](https://github.com/faker-js/faker/issues/1401)) ([ec0181e](https://github.com/faker-js/faker/commit/ec0181ec699e8687ecabda1d251b80f8d84b3a10))

## [7.5.0](https://github.com/faker-js/faker/compare/v7.4.0...v7.5.0) (2022-08-29)


### Features

* **company:** move name formats to locales ([#1293](https://github.com/faker-js/faker/issues/1293)) ([e1f26a6](https://github.com/faker-js/faker/commit/e1f26a6d2c79f3d9540f1890669631aca0fe5527))
* **datatype:** change hexadecimal signature ([#1238](https://github.com/faker-js/faker/issues/1238)) ([8cb6027](https://github.com/faker-js/faker/commit/8cb6027087fbc3fd038c4063b78f283d9aa48959))
* **locale:** add city names (de) ([#1283](https://github.com/faker-js/faker/issues/1283)) ([cc8b2b2](https://github.com/faker-js/faker/commit/cc8b2b2e1645c5b7cfe647ed64b498dbd277688a))
* **locale:** add directions and directions abbr to pl ([#1225](https://github.com/faker-js/faker/issues/1225)) ([7a718b9](https://github.com/faker-js/faker/commit/7a718b928dc255dc40791025e0b0c0bed6a4d2f9))
* **locale:** add horses to pl ([#1227](https://github.com/faker-js/faker/issues/1227)) ([ca7cb41](https://github.com/faker-js/faker/commit/ca7cb41c0580822da31cd6d4c537d4cd2dcbccb1))
* **locale:** extend Hebrew (he) ([#1257](https://github.com/faker-js/faker/issues/1257)) ([f8c9f60](https://github.com/faker-js/faker/commit/f8c9f60307823be517825ae60624f9bbe2ea5219))
* **name:** add sexType method ([#1289](https://github.com/faker-js/faker/issues/1289)) ([f684a14](https://github.com/faker-js/faker/commit/f684a14ddc3729c74f8434db68324269ae9a640f))
* **name:** extract sex generator from gender to sex ([#1168](https://github.com/faker-js/faker/issues/1168)) ([ad3c9bf](https://github.com/faker-js/faker/commit/ad3c9bf0caab9fac465694641d4d170fa103a243))
* **system:** add cron ([#897](https://github.com/faker-js/faker/issues/897)) ([8fecd58](https://github.com/faker-js/faker/commit/8fecd58b7cfd07826194e0de5d2c868c07c4d913))


### Bug Fixes

* **internal:** fix typo in deprecation message ([#1316](https://github.com/faker-js/faker/issues/1316)) ([7b18404](https://github.com/faker-js/faker/commit/7b184048f3d97ee75db6f5227776a801701d012d))
* **locale:** remove SUS names ([#1303](https://github.com/faker-js/faker/issues/1303)) ([f78843e](https://github.com/faker-js/faker/commit/f78843edb05913c44ebd86535b0d50d22e99fc5e))
* **random:** retry on invalid word generation ([#1307](https://github.com/faker-js/faker/issues/1307)) ([c2108fa](https://github.com/faker-js/faker/commit/c2108fa5db889bb1455a5735934776bcf91fabac))

## [7.4.0](https://github.com/faker-js/faker/compare/v7.3.0...v7.4.0) (2022-08-08)


### Features

* add rodent breed on animal 'en' locale ([#1136](https://github.com/faker-js/faker/issues/1136)) ([a65f1a2](https://github.com/faker-js/faker/commit/a65f1a2b59b909c4b5cc044af560ff91642c9a56))
* **dev:** add male_first_name,female_first_name, jp locale ([#1217](https://github.com/faker-js/faker/issues/1217)) ([ad885ea](https://github.com/faker-js/faker/commit/ad885eaffd4d95031df1b3a09cead7c18ccd2c5b))
* **image:** add image via.placeholder provider ([#1186](https://github.com/faker-js/faker/issues/1186)) ([00d4741](https://github.com/faker-js/faker/commit/00d4741fb8cde9c2790241654ba375fa6afa4f81))
* **locales.en:** extend vehicle ([#1102](https://github.com/faker-js/faker/issues/1102)) ([07a970f](https://github.com/faker-js/faker/commit/07a970f2e05ef4e04a9d7382ab75fa544fb986d6))
* **locales.pl:** add pl translation for gender and binary gender ([#1162](https://github.com/faker-js/faker/issues/1162)) ([22a050e](https://github.com/faker-js/faker/commit/22a050e86fd4fb04e4232a9e57fb1602fd3944cc))
* **system.fileName:** file extension count ([#1101](https://github.com/faker-js/faker/issues/1101)) ([968134c](https://github.com/faker-js/faker/commit/968134c398a11b698b489a492179080aa7ca8c73))
* **system.networkInterface:** add networkInterface faker ([#1133](https://github.com/faker-js/faker/issues/1133)) ([5979f82](https://github.com/faker-js/faker/commit/5979f82e17d4f9adf80fa795afb668d57b33411f))


### Bug Fixes

* **finance.bic:** remove hardcoded elements and simplify function ([#1171](https://github.com/faker-js/faker/issues/1171)) ([5a397e0](https://github.com/faker-js/faker/commit/5a397e0f8a6b4651a5b093b8eafe4895bf166845))

## [7.3.0](https://github.com/faker-js/faker/compare/v7.2.0...v7.3.0) (2022-06-20)

### New & Noteworthy

* Webpack v4 should now be supported 🚀


### Features

* add abbreviations to `hacker.abbreviation()` in the `en` locale ([#1086](https://github.com/faker-js/faker/issues/1086)) ([c5d8934](https://github.com/faker-js/faker/commit/c5d893401be7b9cdf3e54930df0681ea9f43be06))
* improve norwegian translations ([#1042](https://github.com/faker-js/faker/issues/1042)) ([e071c78](https://github.com/faker-js/faker/commit/e071c78df7a40813228aeed0ad54f10471289b3f))
* throw error on unknown locale ([#1071](https://github.com/faker-js/faker/issues/1071)) ([5ea8252](https://github.com/faker-js/faker/commit/5ea8252f727e2e577c2adca9650ac8f24a171632))


### Bug Fixes

* lower target to support Webpack 4 ([#1085](https://github.com/faker-js/faker/issues/1085)) ([080e51d](https://github.com/faker-js/faker/commit/080e51d19afc5c426b804f6afbd44333bbd0d1c9))

## [7.2.0](https://github.com/faker-js/faker/compare/v7.0.1...v7.2.0) (2022-06-06)


### ⚠ SOFT BREAKING CHANGES

* always use https for loremflickr (#1034)
  * we removed the last parameter for `image.imageUrl` and always generate https urls


### Features

* add en_CA city_name (close [#983](https://github.com/faker-js/faker/issues/983)) ([#992](https://github.com/faker-js/faker/issues/992)) ([dc5c720](https://github.com/faker-js/faker/commit/dc5c72070c2dd32e9b1cb32368646fa6ae1bed0c))
* add german adjectives ([#1023](https://github.com/faker-js/faker/issues/1023)) ([1b9a920](https://github.com/faker-js/faker/commit/1b9a920b36133a1b1c6313f1fb086fb697120076))
* always use https for loremflickr ([#1034](https://github.com/faker-js/faker/issues/1034)) ([a235dca](https://github.com/faker-js/faker/commit/a235dcac30064e1357c4b0dc2481b2dcfa04a365))
* science module ([#1014](https://github.com/faker-js/faker/issues/1014)) ([d75d079](https://github.com/faker-js/faker/commit/d75d07970b44bde066de0a765c169809ee8f6b74))


### Bug Fixes

* 🇫🇷 fr and 🇨🇭 fr_CH 🎨 colors ([#1007](https://github.com/faker-js/faker/issues/1007)) ([d96457e](https://github.com/faker-js/faker/commit/d96457ed99fb124dd4fceb16a8b418be2a4f47ed))

## [7.1.0](https://github.com/faker-js/faker/compare/v7.0.1...v7.1.0) (2022-05-25)


### Features

* add en_CA city_name (close [#983](https://github.com/faker-js/faker/issues/983)) ([#992](https://github.com/faker-js/faker/issues/992)) ([dc5c720](https://github.com/faker-js/faker/commit/dc5c72070c2dd32e9b1cb32368646fa6ae1bed0c))
* add music.songName ([#996](https://github.com/faker-js/faker/issues/996)) ([ccd2959](https://github.com/faker-js/faker/commit/ccd2959d72e70b74c7faf755bb690da35e3c48a6))


### Bug Fixes

* Luhn generation algorithms and tests ([#980](https://github.com/faker-js/faker/issues/980)) ([c95826f](https://github.com/faker-js/faker/commit/c95826f348bf317d3cff240a7ebbae4bd80956f6))
* **typescript:** support module Node16/NodeNext ([#1005](https://github.com/faker-js/faker/issues/1005)) ([8736c2a](https://github.com/faker-js/faker/commit/8736c2a0e5370d40e006abfa65c5ce1057c31121))

## [7.0.1](https://github.com/faker-js/faker/compare/v7.0.0...v7.0.1) (2022-05-23)


### Bug Fixes

* revert type exports for module NodeNext, Node16 ([#979](https://github.com/faker-js/faker/issues/979)) ([#989](https://github.com/faker-js/faker/issues/989)) ([c704e36](https://github.com/faker-js/faker/commit/c704e36f692e69bbe23bdf05f4fdcccba4ce8b0f))

## [7.0.0](https://github.com/faker-js/faker/compare/v6.3.1...v7.0.0) (2022-05-23)


### ⚠ BREAKING CHANGES

* remove faker default export (#799)
* target es2020 (#848)
* remove deprecations (#916)
* get rid of export = (#849)
* remove node v12 support (#850)
* reorganize src folder (#909)

### Features

* add casing option ([#955](https://github.com/faker-js/faker/issues/955)) ([4c0e418](https://github.com/faker-js/faker/commit/4c0e41831f8d2fad92f85cea647cbd0873fd842e))
* add date.birthdate ([#962](https://github.com/faker-js/faker/issues/962)) ([5e66d96](https://github.com/faker-js/faker/commit/5e66d9699ba92b42c835662f43d53cba0aadbffb))
* added sponsor and community members to the readme ([#986](https://github.com/faker-js/faker/issues/986)) ([2a6003f](https://github.com/faker-js/faker/commit/2a6003f5fe15b79d361ed4f0d7b3c2b8ec98bfab))
* **address:** use localized fake pattern in city ([#948](https://github.com/faker-js/faker/issues/948)) ([7373a22](https://github.com/faker-js/faker/commit/7373a22f33f38d29ff53e4f4588f0137a35132b8))
* allow banned as string ([#819](https://github.com/faker-js/faker/issues/819)) ([a0d25bb](https://github.com/faker-js/faker/commit/a0d25bbec84c710a6dc8d2cf438af351cf486ab0))
* color module ([#801](https://github.com/faker-js/faker/issues/801)) ([bee6054](https://github.com/faker-js/faker/commit/bee6054f8da67e26dcfdf572103eebabbd6443c0))
* improve and extend 🇫🇷 fr and 🇨🇭fr_CH locales ([#973](https://github.com/faker-js/faker/issues/973)) ([6f39b7c](https://github.com/faker-js/faker/commit/6f39b7cb93baf05863497381a117ad5185948b1e))
* **internet:** HTTP random status code ([#945](https://github.com/faker-js/faker/issues/945)) ([05f555b](https://github.com/faker-js/faker/commit/05f555bc7e304afaa657586ae88f2173507e084f))
* reimplement datatype.bigInt ([#791](https://github.com/faker-js/faker/issues/791)) ([1793385](https://github.com/faker-js/faker/commit/1793385c1ea7b7db349720c7bab20ac9765e9200))
* support locale definitions directly from faker.fake ([#884](https://github.com/faker-js/faker/issues/884)) ([a60d5e3](https://github.com/faker-js/faker/commit/a60d5e3ea3d1109b90cbb51d8a4a10aba2290ada))
* use localized fake pattern in street ([#966](https://github.com/faker-js/faker/issues/966)) ([5af79f4](https://github.com/faker-js/faker/commit/5af79f487bd1537676d017ae09563e53d18458c4))


### Bug Fixes

* empty array passed into helpers.arrayElements ([#921](https://github.com/faker-js/faker/issues/921)) ([100a1ea](https://github.com/faker-js/faker/commit/100a1ea230cba422174a6b5103c56bc4cf9c0dc4))
* **generate:locale:** make the definition types extendible ([#915](https://github.com/faker-js/faker/issues/915)) ([984fbb4](https://github.com/faker-js/faker/commit/984fbb445ff3be3658535bf98916ce5f38943fbf))
* type exports for module NodeNext, Node16 ([#979](https://github.com/faker-js/faker/issues/979)) ([73db3a7](https://github.com/faker-js/faker/commit/73db3a77d95a21e320888228e39ebbf60d551451))
* typo in error message ([#925](https://github.com/faker-js/faker/issues/925)) ([3ea64ce](https://github.com/faker-js/faker/commit/3ea64ceeecfe4cac2f2e6708f12a4bee288d1cb6))


### build

* remove node v12 support ([#850](https://github.com/faker-js/faker/issues/850)) ([cc9aec7](https://github.com/faker-js/faker/commit/cc9aec71eb35e5be4949de9fd6f62f1b8ee48db0))
* get rid of export = ([#849](https://github.com/faker-js/faker/issues/849)) ([20fbeaf](https://github.com/faker-js/faker/commit/20fbeaf35d7c82cdf39da93097927d3a3d81c41c))
* remove deprecations ([#916](https://github.com/faker-js/faker/issues/916)) ([683ee34](https://github.com/faker-js/faker/commit/683ee3405c39408d74d74cac0755a26de7a99e35))
* remove faker default export ([#799](https://github.com/faker-js/faker/issues/799)) ([39b74c0](https://github.com/faker-js/faker/commit/39b74c0326da2d96fa48837a9ad9b995b7158fbd))
* reorganize src folder ([#909](https://github.com/faker-js/faker/issues/909)) ([a2da7c4](https://github.com/faker-js/faker/commit/a2da7c496e9a3741d165ddfe6128b50837fec361))
* target es2020 ([#848](https://github.com/faker-js/faker/issues/848)) ([63f6361](https://github.com/faker-js/faker/commit/63f63612fab40c3def72d9ed50d0ac042a078677))

## [6.3.1](https://github.com/faker-js/faker/compare/v6.3.0...v6.3.1) (2022-05-02)


### Bug Fixes

* replace deprecated arrayElement calls ([#903](https://github.com/faker-js/faker/issues/903)) ([42d6795](https://github.com/faker-js/faker/commit/42d679566624aaedd01eb5c0d9fa54104008016c))

## [6.3.0](https://github.com/faker-js/faker/compare/v6.2.0...v6.3.0) (2022-05-02)


### Features

* add creditCardIssuer ([#888](https://github.com/faker-js/faker/issues/888)) ([58b4f10](https://github.com/faker-js/faker/commit/58b4f10ad11fe42a736dd84f6f61f3c84c743ed8))
* add Hungarian locale ([#53](https://github.com/faker-js/faker/issues/53)) ([9b0d100](https://github.com/faker-js/faker/commit/9b0d1009134d0f0364b0a4851e03fd5e0af145f9))
* configure eol character for git.commitEntry ([#681](https://github.com/faker-js/faker/issues/681)) ([f797b63](https://github.com/faker-js/faker/commit/f797b6310ea73c8ab5637ed415faab221115ea30))
* faker.helpers.maybe ([#874](https://github.com/faker-js/faker/issues/874)) ([a64cbde](https://github.com/faker-js/faker/commit/a64cbde7c9cb05d97617708710c572b23a382f6d))
* random numeric ([#797](https://github.com/faker-js/faker/issues/797)) ([712b1de](https://github.com/faker-js/faker/commit/712b1de126ea6580660a320e065c35ac775f09b3))
* resettable unique store ([#800](https://github.com/faker-js/faker/issues/800)) ([29bba7b](https://github.com/faker-js/faker/commit/29bba7be530d2e11c56de021fc67a9641b2e6e0d))
* return seed value from seed() ([#853](https://github.com/faker-js/faker/issues/853)) ([1851eca](https://github.com/faker-js/faker/commit/1851ecab1e33b6266bb4b4614c814a7674099d01))
* separate methods for object key value ([#503](https://github.com/faker-js/faker/issues/503)) ([36cd461](https://github.com/faker-js/faker/commit/****************************************))


### Bug Fixes

* address.nearbyGPSCoordinate ([#876](https://github.com/faker-js/faker/issues/876)) ([3e23fc4](https://github.com/faker-js/faker/commit/3e23fc48820319c3e6b8d0581c70d836401f88ba))
* dont log deprecations on startup ([#857](https://github.com/faker-js/faker/issues/857)) ([a28b5de](https://github.com/faker-js/faker/commit/a28b5deab9079c567b7eb8a1917c661cadd35849))

## [6.2.0](https://github.com/faker-js/faker/compare/v6.1.2...v6.2.0) (2022-04-21)


### Features

* add chinese hyphenated name ([#277](https://github.com/faker-js/faker/issues/277)) ([40c9d5a](https://github.com/faker-js/faker/commit/40c9d5a8d0a03d8cb9275e40640b6d193ce1780c))
* add internet.emoji ([#504](https://github.com/faker-js/faker/issues/504)) ([cb746cb](https://github.com/faker-js/faker/commit/cb746cb466743a219c0e3845edb29527a06b0a35))
* add Lastname - Bhadresha, Jangid, Suthar ([#243](https://github.com/faker-js/faker/issues/243)) ([6214425](https://github.com/faker-js/faker/commit/6214425d92a2d34268283bace19a02d6f1d9924e))
* Add MongoDB ObjectId generation ([#616](https://github.com/faker-js/faker/issues/616)) ([a5b3888](https://github.com/faker-js/faker/commit/a5b38885f7b8c420b3587d8ded7fc5d180c92ed7))
* add more arabic names dataset ([#655](https://github.com/faker-js/faker/issues/655)) ([b3a9abc](https://github.com/faker-js/faker/commit/b3a9abcf487372f53eece207099f3f645f9b9c8f))
* add more arabic names datasets ([#368](https://github.com/faker-js/faker/issues/368)) ([f824f9d](https://github.com/faker-js/faker/commit/f824f9d231b0e48f98f84b8cac71e79566f4f5e7))
* add os. as street prefix for pl ([#640](https://github.com/faker-js/faker/issues/640)) ([c6d0cc7](https://github.com/faker-js/faker/commit/c6d0cc7ae4f8ea220803bcee5a5e84a7b8230951))
* add some en locale city prefixes and suffixes ([#685](https://github.com/faker-js/faker/issues/685)) ([955ea43](https://github.com/faker-js/faker/commit/955ea43c2b885feb276a670babe859cb3ecc87d2))
* add some en locale product names ([#686](https://github.com/faker-js/faker/issues/686)) ([f22b93a](https://github.com/faker-js/faker/commit/f22b93a0d6fc08e122b67c2a7dd010ee81d67be4))
* append more korean dataset and delete wrong dataset ([#573](https://github.com/faker-js/faker/issues/573)) ([acaedb3](https://github.com/faker-js/faker/commit/acaedb30d2ff47c8597d08db5bf03f487bfcb153))
* dynamic definitions tree ([#822](https://github.com/faker-js/faker/issues/822)) ([069f4d1](https://github.com/faker-js/faker/commit/069f4d1c08b2a314aa24a4a55b7498ff97be7c3a))
* extend list of domain suffix for PL locale ([#266](https://github.com/faker-js/faker/issues/266)) ([5beac4b](https://github.com/faker-js/faker/commit/5beac4be3ebcd2bc6096f6ed2988353bb592aa26))
* extend list of street prefixes for pl locale ([#844](https://github.com/faker-js/faker/issues/844)) ([425accd](https://github.com/faker-js/faker/commit/425accdeac2973a4111ea419af31a52b522b1918))
* faker.finance.pin() ([#695](https://github.com/faker-js/faker/issues/695)) ([20f33e6](https://github.com/faker-js/faker/commit/20f33e6640551b1d95059207ae2a54ba9115690c))
* immutable options in random.alpha methods ([#790](https://github.com/faker-js/faker/issues/790)) ([dd11846](https://github.com/faker-js/faker/commit/dd11846bd9c61cc09917a06ec231592fff3ec653))
* phone IMEI ([#829](https://github.com/faker-js/faker/issues/829)) ([c25ecd0](https://github.com/faker-js/faker/commit/c25ecd08ec57fae5967148bee14fec1c4be99472))
* special characters in emails ([#792](https://github.com/faker-js/faker/issues/792)) ([3b5a21f](https://github.com/faker-js/faker/commit/3b5a21f3aae52f263f2c91e763fcee613092166c))
* updated mime-db to 1.52.0 ([#808](https://github.com/faker-js/faker/issues/808)) ([78a30fb](https://github.com/faker-js/faker/commit/78a30fbdb8779a0e4b242d353a696672f64a1fcc))
* use street address templates from locales ([#754](https://github.com/faker-js/faker/issues/754)) ([b04dc91](https://github.com/faker-js/faker/commit/b04dc914eec44ec3c5ae760976b616ce1d2584e1))


### Bug Fixes

* Maryland (MD) wrong postal code ([#804](https://github.com/faker-js/faker/issues/804)) ([4ac2a04](https://github.com/faker-js/faker/commit/4ac2a0424fffcf48dedea58148ac1b4b510a68a5))
* use `\n` as default separator for lorem.paragraphs() ([#783](https://github.com/faker-js/faker/issues/783)) ([6a2d8fc](https://github.com/faker-js/faker/commit/6a2d8fce89e0b46f7b9693a7b1f697c108e8af04))

## [6.1.2](https://github.com/faker-js/faker/compare/v6.1.1...v6.1.2) (2022-04-04)


### Features

* FakerError ([#718](https://github.com/faker-js/faker/issues/718)) ([c3be3b1](https://github.com/faker-js/faker/commit/c3be3b1945248ed422342b046ad765d252bbac05))


### Bug Fixes

* datatype.number when min = max + precision, throw when max > min ([#664](https://github.com/faker-js/faker/issues/664)) ([0304120](https://github.com/faker-js/faker/commit/03041201c21ad599bbe1874c375f4f41b94961ba))
* deterministic results for address.nearbyGPSCoordinate ([#737](https://github.com/faker-js/faker/issues/737)) ([bc7bd57](https://github.com/faker-js/faker/commit/bc7bd571d8d6c70b046a3bda23c61c527ddb1d4a))
* random word fails on undefined ([#771](https://github.com/faker-js/faker/issues/771)) ([fb1b87e](https://github.com/faker-js/faker/commit/fb1b87e2249798c6257cb5383f73a15022f438f0))
* remove doubled extension in system.filePath ([#300](https://github.com/faker-js/faker/issues/300)) ([2532eb9](https://github.com/faker-js/faker/commit/********************6ec91952f838d98fd72a))

## [6.1.1](https://github.com/faker-js/faker/compare/v6.1.0...v6.1.1) (2022-03-28)

### Bug Fixes

* forgot to run `pnpm run build`

## [6.1.0](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.1.0) (2022-03-28)

⚠**WARNING**: This build is broken due to missing build files. Please don't use this version!

### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* accept dates as params for Date methods ([#200](https://github.com/faker-js/faker/issues/200)) ([91a1aab](https://github.com/faker-js/faker/commit/91a1aaba954b7d172c3dd3346567078644b74189))
* alphaNumeric all chars banned ([#550](https://github.com/faker-js/faker/issues/550)) ([c51fb15](https://github.com/faker-js/faker/commit/c51fb1570669284e14915916636de97b7f644e17))
* corrected the Costa Rican IBAN format ([#646](https://github.com/faker-js/faker/issues/646)) ([3f3de78](https://github.com/faker-js/faker/commit/3f3de78c83ae919fd44531ac7ae9caed885800d4))
* correcting the readme file ([#529](https://github.com/faker-js/faker/issues/529)) ([6c9dcdd](https://github.com/faker-js/faker/commit/6c9dcdd03050ef70554fc2bb6ccc0c883fd1edc9))
* datatype.datetime should use static boundaries ([#343](https://github.com/faker-js/faker/issues/343)) ([7141cd7](https://github.com/faker-js/faker/commit/7141cd7d8a2fd505d3338d13ca29fd1ab7a5dc80))
* enable algolia search ([#641](https://github.com/faker-js/faker/issues/641)) ([a759c87](https://github.com/faker-js/faker/commit/a759c8707c2e3758e64e1c174c2de7aad51cd64b))
* fake behavior with special replacement patterns ([#688](https://github.com/faker-js/faker/issues/688)) ([8d1aefb](https://github.com/faker-js/faker/commit/8d1aefbda070265909cedb07af564ea143be74a7))
* fake is unable to return empty strings ([#347](https://github.com/faker-js/faker/issues/347)) ([301a6d2](https://github.com/faker-js/faker/commit/301a6d2024816bf40f1091ccffe6bb81cb7ba7b0))
* **finance:** update iso-3166-1 country codes for IBAN/BIC ([#168](https://github.com/faker-js/faker/issues/168)) ([6050d7a](https://github.com/faker-js/faker/commit/6050d7acbb991568cbdac7b0c16a088aef200abc))
* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix time.recent method signature ([#586](https://github.com/faker-js/faker/issues/586)) ([60d3cc5](https://github.com/faker-js/faker/commit/60d3cc519cd2cab061a23fbe09164d8b59e177ff))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* force passed locales into faker constructor ([#580](https://github.com/faker-js/faker/issues/580)) ([5ed963f](https://github.com/faker-js/faker/commit/5ed963f1e4928eb2df36f230faf7f9d63b51bef1))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* mersenne rand invalid input argument ([#577](https://github.com/faker-js/faker/issues/577)) ([486c76e](https://github.com/faker-js/faker/commit/486c76e34f22cf1fd66fa2c99e605d52c7077760))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* name module gender ([#644](https://github.com/faker-js/faker/issues/644)) ([7675abe](https://github.com/faker-js/faker/commit/7675abe5a62c8d44ecfd4a6f50bfb5fa50f40efa))
* normalize provider in finance.creditCardNumber ([#662](https://github.com/faker-js/faker/issues/662)) ([9ce1551](https://github.com/faker-js/faker/commit/9ce1551ab7c9cafc97e3d051f0632591e34eb3ce))
* only return word with desirable alpha characters ([#654](https://github.com/faker-js/faker/issues/654)) ([b7b2e4f](https://github.com/faker-js/faker/commit/b7b2e4f8dbd40b6bb2678fb60ee95e198838d08a))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* remove unreachable code finance ([#508](https://github.com/faker-js/faker/issues/508)) ([1bc622a](https://github.com/faker-js/faker/commit/1bc622a9499bf236052809610ecabe80f87de482))
* replaced placeimg.com on loremflickr.com ([#692](https://github.com/faker-js/faker/issues/692)) ([2a49e82](https://github.com/faker-js/faker/commit/2a49e8215a81ef284a2955f7f9a51d6f2bd76f5f))
* solve various todos ([#649](https://github.com/faker-js/faker/issues/649)) ([d0a473f](https://github.com/faker-js/faker/commit/d0a473f7ca6b525391ca8ab17e029335c66ed99e))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))
* use require/import export map in package.json ([#697](https://github.com/faker-js/faker/issues/697)) ([0f74908](https://github.com/faker-js/faker/commit/0f74908a5ac16d4a9b73d747a275a9399a131757))
* Vehicle vin is always 17 characters long ([#320](https://github.com/faker-js/faker/issues/320)) ([d2fc1e6](https://github.com/faker-js/faker/commit/d2fc1e6b5ba55242d16b9b8a1e9f42c7b24957b0))

## [6.0.0](https://github.com/faker-js/faker/compare/v6.0.0-beta.0...v6.0.0) (2022-03-15)


### Bug Fixes

* fix some lint warnings ([#613](https://github.com/faker-js/faker/issues/613)) ([5cb74b1b](https://github.com/faker-js/faker/commit/5cb74b1bf31f44311b4ee54ea320b81f68879f07))
* **docs:** add image_providers jsdocs ([#612](https://github.com/faker-js/faker/issues/612)) ([09487b6b](https://github.com/faker-js/faker/commit/09487b6b3a6e6cc3de0303851b9913ecdf1390dc))
* **docs:** improve visualization for complex types ([#608](https://github.com/faker-js/faker/issues/608)) ([f038937c](https://github.com/faker-js/faker/commit/f038937c973a0ad4b0014c5aaa56a0323c94fff4))
* configure eqeqeq lint rule ([#595](https://github.com/faker-js/faker/issues/595)) ([5cd3daef](https://github.com/faker-js/faker/commit/5cd3daef2b586b7f7c89b82259831ca4810d77d5))
* fix eslint error ([#597](https://github.com/faker-js/faker/issues/597)) ([4f7447c3](https://github.com/faker-js/faker/commit/4f7447c3e38da71d261da254dd631b84fb4c22c4))
* **docs:** comment out algolia until search is available ([#599](https://github.com/faker-js/faker/issues/599)) ([bcc97d69](https://github.com/faker-js/faker/commit/bcc97d69ba434b436d4b173dfba8122a417e8345))
* **docs:** add test for api docs generation ([#574](https://github.com/faker-js/faker/issues/574)) ([c99160f0](https://github.com/faker-js/faker/commit/c99160f0ab059729af29d7ee08bd97c38d323b2a))
* **docs:** display correct signature ([#596](https://github.com/faker-js/faker/issues/596)) ([c115056e](https://github.com/faker-js/faker/commit/c115056e04d1e42f97c8d77daed3d9056c375953))
* **docs:** show union options parameters ([#602](https://github.com/faker-js/faker/issues/602)) ([5595b9fc](https://github.com/faker-js/faker/commit/5595b9fc7bf700cb0ca3d0792a3a879110ba43c5))

## [6.0.0-beta.0](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.0.0-beta.0) (2022-03-07)


### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* correcting the readme file ([#529](https://github.com/faker-js/faker/issues/529)) ([6c9dcdd](https://github.com/faker-js/faker/commit/6c9dcdd03050ef70554fc2bb6ccc0c883fd1edc9))
* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix time.recent method signature ([#586](https://github.com/faker-js/faker/issues/586)) ([60d3cc5](https://github.com/faker-js/faker/commit/60d3cc519cd2cab061a23fbe09164d8b59e177ff))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))

## [6.0.0-alpha.7](https://github.com/faker-js/faker/compare/v6.0.0-alpha.6...v6.0.0-alpha.7) (2022-02-20)


### Features

* **types:** generate all locales index files with non-any types ([#494](https://github.com/faker-js/faker/issues/494)) ([b611ca1](https://github.com/faker-js/faker/commit/b611ca1c6f7d756f224a1a7b641e36b531d169be))
* **types:** provide strong typing for locales 2 ([#398](https://github.com/faker-js/faker/issues/398)) ([419e9e2](https://github.com/faker-js/faker/commit/419e9e20a570b44909faf24c9019bf7f232ee7a9))


### Bug Fixes

* fix bicycle_types files being named incorrectly ([#477](https://github.com/faker-js/faker/issues/477)) ([7a2a522](https://github.com/faker-js/faker/commit/7a2a522c181dc696f6e9b4767ca18d39bad4476d))
* fix unique method types ([#457](https://github.com/faker-js/faker/issues/457)) ([14df7d3](https://github.com/faker-js/faker/commit/14df7d3f70b54c4a153f5dcf111ef90575bbbe9e))
* handle missing or broken locales main index files ([#478](https://github.com/faker-js/faker/issues/478)) ([ff97431](https://github.com/faker-js/faker/commit/ff974319a63acbcbbb96cf1a67a48616de9fd9da))
* move el credit card information to the expected location ([#484](https://github.com/faker-js/faker/issues/484)) ([dcbd18e](https://github.com/faker-js/faker/commit/dcbd18e13ab53e837de17cec7b3f8e51ba479d0e))
* remove invalid aliases ([#488](https://github.com/faker-js/faker/issues/488)) ([58fbfe3](https://github.com/faker-js/faker/commit/58fbfe3835b0f27949e7eb262d246eb9d84bb870))
* test random.alphaNumeric ([#517](https://github.com/faker-js/faker/issues/517)) ([41ec6f0](https://github.com/faker-js/faker/commit/41ec6f08784afc2a674568328a0ea2bfb033a411))
* **build:** add redirect aliases for cjs ([#520](https://github.com/faker-js/faker/issues/520)) ([7d806d9](https://github.com/faker-js/faker/commit/7d806d9a0b9914f9e0ce0c11e529bdd8ae116fc4))
* **docs:** various fixes ([#523](https://github.com/faker-js/faker/issues/523)) ([b22cf3b](https://github.com/faker-js/faker/commit/b22cf3b809efab92f5933b9f6641e1959a4c8fcc))

## [6.0.0-alpha.6](https://github.com/faker-js/faker/compare/v6.0.0-alpha.5...v6.0.0-alpha.6) (2022-02-10)


### Features

* add chat.fakerjs.dev subdomain redirection to discord ([#306](https://github.com/faker-js/faker/issues/306)) ([32b4839](https://github.com/faker-js/faker/commit/32b483920450a5c1a084e83723f1c433db8ff34f))
* auto generate/update locale files ([#252](https://github.com/faker-js/faker/issues/252)) ([df48704](https://github.com/faker-js/faker/commit/df48704c818a1addb8c32140f12e592e243f4e6d))
* **types:** provide strong typing for locales ([#363](https://github.com/faker-js/faker/issues/363)) ([5e6754d](https://github.com/faker-js/faker/commit/5e6754da61b63019fd063fad26adbeeabd8b789b))


### Bug Fixes

* improve internet tests and fix bug in domain name generation ([#258](https://github.com/faker-js/faker/issues/258)) ([c6f7aa9](https://github.com/faker-js/faker/commit/c6f7aa9de0259b45a62bf59336c3ff037a40651d))
* optional args on faker.finance.iban() ([#431](https://github.com/faker-js/faker/issues/431)) ([c71469c](https://github.com/faker-js/faker/commit/c71469c8e0bca9ccf4a8f66be1294f5139f9631f))
* param can be optional ([#381](https://github.com/faker-js/faker/issues/381)) ([f8a95a1](https://github.com/faker-js/faker/commit/f8a95a1b2d66d188b5f5ef622de7e2c7c27743bc))
* remove redundant precision from datatype.datetime options ([#335](https://github.com/faker-js/faker/issues/335)) ([9d5a7a2](https://github.com/faker-js/faker/commit/9d5a7a2533c569fa1cad2dacb6ae223644bb98cb))
* removed dead code and adjusted fileExt ([#304](https://github.com/faker-js/faker/issues/304)) ([471bc1d](https://github.com/faker-js/faker/commit/471bc1d5d797b5eee5de6c980505d0d8db158776))
* removed node v12 from the pipe ([#309](https://github.com/faker-js/faker/issues/309)) ([537f56e](https://github.com/faker-js/faker/commit/537f56e129512b38f62f802a8099dc40f5cfa66f))
* some return types ([#307](https://github.com/faker-js/faker/issues/307)) ([4ca61ca](https://github.com/faker-js/faker/commit/4ca61ca5b73c3cd4a0ba4094aabd492e2b7f74bf))

## [6.0.0-alpha.5](https://github.com/faker-js/faker/compare/v6.0.0-alpha.4...v6.0.0-alpha.5) (2022-01-25)


### Bug Fixes

* revert pnpm only-allow ([#291](https://github.com/faker-js/faker/issues/291)) ([132e8c3](https://github.com/faker-js/faker/commit/132e8c3d7c173a6c9708f865e8c7d6c31f9c6e55))

## [6.0.0-alpha.4](https://github.com/faker-js/faker/compare/v6.0.0-alpha.3...v6.0.0-alpha.4) (2022-01-24)


### ⚠ BREAKING CHANGES

* support tree-shaking (#152)

### Features

* add autocomplete support for locales ([#248](https://github.com/faker-js/faker/issues/248)) ([c1f2b09](https://github.com/faker-js/faker/commit/c1f2b09be957760a133baaab9d58364bda57be0d))
* migrate locales to typescript ([#238](https://github.com/faker-js/faker/issues/238)) ([d4cfa3c](https://github.com/faker-js/faker/commit/d4cfa3cbbc1c887a17dddbdd5ffc64140e8fa605))
* support tree-shaking ([#152](https://github.com/faker-js/faker/issues/152)) ([ccf119d](https://github.com/faker-js/faker/commit/ccf119deeb6c6d4c942544d83b515cddc5681ba3))
* use export default for locales ([#249](https://github.com/faker-js/faker/issues/249)) ([93e8e53](https://github.com/faker-js/faker/commit/93e8e535ba5bf85fae029078941523ce566fb356))


### Bug Fixes

* add missing fakers for existing locales ([#263](https://github.com/faker-js/faker/issues/263)) ([7177d37](https://github.com/faker-js/faker/commit/7177d375ec1d29d5bcab7525ef90a3eed1a4fa84))
* jalapeño typo ([#259](https://github.com/faker-js/faker/issues/259)) ([74204a0](https://github.com/faker-js/faker/commit/74204a0ded997b08a988fe20481a715081b7daf6))
* ts support for locales ([#169](https://github.com/faker-js/faker/issues/169)) ([61d0296](https://github.com/faker-js/faker/commit/61d02960a9132fa2c4d4d7259883b2e944233bb9))
* **types:** fixed datetime types for optional argument ([#192](https://github.com/faker-js/faker/issues/192)) ([60b71fd](https://github.com/faker-js/faker/commit/60b71fd257c2b95a6997f5aac516b88327a78f4b))
* **types:** fixed exampleEmail arguments types ([#241](https://github.com/faker-js/faker/issues/241)) ([66aa374](https://github.com/faker-js/faker/commit/66aa374fc3ca958070819d7eb6b119accc01cd3d))
* typo in update.md ([#180](https://github.com/faker-js/faker/issues/180)) ([4a69d6d](https://github.com/faker-js/faker/commit/4a69d6dc295153e037c15e084e55aef1e662096b))

## [6.0.0-alpha.3](https://github.com/faker-js/faker/compare/v6.0.0-alpha.2...v6.0.0-alpha.3) (2022-01-15)


### Features

* add definitions ([#84](https://github.com/faker-js/faker/issues/84)) ([cfdfad5](https://github.com/faker-js/faker/commit/cfdfad5d77b9b52a07c02d2fb3ca4588acdb7d5d))
* **domain_suffix:** add the `id`, `ponpes.id` and `net.id` domains for id_ID ([#137](https://github.com/faker-js/faker/issues/137)) ([ce4d48e](https://github.com/faker-js/faker/commit/ce4d48eb300e9e5e87ad9d7dcf7522e3147c98fb))
* migrate address ([#133](https://github.com/faker-js/faker/issues/133)) ([88afa60](https://github.com/faker-js/faker/commit/88afa60f739a60f08efe7365a3b80dbfd306055a))
* migrate animal ([#105](https://github.com/faker-js/faker/issues/105)) ([0abec83](https://github.com/faker-js/faker/commit/0abec83219a3e070ef9517849df556cad609ec4d))
* migrate commerce ([#106](https://github.com/faker-js/faker/issues/106)) ([82ab145](https://github.com/faker-js/faker/commit/82ab145286909d49a798c95bf46ea504ebdd7be7))
* migrate company ([#132](https://github.com/faker-js/faker/issues/132)) ([0205183](https://github.com/faker-js/faker/commit/0205183ed821fa1bc04bbb290e7ab713db6e5a91))
* migrate database ([#89](https://github.com/faker-js/faker/issues/89)) ([4d4653e](https://github.com/faker-js/faker/commit/4d4653e6cdfd551b5d6d7a939c85f4232391a235))
* migrate date ([#83](https://github.com/faker-js/faker/issues/83)) ([0fe6f2b](https://github.com/faker-js/faker/commit/0fe6f2b2f6780497c8992820326a610a702eb8aa))
* migrate fake ([#79](https://github.com/faker-js/faker/issues/79)) ([8fa14c6](https://github.com/faker-js/faker/commit/8fa14c698c25ee23ffc5168d7c69dc37fd7f8c4b))
* migrate finance ([#131](https://github.com/faker-js/faker/issues/131)) ([a1208ca](https://github.com/faker-js/faker/commit/a1208cacf76f96738a3505e2a71ae91d9759b805))
* migrate git ([#78](https://github.com/faker-js/faker/issues/78)) ([6122d3c](https://github.com/faker-js/faker/commit/6122d3c9b0e6c352514bf35707187da56e379359))
* migrate hacker ([#81](https://github.com/faker-js/faker/issues/81)) ([e0005ad](https://github.com/faker-js/faker/commit/e0005addb5c21773e0f61b2ba8be821b94822a10))
* migrate helpers ([#85](https://github.com/faker-js/faker/issues/85)) ([07f8b44](https://github.com/faker-js/faker/commit/07f8b4482eec8da397f87bbaa81450f7036f2236))
* migrate image ([#92](https://github.com/faker-js/faker/issues/92)) ([3c3e567](https://github.com/faker-js/faker/commit/3c3e567f4d9d901770a76bf30068a6742a00d882))
* migrate internet ([#94](https://github.com/faker-js/faker/issues/94)) ([8fcfcc6](https://github.com/faker-js/faker/commit/8fcfcc6b1a64f078ad14b4a434ffb2969487aca1))
* migrate lorem ([#86](https://github.com/faker-js/faker/issues/86)) ([7e6273b](https://github.com/faker-js/faker/commit/7e6273b32bdb6a1f1057a2d9b7dd1bbe6acccca0))
* migrate music ([#107](https://github.com/faker-js/faker/issues/107)) ([46d51ba](https://github.com/faker-js/faker/commit/46d51bac072e1efee0b7c6ddfa4b6aac2a9aa0ee))
* migrate name ([#103](https://github.com/faker-js/faker/issues/103)) ([de9f9d6](https://github.com/faker-js/faker/commit/de9f9d67c5fe9a698b048644f5b7374142b523b6))
* migrate phone ([#127](https://github.com/faker-js/faker/issues/127)) ([77f4e63](https://github.com/faker-js/faker/commit/77f4e63c4bfac731b813f7577a6e257548e20dd9))
* migrate random ([#91](https://github.com/faker-js/faker/issues/91)) ([efc11f1](https://github.com/faker-js/faker/commit/efc11f16e9c05e144c93fd2a6192c22313737918))
* migrate system ([#90](https://github.com/faker-js/faker/issues/90)) ([c0a6277](https://github.com/faker-js/faker/commit/c0a62778cc32b120e256f9779b1088b8a753e2ae))
* migrate time ([#74](https://github.com/faker-js/faker/issues/74)) ([168a211](https://github.com/faker-js/faker/commit/168a21146f953b3e219c32bd91bff3da9fe92296))
* migrate unique ([#128](https://github.com/faker-js/faker/issues/128)) ([86580d8](https://github.com/faker-js/faker/commit/86580d89135bfa0c077d96bb9634a1e47c2f7ea9))
* migrate vehicle ([#130](https://github.com/faker-js/faker/issues/130)) ([661f3b4](https://github.com/faker-js/faker/commit/661f3b4272b2a429c704ab31b4e839bd9ac94f94))
* migrate word ([#102](https://github.com/faker-js/faker/issues/102)) ([1b10032](https://github.com/faker-js/faker/commit/1b1003269ac90db163d4a2c23f5160192add8d6d))
* rewrite datatype to ts ([e0d0b5c](https://github.com/faker-js/faker/commit/********************51650becf9ea83b99ae2))


### Bug Fixes

* added missing keys into package.json ([#148](https://github.com/faker-js/faker/issues/148)) ([b20f80b](https://github.com/faker-js/faker/commit/b20f80bf7886e89045e369add9c5598d3586a225))

## [6.0.0-alpha.2](https://github.com/faker-js/faker/compare/v6.0.0-alpha.1...v6.0.0-alpha.2) (2022-01-14)

## 6.0.0-alpha.1 (2022-01-14)

## 6.0.0-alpha.0 (2022-01-07)

For changes prior to version `v6`, see [the old changelog](https://github.com/faker-js/faker/blob/v6/CHANGELOG_old.md).
