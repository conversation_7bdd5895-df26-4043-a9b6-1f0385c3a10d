{"version": 3, "sources": ["../../src/mutationOptions.ts"], "sourcesContent": ["import type { DefaultError, WithRequired } from '@tanstack/query-core'\nimport type { UseMutationOptions } from './types'\n\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: WithRequired<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): WithRequired<\n  UseMutationOptions<TData, TError, TVariables, TContext>,\n  'mutationKey'\n>\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): Omit<UseMutationOptions<TData, TError, TVariables, TContext>, 'mutationKey'>\nexport function mutationOptions<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationOptions<TData, TError, TVariables, TContext> {\n  return options\n}\n"], "mappings": ";AA4BO,SAAS,gBAMd,SACyD;AACzD,SAAO;AACT;", "names": []}