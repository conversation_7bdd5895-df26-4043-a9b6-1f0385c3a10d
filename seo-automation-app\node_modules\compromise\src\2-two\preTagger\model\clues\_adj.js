const jj = 'Adjective'

export default {
  beforeTags: {
    Determiner: jj, //the detailed
    // Copula: jj, //is detailed
    Possessive: jj, //spencer's detailed
    Hyphenated: jj, //rapidly-changing
  },

  afterTags: {
    // Noun: jj, //detailed plan, overwhelming evidence
    Adjective: jj, //intoxicated little
  },

  beforeWords: {
    seem: jj, //seem prepared
    seemed: jj,
    seems: jj,
    feel: jj, //feel prepared
    feels: jj,
    felt: jj,
    stay: jj,
    appear: jj,
    appears: jj,
    appeared: jj,
    also: jj,
    over: jj, //over cooked
    under: jj,
    too: jj, //too insulting
    it: jj, //find it insulting
    but: jj, //nothing but frustrating
    still: jj, //still scared
    // adverbs that are adjective-ish
    really: jj, //really damaged
    quite: jj,
    well: jj,
    very: jj,
    truly: jj,
    how: jj, //how slow
    deeply: jj,
    hella: jj,
    // always: jj,
    // never: jj,
    profoundly: jj,
    extremely: jj,
    so: jj,
    badly: jj,
    mostly: jj,
    totally: jj,
    awfully: jj,
    rather: jj,
    nothing: jj, //nothing secret,
    something: jj, //something wrong
    anything: jj,
    not: jj, //not swell
    me: jj, //called me swell
    is: jj,

    face: jj, //faces shocking revelations
    faces: jj,
    faced: jj,

    look: jj,
    looks: jj,
    looked: jj,

    reveal: jj,
    reveals: jj,
    revealed: jj,

    sound: jj,
    sounded: jj,
    sounds: jj,
    remains: jj,
    remained: jj,
    prove: jj, //would prove shocking
    proves: jj,
    proved: jj,

    becomes: jj,
    stays: jj,
    tastes: jj,
    taste: jj,
    smells: jj,
    smell: jj,
    gets: jj, //gets shocking snowfall
    grows: jj,
    as: jj,
    rings: jj,
    radiates: jj,
    conveys: jj,
    convey: jj,
    conveyed: jj,
    of: jj,
    // 'smacks of': jj,
    // 'reeks of': jj,
  },
  afterWords: {
    too: jj, //insulting too
    also: jj, //insulting too
    or: jj, //insulting or
    enough: jj, //cool enough
    as: jj, //as shocking as
    //about: jj, //cool about
  },
}
