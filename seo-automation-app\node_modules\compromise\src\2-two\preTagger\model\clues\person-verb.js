import person from './_person.js'
import verb from './_verb.js'
import noun from './_noun.js'

// 'rob the store'   -  'rob lowe'
// can be a noun too - 'losing hope'
const clues = {
  beforeTags: Object.assign({}, noun.beforeTags, person.beforeTags, verb.beforeTags),
  afterTags: Object.assign({}, noun.afterTags, person.afterTags, verb.afterTags),
  beforeWords: Object.assign({}, noun.beforeWords, person.beforeWords, verb.beforeWords),
  afterWords: Object.assign({}, noun.afterWords, person.afterWords, verb.afterWords),
}
export default clues