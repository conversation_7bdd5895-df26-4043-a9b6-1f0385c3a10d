!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).efrt={})}(this,(function(t){"use strict";var n=function(t,n){let e=Math.min(t.length,n.length);for(;e>0;){const o=t.slice(0,e);if(o===n.slice(0,e))return o;e-=1}return""},e=function(t){t.sort();for(let n=1;n<t.length;n++)t[n-1]===t[n]&&t.splice(n,1)};const o=function(){this.counts={}},s={init:function(t){void 0===this.counts[t]&&(this.counts[t]=0)},add:function(t,n){void 0===n&&(n=1),this.init(t),this.counts[t]+=n},countOf:function(t){return this.init(t),this.counts[t]},highest:function(t){let n=[];const e=Object.keys(this.counts);for(let t=0;t<e.length;t++){const o=e[t];n.push([o,this.counts[o]])}return n.sort((function(t,n){return n[1]-t[1]})),t&&(n=n.slice(0,t)),n}};Object.keys(s).forEach((function(t){o.prototype[t]=s[t]}));const i=36,r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",u=r.split("").reduce((function(t,n,e){return t[n]=e,t}),{});var c=function(t){if(void 0!==r[t])return r[t];let n=1,e=i,o="";for(;t>=e;t-=e,n++,e*=i);for(;n--;){const n=t%i;o=String.fromCharCode((n<10?48:55)+n)+o,t=(t-n)/i}return o},f=function(t){if(void 0!==u[t])return u[t];let n=0,e=1,o=i,s=1;for(;e<t.length;n+=o,e++,o*=i);for(let e=t.length-1;e>=0;e--,s*=i){let o=t.charCodeAt(e)-48;o>10&&(o-=7),n+=o*s}return n};const h=";",l=":",d=",",p="!",g=36,a=function(t,n){let e="",o="";t.isTerminal(n)&&(e+=p);const s=t.nodeProps(n);for(let i=0;i<s.length;i++){const r=s[i];if("number"==typeof n[r]){e+=o+r,o=d;continue}if(t.syms[n[r]._n]){e+=o+r+t.syms[n[r]._n],o="";continue}let u=c(n._n-n[r]._n-1+t.symCount);n[r]._g&&u.length>=n[r]._g.length&&1===n[n[r]._g]?(u=n[r]._g,e+=o+r+u,o=d):(e+=o+r+u,o="")}return e},y=function(t,n){if(t.visited(n))return;const e=t.nodeProps(n,!0);for(let o=0;o<e.length;o++){const s=e[o],i=n._n-n[s]._n-1;i<g&&t.histRel.add(i),t.histAbs.add(n[s]._n,c(i).length-1),y(t,n[s])}},m=function(t,n){if(void 0!==n._n)return;const e=t.nodeProps(n,!0);for(let o=0;o<e.length;o++)m(t,n[e[o]]);n._n=t.pos++,t.nodes.unshift(n)},b=function(t){t.nodes=[],t.nodeCount=0,t.syms={},t.symCount=0,t.pos=0,t.optimize(),t.histAbs=new o,t.histRel=new o,m(t,t.root),t.nodeCount=t.nodes.length,t.prepDFS(),y(t,t.root),t.symCount=function(t){t.histAbs=t.histAbs.highest(g);const n=[];n[-1]=0;let e=0,o=0;const s=3+c(t.nodeCount).length;for(let i=0;i<g&&void 0!==t.histAbs[i];i++)n[i]=t.histAbs[i][1]-s-t.histRel.countOf(g-i-1)+n[i-1],n[i]>=e&&(e=n[i],o=i+1);return o}(t);for(let n=0;n<t.symCount;n++)t.syms[t.histAbs[n][0]]=c(n);for(let n=0;n<t.nodeCount;n++)t.nodes[n]=a(t,t.nodes[n]);for(let n=t.symCount-1;n>=0;n--)t.nodes.unshift(c(n)+l+c(t.nodeCount-t.histAbs[n][0]-1));return t.nodes.join(h)},_=new RegExp("[0-9A-Z,;!:|¦]"),C={_d:!0,_v:!0,_c:!0,_g:!0,_n:!0},v={insertWords:function(t){if(void 0!==t){"string"==typeof t&&(t=t.split(/[^a-zA-Z]+/));for(let n=0;n<t.length;n++)t[n]=t[n].toLowerCase();e(t);for(let n=0;n<t.length;n++)null===t[n].match(_)&&this.insert(t[n])}},insert:function(t){this._insert(t,this.root);const e=this.lastWord;this.lastWord=t;if(n(t,e)===e)return;const o=this.uniqueNode(e,t,this.root);o&&this.combineSuffixNode(o)},_insert:function(t,e){let o,s;if(0===t.length)return;const i=Object.keys(e);for(let r=0;r<i.length;r++){const u=i[r];if(o=n(t,u),0!==o.length){if(u===o&&"object"==typeof e[u])return void this._insert(t.slice(o.length),e[u]);if(u===t&&"number"==typeof e[u])return;return s={},s[u.slice(o.length)]=e[u],this.addTerminal(s,t=t.slice(o.length)),delete e[u],e[o]=s,void this.wordCount++}}this.addTerminal(e,t),this.wordCount++},addTerminal:function(t,n){if(n.length<=1)return void(t[n]=1);const e={};t[n[0]]=e,this.addTerminal(e,n.slice(1))},nodeProps:function(t,n){const e=[];for(const o in t)""===o||C.hasOwnProperty(o)||n&&"object"!=typeof t[o]||e.push(o);return e.sort(),e},optimize:function(){this.combineSuffixNode(this.root),this.prepDFS(),this.countDegree(this.root),this.prepDFS(),this.collapseChains(this.root)},combineSuffixNode:function(t){if(t._c)return t;let n=[];this.isTerminal(t)&&n.push("!");const e=this.nodeProps(t);for(let o=0;o<e.length;o++){const s=e[o];"object"==typeof t[s]?(t[s]=this.combineSuffixNode(t[s]),n.push(s),n.push(t[s]._c)):n.push(s)}n=n.join("-");const o=this.suffixes[n];return o||(this.suffixes[n]=t,t._c=this.cNext++,t)},prepDFS:function(){this.vCur++},visited:function(t){return t._v===this.vCur||(t._v=this.vCur,!1)},countDegree:function(t){if(void 0===t._d&&(t._d=0),t._d++,this.visited(t))return;const n=this.nodeProps(t,!0);for(let e=0;e<n.length;e++)this.countDegree(t[n[e]])},collapseChains:function(t){let n,e,o;if(this.visited(t))return;const s=this.nodeProps(t);for(o=0;o<s.length;o++)n=s[o],e=t[n],"object"==typeof e&&(this.collapseChains(e),void 0===e._g||1!==e._d&&1!==e._g.length||(delete t[n],n+=e._g,t[n]=e[e._g]));1!==s.length||this.isTerminal(t)||(t._g=n)},isTerminal:function(t){return!!t[""]},uniqueNode:function(t,n,e){const o=this.nodeProps(e,!0);for(let s=0;s<o.length;s++){const i=o[s];if(i===t.slice(0,i.length))return i!==n.slice(0,i.length)?e[i]:this.uniqueNode(t.slice(i.length),n.slice(i.length),e[i])}},pack:function(){return b(this)}},j=function(t){this.root={},this.lastWord="",this.suffixes={},this.suffixCounts={},this.cNext=1,this.wordCount=0,this.insertWords(t),this.vCur=0};Object.keys(v).forEach((function(t){j.prototype[t]=v[t]}));const A=function(t){return"[object Array]"===Object.prototype.toString.call(t)},O=function(t,n,e){const o=f(n);return o<t.symCount?t.syms[o]:e+o+1-t.symCount},x=function(t){const n={nodes:t.split(";"),syms:[],symCount:0};return t.match(":")&&function(t){const n=new RegExp("([0-9A-Z]+):([0-9A-Z]+)");for(let e=0;e<t.nodes.length;e++){const o=n.exec(t.nodes[e]);if(!o){t.symCount=e;break}t.syms[f(o[1])]=f(o[2])}t.nodes=t.nodes.slice(t.symCount,t.nodes.length)}(n),function(t){const n=[],e=(o,s)=>{let i=t.nodes[o];"!"===i[0]&&(n.push(s),i=i.slice(1));const r=i.split(/([A-Z0-9,]+)/g);for(let i=0;i<r.length;i+=2){const u=r[i],c=r[i+1];if(!u)continue;const f=s+u;if(","===c||void 0===c){n.push(f);continue}const h=O(t,c,o);e(h,f)}};return e(0,""),n}(n)};t.pack=function(t){var n;t=null==(n=t)?{}:"string"==typeof n?n.split(/ +/g).reduce((function(t,n){return t[n]=!0,t}),{}):A(n)?n.reduce((function(t,n){return t[n]=!0,t}),{}):n;const e=Object.keys(t).reduce((function(n,e){const o=t[e];if(A(o)){for(let t=0;t<o.length;t++)n[o[t]]=n[o[t]]||[],n[o[t]].push(e);return n}return!1===n.hasOwnProperty(o)&&Object.defineProperty(n,o,{writable:!0,enumerable:!0,configurable:!0,value:[]}),n[o].push(e),n}),{});return Object.keys(e).forEach((function(t){const n=new j(e[t]);e[t]=n.pack()})),Object.keys(e).map((t=>t+"¦"+e[t])).join("|")},t.unpack=function(t){if(!t)return{};const n=t.split("|").reduce(((t,n)=>{const e=n.split("¦");return t[e[0]]=e[1],t}),{}),e={};return Object.keys(n).forEach((function(t){const o=x(n[t]);"true"===t&&(t=!0);for(let n=0;n<o.length;n++){const s=o[n];!0===e.hasOwnProperty(s)?!1===Array.isArray(e[s])?e[s]=[e[s],t]:e[s].push(t):e[s]=t}})),e},t.version="2.7.0",Object.defineProperty(t,"__esModule",{value:!0})}));
