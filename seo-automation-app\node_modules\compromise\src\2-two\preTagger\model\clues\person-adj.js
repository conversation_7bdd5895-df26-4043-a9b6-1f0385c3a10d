import person from './_person.js'
import adj from './_adj.js'

// 'rusty nail'   -  'rusty smith'
const clues = {
  beforeTags: Object.assign({}, person.beforeTags, adj.beforeTags),
  afterTags: Object.assign({}, person.afterTags, adj.afterTags),
  beforeWords: Object.assign({}, person.beforeWords, adj.beforeWords),
  afterWords: Object.assign({}, person.afterWords, adj.afterWords),
}
export default clues