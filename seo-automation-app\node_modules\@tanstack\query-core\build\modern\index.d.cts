export { T as AnyDataTag, b6 as CancelOptions, C as CancelledError, V as DataTag, G as DefaultError, b5 as DefaultOptions, ak as DefaultedInfiniteQueryObserverOptions, ai as DefaultedQueryObserverOptions, aP as DefinedInfiniteQueryObserverResult, aG as DefinedQueryObserverResult, D as DehydrateOptions, A as DehydratedState, B as DistributiveOmit, $ as Enabled, an as EnsureInfiniteQueryDataOptions, am as EnsureQueryDataOptions, ao as FetchInfiniteQueryOptions, av as FetchNextPageOptions, aw as FetchPreviousPageOptions, al as FetchQueryOptions, ay as FetchStatus, a7 as GetNextPageParamFunction, a6 as GetPreviousPageParamFunction, H as HydrateOptions, W as InferDataFromTag, X as InferErrorFromTag, a8 as InfiniteData, aI as InfiniteQueryObserverBaseResult, aL as InfiniteQueryObserverLoadingErrorResult, aK as InfiniteQueryObserverLoadingResult, aj as InfiniteQueryObserverOptions, aJ as InfiniteQueryObserverPendingResult, aO as InfiniteQueryObserverPlaceholderResult, aM as InfiniteQueryObserverRefetchErrorResult, aQ as InfiniteQueryObserverResult, aN as InfiniteQueryObserverSuccessResult, ae as InfiniteQueryPageParamsOptions, a2 as InitialDataFunction, ad as InitialPageParam, at as InvalidateOptions, ar as InvalidateQueryFilters, aZ as MutateFunction, aY as MutateOptions, z as Mutation, M as MutationCache, d as MutationCacheNotifyEvent, j as MutationFilters, aV as MutationFunction, aR as MutationKey, aU as MutationMeta, e as MutationObserver, a_ as MutationObserverBaseResult, b1 as MutationObserverErrorResult, a$ as MutationObserverIdleResult, b0 as MutationObserverLoadingResult, aX as MutationObserverOptions, b3 as MutationObserverResult, b2 as MutationObserverSuccessResult, aW as MutationOptions, aT as MutationScope, y as MutationState, aS as MutationStatus, aa as NetworkMode, F as NoInfer, N as NonUndefinedGuard, b9 as NotifyEvent, b8 as NotifyEventType, ab as NotifyOnChangeProps, O as OmitKeyof, E as Override, a3 as PlaceholderDataFunction, a4 as QueriesPlaceholderDataFunction, x as Query, Q as QueryCache, a as QueryCacheNotifyEvent, b as QueryClient, b4 as QueryClientConfig, l as QueryFilters, Y as QueryFunction, a1 as QueryFunctionContext, I as QueryKey, a5 as QueryKeyHashFunction, a9 as QueryMeta, c as QueryObserver, az as QueryObserverBaseResult, aC as QueryObserverLoadingErrorResult, aB as QueryObserverLoadingResult, ag as QueryObserverOptions, aA as QueryObserverPendingResult, aF as QueryObserverPlaceholderResult, aD as QueryObserverRefetchErrorResult, aH as QueryObserverResult, aE as QueryObserverSuccessResult, ac as QueryOptions, a0 as QueryPersister, w as QueryState, ax as QueryStatus, aq as RefetchOptions, as as RefetchQueryFilters, R as Register, au as ResetOptions, ap as ResultOptions, b7 as SetDataOptions, S as SkipToken, Z as StaleTime, _ as StaleTimeFunction, af as ThrowOnError, P as UnsetMarker, U as Updater, ah as WithRequired, K as dataTagErrorSymbol, J as dataTagSymbol, v as defaultShouldDehydrateMutation, u as defaultShouldDehydrateQuery, q as dehydrate, h as hashKey, t as hydrate, o as isCancelledError, i as isServer, k as keepPreviousData, f as matchMutation, m as matchQuery, n as noop, p as partialMatchKey, r as replaceEqualDeep, g as shouldThrowError, s as skipToken, L as unsetMarker } from './hydration-CADtEOkK.cjs';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.cjs';
export { InfiniteQueryObserver } from './infiniteQueryObserver.cjs';
export { defaultScheduler, notifyManager } from './notifyManager.cjs';
export { focusManager } from './focusManager.cjs';
export { onlineManager } from './onlineManager.cjs';
export { streamedQuery as experimental_streamedQuery } from './streamedQuery.cjs';
import './removable.cjs';
import './subscribable.cjs';
