import { HeadingAnalysis, Heading } from './heading-analyzer';
import { Entity } from './entity-analyzer';
import { MetaTags } from './meta-tag-analyzer';
import { ContentStructureAnalysisResult } from './content-structure-analyzer';
import { WordAnalysis } from './word-count';
import { LsiKeyword } from './lsi-keyword-extractor';

export interface SeoAnalysisResult {
  wordAnalysis: WordAnalysis;
  keywordDensity: number;
  keywordVariations: string[];
  keywordDistribution: number[];
  keywordProminence: number;
  headingAnalysis: HeadingAnalysis;
  lsiKeywords: LsiKeyword[];
  entities: Entity[];
  metaTags: MetaTags;
  contentStructure: ContentStructureAnalysisResult;
  seoScore: number;
  recommendations: string[];
}

export { Heading, Entity, MetaTags, HeadingAnalysis, ContentStructureAnalysisResult, WordAnalysis, LsiKeyword };