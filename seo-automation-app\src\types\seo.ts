
import { HeadingAnalysis, Heading } from './heading-analyzer';
import { Entity, EntityRelationship } from './entity-analyzer';
import { MetaTags } from './meta-tag-analyzer';
import { ContentStructureAnalysisResult } from './content-structure-analyzer';
import { WordAnalysis } from './word-count';
import { LsiKeyword } from './lsi-keyword-extractor';
import { PrecisionMetrics } from './keyword-analyzer';
import { HeadingMetrics } from './heading-optimization-counter';

export interface CompetitorComparison {
  averageWordCount: number;
  wordCountDifference: number; // Your word count vs average
  averageKeywordDensity: number;
  keywordDensityDifference: number;
  // Add more comparison metrics as needed
}

export interface SeoAnalysisResult {
  wordAnalysis: WordAnalysis;
  keywordDensity: number;
  keywordVariations: string[];
  keywordDistribution: number[];
  keywordProminence: number;
  precisionKeywordAnalysis: PrecisionMetrics;
  headingAnalysis: HeadingAnalysis;
  headingOptimizationMetrics: HeadingMetrics;
  lsiKeywords: LsiKeyword[];
  entities: Entity[];
  entityRelationships: EntityRelationship[];
  metaTags: MetaTags;
  contentStructure: ContentStructureAnalysisResult;
  seoScore: number;
  recommendations: string[];
  competitorComparison?: CompetitorComparison; // Optional, only present if comparison is done
}

export interface SeoMetrics {
  id?: string; // UUID from database
  competitorAnalysisId: string; // Foreign key to competitor_analysis table
  keyword: string;
  location: string;
  analysisDate: string; // ISO string
  metrics: SeoAnalysisResult; // Store the full analysis result
}

export { Heading, Entity, MetaTags, HeadingAnalysis, ContentStructureAnalysisResult, WordAnalysis, LsiKeyword, PrecisionMetrics, HeadingMetrics, EntityRelationship };
