
import OpenAI from 'openai';
import { fillExpertContentPrompt } from './prompts/expert-content-template';
import { ContentQualityChecker, ContentQualityAnalysisResult } from './quality-checker';
import { HumanWritingPatternAnalyzer, HumanWritingPatternAnalysis } from './human-writing-patterns';
import { EeatOptimizer, EeatOptimizationResult } from './eeat-optimizer';
import { CurrentInformationIntegrator } from './current-information-integrator';
import { UserValueOptimizer, UserValueAnalysisResult } from './user-value-optimizer';
import { AuthoritySignalIntegrator, AuthoritySignalAnalysisResult } from './authority-signal-integrator';

export interface GeneratedContent {
  content: string;
  wordCount: number;
  qualityAnalysis: ContentQualityAnalysisResult; // Detailed quality analysis
  humanWritingAnalysis: HumanWritingPatternAnalysis; // Human-like writing analysis
  eeatOptimization: EeatOptimizationResult; // E-E-A-T optimization analysis
  userValueAnalysis: UserValueAnalysisResult; // User value analysis
  authoritySignalAnalysis: AuthoritySignalAnalysisResult; // Authority signal analysis
  timestamp: string;
}

export interface ContentGenerationOptions {
  keyword: string;
  industry: string;
  targetAudience: string;
  tone: string;
  wordCount: number;
  competitorInsights?: string;
}

export class AIContentGenerator {
  private openai: OpenAI;
  private qualityChecker: ContentQualityChecker;
  private humanWritingAnalyzer: HumanWritingPatternAnalyzer;
  private eeatOptimizer: EeatOptimizer;
  private currentInformationIntegrator: CurrentInformationIntegrator;
  private userValueOptimizer: UserValueOptimizer;
  private authoritySignalIntegrator: AuthoritySignalIntegrator;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is not set.');
    }
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.qualityChecker = new ContentQualityChecker();
    this.humanWritingAnalyzer = new HumanWritingPatternAnalyzer();
    this.eeatOptimizer = new EeatOptimizer();
    this.currentInformationIntegrator = new CurrentInformationIntegrator();
    this.userValueOptimizer = new UserValueOptimizer();
    this.authoritySignalIntegrator = new AuthoritySignalIntegrator();
  }

  private getExpertSystemPrompt(): string {
    return `You are an AI content generation expert with 20+ years of experience in SEO and content marketing. Your goal is to produce highly authoritative, human-like, and SEO-optimized content that ranks as the best answer across all search engines. Focus on E-E-A-T principles: Experience, Expertise, Authoritativeness, and Trustworthiness. Incorporate the latest facts and studies relevant to 2025.`;
  }

  async generate(options: ContentGenerationOptions): Promise<GeneratedContent> {
    const currentInfo = await this.currentInformationIntegrator.fetchCurrentInformation(options.keyword, options.industry);
    const formattedCurrentInfo = this.currentInformationIntegrator.formatForPrompt(currentInfo);

    const userPrompt = fillExpertContentPrompt({
      ...options,
      competitorInsights: (options.competitorInsights || '') + formattedCurrentInfo,
    });

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o", // Using gpt-4o as it's a capable model
        messages: [
          { role: "system", content: this.getExpertSystemPrompt() },
          { role: "user", content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: Math.round(options.wordCount * 1.5), // Allow some buffer for tokens
      });

      const content = response.choices[0].message.content;

      if (!content) {
        throw new Error('AI did not return any content.');
      }

      const qualityAnalysis = await this.qualityChecker.analyze(content);
      const humanWritingAnalysis = this.humanWritingAnalyzer.analyze(content);
      const eeatOptimization = this.eeatOptimizer.optimize(content, { industry: options.industry, keyword: options.keyword });
      const userValueAnalysis = this.userValueOptimizer.optimize(content, { keyword: options.keyword, targetAudience: options.targetAudience });
      const authoritySignalAnalysis = this.authoritySignalIntegrator.integrate(content);

      return {
        content,
        wordCount: content.split(/\s+/).length,
        qualityAnalysis,
        humanWritingAnalysis,
        eeatOptimization,
        userValueAnalysis,
        authoritySignalAnalysis,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error generating content with OpenAI:', error);
      throw new Error(`Failed to generate content: ${error.message}`);
    }
  }
}
