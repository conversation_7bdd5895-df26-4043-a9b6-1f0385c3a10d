{"version": 3, "sources": ["../../../../src/client/components/builtin/layout.tsx"], "sourcesContent": ["import React from 'react'\n\nexport default function DefaultLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html>\n      <body>{children}</body>\n    </html>\n  )\n}\n"], "names": ["DefaultLayout", "children", "html", "body"], "mappings": ";;;;+BAEA;;;eAAwBA;;;;;gEAFN;AAEH,SAASA,cAAc,KAIrC;IAJqC,IAAA,EACpCC,QAAQ,EAGT,GAJqC;IAKpC,qBACE,qBAACC;kBACC,cAAA,qBAACC;sBAAMF;;;AAGb", "ignoreList": [0]}