{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst regexCssError =\n  /^(?:CssSyntaxError|SyntaxError)\\n\\n\\((\\d+):(\\d*)\\) (.*)$/s\n\nexport function getCssError(\n  fileName: string,\n  err: Error\n): SimpleWebpackError | false {\n  if (\n    !(\n      (err.name === 'CssSyntaxError' || err.name === 'SyntaxError') &&\n      (err as any).stack === false &&\n      !(err instanceof SyntaxError)\n    )\n  ) {\n    return false\n  }\n\n  // https://github.com/postcss/postcss-loader/blob/d6931da177ac79707bd758436e476036a55e4f59/src/Error.js\n\n  const res = regexCssError.exec(err.message)\n  if (res) {\n    const [, _lineNumber, _column, reason] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumber, 10))\n    const column = Math.max(1, parseInt(_column, 10))\n\n    return new SimpleWebpackError(\n      `${cyan(fileName)}:${yellow(lineNumber.toString())}:${yellow(\n        column.toString()\n      )}`,\n      red(bold('Syntax error')).concat(`: ${reason}`)\n    )\n  }\n\n  return false\n}\n"], "names": ["bold", "cyan", "red", "yellow", "SimpleWebpackError", "regexCssError", "getCssError", "fileName", "err", "name", "stack", "SyntaxError", "res", "exec", "message", "_lineNumber", "_column", "reason", "lineNumber", "Math", "max", "parseInt", "column", "toString", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,MAAMC,gBACJ;AAEF,OAAO,SAASC,YACdC,QAAgB,EAChBC,GAAU;IAEV,IACE,CACE,CAAA,AAACA,CAAAA,IAAIC,IAAI,KAAK,oBAAoBD,IAAIC,IAAI,KAAK,aAAY,KAC3D,AAACD,IAAYE,KAAK,KAAK,SACvB,CAAEF,CAAAA,eAAeG,WAAU,CAAC,GAE9B;QACA,OAAO;IACT;IAEA,uGAAuG;IAEvG,MAAMC,MAAMP,cAAcQ,IAAI,CAACL,IAAIM,OAAO;IAC1C,IAAIF,KAAK;QACP,MAAM,GAAGG,aAAaC,SAASC,OAAO,GAAGL;QACzC,MAAMM,aAAaC,KAAKC,GAAG,CAAC,GAAGC,SAASN,aAAa;QACrD,MAAMO,SAASH,KAAKC,GAAG,CAAC,GAAGC,SAASL,SAAS;QAE7C,OAAO,IAAIZ,mBACT,GAAGH,KAAKM,UAAU,CAAC,EAAEJ,OAAOe,WAAWK,QAAQ,IAAI,CAAC,EAAEpB,OACpDmB,OAAOC,QAAQ,KACd,EACHrB,IAAIF,KAAK,iBAAiBwB,MAAM,CAAC,CAAC,EAAE,EAAEP,QAAQ;IAElD;IAEA,OAAO;AACT", "ignoreList": [0]}