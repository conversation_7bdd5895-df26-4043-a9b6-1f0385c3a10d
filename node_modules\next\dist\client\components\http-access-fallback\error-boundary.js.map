{"version": 3, "sources": ["../../../../src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["HTTPAccessFallbackBoundary", "HTTPAccessFallbackErrorBoundary", "React", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "warnOnce", "getDerivedStateFromError", "error", "isHTTPAccessFallbackError", "httpStatus", "getAccessFallbackHTTPStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "getAccessFallbackErrorTypeByStatus", "constructor", "useUntrackedPathname", "useContext", "MissingSlotContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA;;;;;+BAuJgBA;;;eAAAA;;;;;iEA1IkB;qCACG;oCAM9B;0BACkB;+CACU;AAqBnC,MAAMC,wCAAwCC,cAAK,CAACC,SAAS;IAY3DC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,IAAI,CAACC,KAAK,CAACC,YAAY,IACvB,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,IAAI,GAAG,KAC/B,4EAA4E;QAC5E,CAAC,IAAI,CAACF,KAAK,CAACC,YAAY,CAACE,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAS,AAAC,MAAGA,MAClBC,IAAI,CAAC;YAERV,kBAAkB,oBAAoBC;YAEtCU,IAAAA,kBAAQ,EAACX;QACX;IACF;IAEA,OAAOY,yBAAyBC,KAAU,EAAE;QAC1C,IAAIC,IAAAA,6CAAyB,EAACD,QAAQ;YACpC,MAAME,aAAaC,IAAAA,+CAA2B,EAACH;YAC/C,OAAO;gBACLI,iBAAiBF;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMF;IACR;IAEA,OAAOK,yBACLtB,KAA2C,EAC3CuB,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAIvB,MAAMwB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBK;gBACjBD,kBAAkBzB,MAAMwB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBE,MAAMF,eAAe;YACtCI,kBAAkBzB,MAAMwB,QAAQ;QAClC;IACF;IAEAG,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAC/B,KAAK;QAClE,MAAM,EAAEqB,eAAe,EAAE,GAAG,IAAI,CAACE,KAAK;QACtC,MAAMS,kBAAkB;YACtB,CAACC,yCAAqB,CAACC,SAAS,CAAC,EAAEN;YACnC,CAACK,yCAAqB,CAACE,SAAS,CAAC,EAAEN;YACnC,CAACI,yCAAqB,CAACG,YAAY,CAAC,EAAEN;QACxC;QAEA,IAAIT,iBAAiB;YACnB,MAAMgB,aACJhB,oBAAoBY,yCAAqB,CAACC,SAAS,IAAIN;YACzD,MAAMU,cACJjB,oBAAoBY,yCAAqB,CAACE,SAAS,IAAIN;YACzD,MAAMU,iBACJlB,oBAAoBY,yCAAqB,CAACG,YAAY,IAAIN;YAE5D,kGAAkG;YAClG,IAAI,CAAEO,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOR;YACT;YAEA,qBACE;;kCACE,qBAACS;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3B7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACyC;wBACCC,MAAK;wBACLC,SAASC,IAAAA,sDAAkC,EAACtB;;oBAG/CW,eAAe,CAACX,gBAAgB;;;QAGvC;QAEA,OAAOU;IACT;IArGAa,YAAY5C,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACuB,KAAK,GAAG;YACXF,iBAAiBK;YACjBD,kBAAkBzB,MAAMwB,QAAQ;QAClC;IACF;AAgGF;AAEO,SAAShC,2BAA2B,KAKT;IALS,IAAA,EACzCoC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB,GALS;IAMzC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAMP,WAAWqB,IAAAA,yCAAoB;IACrC,MAAM5C,eAAe6C,IAAAA,iBAAU,EAACC,iDAAkB;IAClD,MAAMC,mBAAmB,CAAC,CAAEpB,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIkB,kBAAkB;QACpB,qBACE,qBAACvD;YACC+B,UAAUA;YACVI,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACd7B,cAAcA;sBAEb8B;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ", "ignoreList": [0]}