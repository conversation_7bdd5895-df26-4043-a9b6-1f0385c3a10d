//prefixes give very-little away, in general.
// more-often for scientific terms, etc.
const Adj = 'Adjective'
const Noun = 'Noun'
const Verb = 'Verb'

export default [
  null,
  null,
  {
    // 2-letter
  },
  {
    // 3-letter
    neo: Noun,
    bio: Noun,
    // pre: Noun,
    'de-': Verb,
    're-': Verb,
    'un-': Verb,
    'ex-': Noun,
  },
  {
    // 4-letter
    anti: Noun,
    auto: Noun,
    faux: Adj,
    hexa: Noun,
    kilo: Noun,
    mono: Noun,
    nano: Noun,
    octa: Noun,
    poly: Noun,
    semi: Adj,
    tele: Noun,
    'pro-': Adj,
    'mis-': Verb,
    'dis-': Verb,
    'pre-': Adj, //hmm
  },
  {
    // 5-letter
    anglo: Noun,
    centi: Noun,
    ethno: Noun,
    ferro: Noun,
    grand: Noun,
    hepta: Noun,
    hydro: Noun,
    intro: Noun,
    macro: Noun,
    micro: Noun,
    milli: Noun,
    nitro: Noun,
    penta: Noun,
    quasi: Adj,
    radio: Noun,
    tetra: Noun,
    'omni-': Adj,
    'post-': Adj,
  },
  {
    // 6-letter
    pseudo: Adj,
    'extra-': Adj,
    'hyper-': Adj,
    'inter-': Adj,
    'intra-': Adj,
    'deca-': Adj,
    // 'trans-': Noun,
  },
  {
    // 7-letter
    electro: Noun,
  },
]
