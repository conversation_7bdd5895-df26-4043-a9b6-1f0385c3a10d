const p = 'Person'

export default {
  beforeTags: {
    Honorific: p,
    Person: p,
    // Preposition: p, //with sue
  },
  afterTags: {
    Person: p,
    ProperNoun: p,
    Verb: p, //bob could
    // Modal:true, //bob could
    // Copula:true, //bob is
    // PresentTense:true, //bob seems
  },
  ownTags: {
    ProperNoun: p, //capital letter
  },
  beforeWords: {
    hi: p,
    hey: p,
    yo: p,
    dear: p,
    hello: p,
  },
  afterWords: {
    // person-usually verbs
    said: p,
    says: p,
    told: p,
    tells: p,
    feels: p,
    felt: p,
    seems: p,
    thinks: p,
    thought: p,
    spends: p,
    spendt: p,
    plays: p,
    played: p,
    sing: p,
    sang: p,
    learn: p,
    learned: p,
    wants: p,
    wanted: p
    // and:true, //sue and jeff
  },
}
