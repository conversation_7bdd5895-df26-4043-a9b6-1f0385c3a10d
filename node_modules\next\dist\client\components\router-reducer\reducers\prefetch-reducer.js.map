{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts"], "sourcesContent": ["import type {\n  PrefetchAction,\n  ReducerState,\n  ReadonlyReducerState,\n} from '../router-reducer-types'\nimport { PromiseQueue } from '../../promise-queue'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nexport const prefetchQueue = new PromiseQueue(5)\n\nexport const prefetchReducer = process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ? identityReducerWhenSegmentCacheIsEnabled\n  : prefetchReducerImpl\n\nfunction identityReducerWhenSegmentCacheIsEnabled<T>(state: T): T {\n  // Unlike the old implementation, the Segment Cache doesn't store its data in\n  // the router reducer state.\n  //\n  // This shouldn't be reachable because we wrap the prefetch API in a check,\n  // too, which prevents the action from being dispatched. But it's here for\n  // clarity + code elimination.\n  return state\n}\n\nfunction prefetchReducerImpl(\n  state: ReadonlyReducerState,\n  action: PrefetchAction\n): ReducerState {\n  // let's prune the prefetch cache before we do anything else\n  prunePrefetchCache(state.prefetchCache)\n\n  const { url } = action\n\n  getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    prefetchCache: state.prefetchCache,\n    kind: action.kind,\n    tree: state.tree,\n    allowAliasing: true,\n  })\n\n  return state\n}\n"], "names": ["prefetchQueue", "prefetchReducer", "PromiseQueue", "process", "env", "__NEXT_CLIENT_SEGMENT_CACHE", "identityReducerWhenSegmentCacheIsEnabled", "prefetchReducerImpl", "state", "action", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "prefetchCache", "url", "getOrCreatePrefetchCacheEntry", "nextUrl", "kind", "tree", "allowAliasing"], "mappings": ";;;;;;;;;;;;;;;IAUaA,aAAa;eAAbA;;IAEAC,eAAe;eAAfA;;;8BAPgB;oCAItB;AACA,MAAMD,gBAAgB,IAAIE,0BAAY,CAAC;AAEvC,MAAMD,kBAAkBE,QAAQC,GAAG,CAACC,2BAA2B,GAClEC,2CACAC;AAEJ,SAASD,yCAA4CE,KAAQ;IAC3D,6EAA6E;IAC7E,4BAA4B;IAC5B,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8BAA8B;IAC9B,OAAOA;AACT;AAEA,SAASD,oBACPC,KAA2B,EAC3BC,MAAsB;IAEtB,4DAA4D;IAC5DC,IAAAA,sCAAkB,EAACF,MAAMG,aAAa;IAEtC,MAAM,EAAEC,GAAG,EAAE,GAAGH;IAEhBI,IAAAA,iDAA6B,EAAC;QAC5BD;QACAE,SAASN,MAAMM,OAAO;QACtBH,eAAeH,MAAMG,aAAa;QAClCI,MAAMN,OAAOM,IAAI;QACjBC,MAAMR,MAAMQ,IAAI;QAChBC,eAAe;IACjB;IAEA,OAAOT;AACT", "ignoreList": [0]}