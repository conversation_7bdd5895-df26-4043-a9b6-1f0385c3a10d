
export interface LinkDistributionAnalysisResult {
  totalInternalLinks: number;
  averageLinksPerPage: number;
  linkEquityDistributionScore: number; // 0-100, how evenly link equity is distributed
  orphanPages: string[]; // URLs of pages with no internal links pointing to them
  hubPages: string[]; // URLs of pages with many outgoing internal links
  authorityPages: string[]; // URLs of pages with many incoming internal links
  linkDepthAnalysis: Array<{ url: string; depth: number }>; // How many clicks from homepage
  accessibilityIssues: string[]; // Pages that are hard to reach
}

export interface PageLinkData {
  url: string;
  internalLinksTo: string[]; // URLs this page links to internally
  internalLinksFrom: string[]; // URLs that link to this page internally
}

export class LinkDistributionAnalyzer {
  analyze(allPageLinkData: PageLinkData[], homepageUrl: string): LinkDistributionAnalysisResult {
    const totalInternalLinks = allPageLinkData.reduce((sum, page) => sum + page.internalLinksTo.length, 0);
    const averageLinksPerPage = allPageLinkData.length > 0 ? totalInternalLinks / allPageLinkData.length : 0;

    const orphanPages: string[] = [];
    const incomingLinkCounts: { [url: string]: number } = {};
    const outgoingLinkCounts: { [url: string]: number } = {};

    allPageLinkData.forEach(page => {
      incomingLinkCounts[page.url] = page.internalLinksFrom.length;
      outgoingLinkCounts[page.url] = page.internalLinksTo.length;
      if (page.internalLinksFrom.length === 0 && page.url !== homepageUrl) {
        orphanPages.push(page.url);
      }
    });

    // Link Equity Distribution Score (simplified: based on variance of incoming links)
    const incomingLinkValues = Object.values(incomingLinkCounts);
    const linkEquityDistributionScore = this.calculateDistributionScore(incomingLinkValues);

    // Hub Pages (many outgoing links)
    const hubPages = Object.entries(outgoingLinkCounts)
      .filter(([, count]) => count > averageLinksPerPage * 2) // Arbitrary threshold
      .map(([url]) => url);

    // Authority Pages (many incoming links)
    const authorityPages = Object.entries(incomingLinkCounts)
      .filter(([, count]) => count > averageLinksPerPage * 2) // Arbitrary threshold
      .map(([url]) => url);

    // Link Depth Analysis (BFS from homepage)
    const linkDepthAnalysis = this.analyzeLinkDepth(allPageLinkData, homepageUrl);
    const accessibilityIssues = linkDepthAnalysis.filter(d => d.depth > 3).map(d => d.url); // Pages more than 3 clicks deep

    return {
      totalInternalLinks,
      averageLinksPerPage: Number(averageLinksPerPage.toFixed(2)),
      linkEquityDistributionScore: Number(linkEquityDistributionScore.toFixed(2)),
      orphanPages,
      hubPages,
      authorityPages,
      linkDepthAnalysis,
      accessibilityIssues,
    };
  }

  private calculateDistributionScore(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    // Score is inverse of coefficient of variation (stdDev / mean)
    return mean === 0 ? 0 : (1 - (stdDev / mean)) * 100; // Higher is better (more even distribution)
  }

  private analyzeLinkDepth(allPageLinkData: PageLinkData[], homepageUrl: string): Array<{ url: string; depth: number }> {
    const depths: { [url: string]: number } = {};
    const queue: { url: string; depth: number }[] = [];
    const visited = new Set<string>();

    if (homepageUrl) {
      queue.push({ url: homepageUrl, depth: 0 });
      visited.add(homepageUrl);
      depths[homepageUrl] = 0;
    }

    let head = 0;
    while (head < queue.length) {
      const { url, depth } = queue[head++];
      const currentPageData = allPageLinkData.find(p => p.url === url);

      if (currentPageData) {
        currentPageData.internalLinksTo.forEach(linkedUrl => {
          if (!visited.has(linkedUrl)) {
            visited.add(linkedUrl);
            depths[linkedUrl] = depth + 1;
            queue.push({ url: linkedUrl, depth: depth + 1 });
          }
        });
      }
    }

    return Object.entries(depths).map(([url, depth]) => ({ url, depth }));
  }
}
