var e={methods:{one:{},two:{},three:{},four:{}},model:{one:{},two:{},three:{}},compute:{},hooks:[]};const t={compute:function(e){const{world:t}=this,n=t.compute;return"string"==typeof e&&n.hasOwnProperty(e)?n[e](this):(e=>"[object Array]"===Object.prototype.toString.call(e))(e)?e.forEach((a=>{t.compute.hasOwnProperty(a)?n[a](this):console.warn("no compute:",e)})):"function"==typeof e?e(this):console.warn("no compute:",e),this}};var n={forEach:function(e){return this.fullPointer.forEach(((t,n)=>{let a=this.update([t]);e(a,n)})),this},map:function(e,t){let n=this.fullPointer.map(((t,n)=>{let a=this.update([t]),r=e(a,n);return void 0===r?this.none():r}));if(0===n.length)return t||this.update([]);if(void 0!==n[0]){if("string"==typeof n[0])return n;if("object"==typeof n[0]&&(null===n[0]||!n[0].isView))return n}let a=[];return n.forEach((e=>{a=a.concat(e.fullPointer)})),this.toView(a)},filter:function(e){let t=this.fullPointer;return t=t.filter(((t,n)=>{let a=this.update([t]);return e(a,n)})),this.update(t)},find:function(e){let t=this.fullPointer.find(((t,n)=>{let a=this.update([t]);return e(a,n)}));return this.update([t])},some:function(e){return this.fullPointer.some(((t,n)=>{let a=this.update([t]);return e(a,n)}))},random:function(e=1){let t=this.fullPointer,n=Math.floor(Math.random()*t.length);return n+e>this.length&&(n=this.length-e,n=n<0?0:n),t=t.slice(n,n+e),this.update(t)}};const a={termList:function(){return this.methods.one.termList(this.docs)},terms:function(e){let t=this.match(".");return"number"==typeof e?t.eq(e):t},groups:function(e){if(e||0===e)return this.update(this._groups[e]||[]);let t={};return Object.keys(this._groups).forEach((e=>{t[e]=this.update(this._groups[e])})),t},eq:function(e){let t=this.pointer;return t||(t=this.docs.map(((e,t)=>[t]))),t[e]?this.update([t[e]]):this.none()},first:function(){return this.eq(0)},last:function(){let e=this.fullPointer.length-1;return this.eq(e)},firstTerms:function(){return this.match("^.")},lastTerms:function(){return this.match(".$")},slice:function(e,t){let n=this.pointer||this.docs.map(((e,t)=>[t]));return n=n.slice(e,t),this.update(n)},all:function(){return this.update().toView()},fullSentences:function(){let e=this.fullPointer.map((e=>[e[0]]));return this.update(e).toView()},none:function(){return this.update([])},isDoc:function(e){if(!e||!e.isView)return!1;let t=this.fullPointer,n=e.fullPointer;return!t.length!==n.length&&t.every(((e,t)=>!!n[t]&&(e[0]===n[t][0]&&e[1]===n[t][1]&&e[2]===n[t][2])))},wordCount:function(){return this.docs.reduce(((e,t)=>(e+=t.filter((e=>""!==e.text)).length,e)),0)},isFull:function(){let e=this.pointer;if(!e)return!0;if(0===e.length||0!==e[0][0])return!1;let t=0,n=0;return this.document.forEach((e=>t+=e.length)),this.docs.forEach((e=>n+=e.length)),t===n},getNth:function(e){return"number"==typeof e?this.eq(e):"string"==typeof e?this.if(e):this}};a.group=a.groups,a.fullSentence=a.fullSentences,a.sentence=a.fullSentences,a.lastTerm=a.lastTerms,a.firstTerm=a.firstTerms;const r=Object.assign({},a,t,n);r.get=r.eq;class View{constructor(t,n,a={}){[["document",t],["world",e],["_groups",a],["_cache",null],["viewType","View"]].forEach((e=>{Object.defineProperty(this,e[0],{value:e[1],writable:!0})})),this.ptrs=n}get docs(){let t=this.document;return this.ptrs&&(t=e.methods.one.getDoc(this.ptrs,this.document)),t}get pointer(){return this.ptrs}get methods(){return this.world.methods}get model(){return this.world.model}get hooks(){return this.world.hooks}get isView(){return!0}get found(){return this.docs.length>0}get length(){return this.docs.length}get fullPointer(){let{docs:e,ptrs:t,document:n}=this,a=t||e.map(((e,t)=>[t]));return a.map((e=>{let[t,a,r,o,i]=e;return a=a||0,r=r||(n[t]||[]).length,n[t]&&n[t][a]&&(o=o||n[t][a].id,n[t][r-1]&&(i=i||n[t][r-1].id)),[t,a,r,o,i]}))}update(e){let t=new View(this.document,e);if(this._cache&&e&&e.length>0){let n=[];e.forEach(((e,t)=>{let[a,r,o]=e;(1===e.length||0===r&&this.document[a].length===o)&&(n[t]=this._cache[a])})),n.length>0&&(t._cache=n)}return t.world=this.world,t}toView(e){return new View(this.document,e||this.pointer)}fromText(e){const{methods:t}=this;let n=t.one.tokenize.fromString(e,this.world),a=new View(n);return a.world=this.world,a.compute(["normal","freeze","lexicon"]),this.world.compute.preTagger&&a.compute("preTagger"),a.compute("unfreeze"),a}clone(){let e=this.document.slice(0);e=e.map((e=>e.map((e=>((e=Object.assign({},e)).tags=new Set(e.tags),e)))));let t=this.update(this.pointer);return t.document=e,t._cache=this._cache,t}}Object.assign(View.prototype,r);const o=function(e){return e&&"object"==typeof e&&!Array.isArray(e)};function i(e,t){if(o(t))for(const n in t)o(t[n])?(e[n]||Object.assign(e,{[n]:{}}),i(e[n],t[n])):Object.assign(e,{[n]:t[n]});return e}const s=function(e,t,n,a){const{methods:r,model:o,compute:s,hooks:l}=t;e.methods&&function(e,t){for(const n in t)e[n]=e[n]||{},Object.assign(e[n],t[n])}(r,e.methods),e.model&&i(o,e.model),e.irregulars&&function(e,t){let n=e.two.models||{};Object.keys(t).forEach((e=>{t[e].pastTense&&(n.toPast&&(n.toPast.ex[e]=t[e].pastTense),n.fromPast&&(n.fromPast.ex[t[e].pastTense]=e)),t[e].presentTense&&(n.toPresent&&(n.toPresent.ex[e]=t[e].presentTense),n.fromPresent&&(n.fromPresent.ex[t[e].presentTense]=e)),t[e].gerund&&(n.toGerund&&(n.toGerund.ex[e]=t[e].gerund),n.fromGerund&&(n.fromGerund.ex[t[e].gerund]=e)),t[e].comparative&&(n.toComparative&&(n.toComparative.ex[e]=t[e].comparative),n.fromComparative&&(n.fromComparative.ex[t[e].comparative]=e)),t[e].superlative&&(n.toSuperlative&&(n.toSuperlative.ex[e]=t[e].superlative),n.fromSuperlative&&(n.fromSuperlative.ex[t[e].superlative]=e))}))}(o,e.irregulars),e.compute&&Object.assign(s,e.compute),l&&(t.hooks=l.concat(e.hooks||[])),e.api&&e.api(n),e.lib&&Object.keys(e.lib).forEach((t=>a[t]=e.lib[t])),e.tags&&a.addTags(e.tags),e.words&&a.addWords(e.words),e.frozen&&a.addWords(e.frozen,!0),e.mutate&&e.mutate(t,a)},l=function(e){return"[object Array]"===Object.prototype.toString.call(e)},u=function(e,t,n){const{methods:a}=n;let r=new t([]);if(r.world=n,"number"==typeof e&&(e=String(e)),!e)return r;if("string"==typeof e){return new t(a.one.tokenize.fromString(e,n))}if(o=e,"[object Object]"===Object.prototype.toString.call(o)&&e.isView)return new t(e.document,e.ptrs);var o;if(l(e)){if(l(e[0])){let n=e.map((e=>e.map((e=>({text:e,normal:e,pre:"",post:" ",tags:new Set})))));return new t(n)}let n=e.map((e=>e.terms.map((e=>(l(e.tags)&&(e.tags=new Set(e.tags)),e)))));return new t(n)}return r};let c=Object.assign({},e);const d=function(e,t){t&&d.addWords(t);let n=u(e,View,c);return e&&n.compute(c.hooks),n};Object.defineProperty(d,"_world",{value:c,writable:!0}),d.tokenize=function(e,t){const{compute:n}=this._world;t&&d.addWords(t);let a=u(e,View,c);return n.contractions&&a.compute(["alias","normal","machine","contractions"]),a},d.plugin=function(e){return s(e,this._world,View,this),this},d.extend=d.plugin,d.world=function(){return this._world},d.model=function(){return this._world.model},d.methods=function(){return this._world.methods},d.hooks=function(){return this._world.hooks},d.verbose=function(e){const t="undefined"!=typeof process&&process.env?process.env:self.env||{};return t.DEBUG_TAGS="tagger"===e||!0===e||"",t.DEBUG_MATCH="match"===e||!0===e||"",t.DEBUG_CHUNKS="chunker"===e||!0===e||"",this},d.version="14.14.4";var h={one:{cacheDoc:function(e){let t=e.map((e=>{let t=new Set;return e.forEach((e=>{""!==e.normal&&t.add(e.normal),e.switch&&t.add(`%${e.switch}%`),e.implicit&&t.add(e.implicit),e.machine&&t.add(e.machine),e.root&&t.add(e.root),e.alias&&e.alias.forEach((e=>t.add(e)));let n=Array.from(e.tags);for(let e=0;e<n.length;e+=1)t.add("#"+n[e])})),t}));return t}}};const m={cache:function(){return this._cache=this.methods.one.cacheDoc(this.document),this},uncache:function(){return this._cache=null,this}};var p={api:function(e){Object.assign(e.prototype,m)},compute:{cache:function(e){e._cache=e.methods.one.cacheDoc(e.document)}},methods:h};const f=e=>/^\p{Lu}[\p{Ll}'’]/u.test(e)||/^\p{Lu}$/u.test(e),b=(e,t,n)=>{if(n.forEach((e=>e.dirty=!0)),e){let a=[t,0].concat(n);Array.prototype.splice.apply(e,a)}return e},y=function(e){let t=e[e.length-1];!t||/ $/.test(t.post)||/[-–—]/.test(t.post)||(t.post+=" ")},v=(e,t,n)=>{const a=/[-.?!,;:)–—'"]/g;let r=e[t-1];if(!r)return;let o=r.post;if(a.test(o)){let e=o.match(a).join(""),t=n[n.length-1];t.post=e+t.post,r.post=r.post.replace(a,"")}},w=function(e,t,n,a){let[r,o,i]=t;0===o||i===a[r].length?y(n):(y(n),y([e[t[1]]])),function(e,t,n){let a=e[t];if(0!==t||!f(a.text))return;n[0].text=n[0].text.replace(/^\p{Ll}/u,(e=>e.toUpperCase()));let r=e[t];r.tags.has("ProperNoun")||r.tags.has("Acronym")||f(r.text)&&r.text.length>1&&(r.text=(o=r.text,o.replace(/^\p{Lu}/u,(e=>e.toLowerCase()))));var o}(e,o,n),b(e,o,n)};let k=0;const P=e=>(e=e.length<3?"0"+e:e).length<3?"0"+e:e,A=function(e){let[t,n]=e.index||[0,0];k+=1,k=k>46655?0:k,t=t>46655?0:t,n=n>1294?0:n;let a=P(k.toString(36));a+=P(t.toString(36));let r=n.toString(36);return r=r.length<2?"0"+r:r,a+=r,a+=parseInt(36*Math.random(),10).toString(36),e.normal+"|"+a.toUpperCase()},C=function(e){if(e.has("@hasContraction")&&"function"==typeof e.contractions){e.grow("@hasContraction").contractions().expand()}},j=e=>"[object Array]"===Object.prototype.toString.call(e),N=function(e,t,n){const{document:a,world:r}=t;t.uncache();let o=t.fullPointer,i=t.fullPointer;t.forEach(((s,l)=>{let u=s.fullPointer[0],[c]=u,d=a[c],h=function(e,t){const{methods:n}=t;return"string"==typeof e?n.one.tokenize.fromString(e,t)[0]:"object"==typeof e&&e.isView?e.clone().docs[0]||[]:j(e)?j(e[0])?e[0]:e:[]}(e,r);0!==h.length&&(h=function(e){return e.map((e=>(e.id=A(e),e)))}(h),n?(C(t.update([u]).firstTerm()),w(d,u,h,a)):(C(t.update([u]).lastTerm()),function(e,t,n,a){let[r,,o]=t,i=(a[r]||[]).length;o<i?(v(e,o,n),y(n)):i===o&&(y(e),v(e,o,n),a[r+1]&&(n[n.length-1].post+=" ")),b(e,t[2],n),t[4]=n[n.length-1].id}(d,u,h,a)),a[c]&&a[c][u[1]]&&(u[3]=a[c][u[1]].id),i[l]=u,u[2]+=h.length,o[l]=u)}));let s=t.toView(o);return t.ptrs=i,s.compute(["id","index","freeze","lexicon"]),s.world.compute.preTagger&&s.compute("preTagger"),s.compute("unfreeze"),s},I={insertAfter:function(e){return N(e,this,!1)},insertBefore:function(e){return N(e,this,!0)}};I.append=I.insertAfter,I.prepend=I.insertBefore,I.insert=I.insertAfter;const D=/\$[0-9a-z]+/g,H={},G=e=>e.replace(/^\p{Ll}/u,(e=>e.toUpperCase())),T=e=>e.replace(/^\p{Lu}/u,(e=>e.toLowerCase()));H.replaceWith=function(e,t={}){let n=this.fullPointer,a=this;if(this.uncache(),"function"==typeof e)return function(e,t,n){return e.forEach((e=>{let a=t(e);e.replaceWith(a,n)})),e}(a,e,t);let r=a.docs[0];if(!r)return a;let o=t.possessives&&r[r.length-1].tags.has("Possessive"),i=t.case&&(s=r[0].text,/^\p{Lu}[\p{Ll}'’]/u.test(s)||/^\p{Lu}$/u.test(s));var s;e=function(e,t){if("string"!=typeof e)return e;let n=t.groups();return e=e.replace(D,(e=>{let t=e.replace(/\$/,"");return n.hasOwnProperty(t)?n[t].text():e})),e}(e,a);let l=this.update(n);n=n.map((e=>e.slice(0,3)));let u=(l.docs[0]||[]).map((e=>Array.from(e.tags))),c=l.docs[0][0].pre,d=l.docs[0][l.docs[0].length-1].post;if("string"==typeof e&&(e=this.fromText(e).compute("id")),a.insertAfter(e),l.has("@hasContraction")&&a.contractions){a.grow("@hasContraction+").contractions().expand()}if(a.delete(l),o){let e=a.docs[0],t=e[e.length-1];t.tags.has("Possessive")||(t.text+="'s",t.normal+="'s",t.tags.add("Possessive"))}if(c&&a.docs[0]&&(a.docs[0][0].pre=c),d&&a.docs[0]){let e=a.docs[0][a.docs[0].length-1];e.post.trim()||(e.post=d)}let h=a.toView(n).compute(["index","freeze","lexicon"]);if(h.world.compute.preTagger&&h.compute("preTagger"),h.compute("unfreeze"),t.tags&&h.terms().forEach(((e,t)=>{e.tagSafe(u[t])})),!h.docs[0]||!h.docs[0][0])return h;if(t.case){let e=i?G:T;h.docs[0][0].text=e(h.docs[0][0].text)}return h},H.replace=function(e,t,n){if(e&&!t)return this.replaceWith(e,n);let a=this.match(e);return a.found?(this.soften(),a.replaceWith(t,n)):this};const x={remove:function(e){const{indexN:t}=this.methods.one.pointer;this.uncache();let n=this.all(),a=this;e&&(n=this,a=this.match(e));let r=!n.ptrs;if(a.has("@hasContraction")&&a.contractions){a.grow("@hasContraction").contractions().expand()}let o=n.fullPointer,i=a.fullPointer.reverse(),s=function(e,t){t.forEach((t=>{let[n,a,r]=t,o=r-a;e[n]&&(r===e[n].length&&r>1&&function(e,t){let n=e.length-1,a=e[n],r=e[n-t];r&&a&&(r.post+=a.post,r.post=r.post.replace(/ +([.?!,;:])/,"$1"),r.post=r.post.replace(/[,;:]+([.?!])/,"$1"))}(e[n],o),e[n].splice(a,o))}));for(let t=e.length-1;t>=0;t-=1)if(0===e[t].length&&(e.splice(t,1),t===e.length&&e[t-1])){let n=e[t-1],a=n[n.length-1];a&&(a.post=a.post.trimEnd())}return e}(this.document,i);return o=function(e,t){return e=e.map((e=>{let[n]=e;return t[n]?(t[n].forEach((t=>{let n=t[2]-t[1];e[1]<=t[1]&&e[2]>=t[2]&&(e[2]-=n)})),e):e})),e.forEach(((t,n)=>{if(0===t[1]&&0==t[2])for(let t=n+1;t<e.length;t+=1)e[t][0]-=1,e[t][0]<0&&(e[t][0]=0)})),e=(e=e.filter((e=>e[2]-e[1]>0))).map((e=>(e[3]=null,e[4]=null,e)))}(o,t(i)),n.ptrs=o,n.document=s,n.compute("index"),r&&(n.ptrs=void 0),e?n.toView(o):(this.ptrs=[],n.none())}};x.delete=x.remove;const E={pre:function(e,t){return void 0===e&&this.found?this.docs[0][0].pre:(this.docs.forEach((n=>{let a=n[0];!0===t?a.pre+=e:a.pre=e})),this)},post:function(e,t){if(void 0===e){let e=this.docs[this.docs.length-1];return e[e.length-1].post}return this.docs.forEach((n=>{let a=n[n.length-1];!0===t?a.post+=e:a.post=e})),this},trim:function(){if(!this.found)return this;let e=this.docs,t=e[0][0];t.pre=t.pre.trimStart();let n=e[e.length-1],a=n[n.length-1];return a.post=a.post.trimEnd(),this},hyphenate:function(){return this.docs.forEach((e=>{e.forEach(((t,n)=>{0!==n&&(t.pre=""),e[n+1]&&(t.post="-")}))})),this},dehyphenate:function(){const e=/[-–—]/;return this.docs.forEach((t=>{t.forEach((t=>{e.test(t.post)&&(t.post=" ")}))})),this},toQuotations:function(e,t){return e=e||'"',t=t||'"',this.docs.forEach((n=>{n[0].pre=e+n[0].pre;let a=n[n.length-1];a.post=t+a.post})),this},toParentheses:function(e,t){return e=e||"(",t=t||")",this.docs.forEach((n=>{n[0].pre=e+n[0].pre;let a=n[n.length-1];a.post=t+a.post})),this}};E.deHyphenate=E.dehyphenate,E.toQuotation=E.toQuotations;var F={alpha:(e,t)=>e.normal<t.normal?-1:e.normal>t.normal?1:0,length:(e,t)=>{let n=e.normal.trim().length,a=t.normal.trim().length;return n<a?1:n>a?-1:0},wordCount:(e,t)=>e.words<t.words?1:e.words>t.words?-1:0,sequential:(e,t)=>e[0]<t[0]?1:e[0]>t[0]?-1:e[1]>t[1]?1:-1,byFreq:function(e){let t={};return e.forEach((e=>{t[e.normal]=t[e.normal]||0,t[e.normal]+=1})),e.sort(((e,n)=>{let a=t[e.normal],r=t[n.normal];return a<r?1:a>r?-1:0})),e}};const O=new Set(["index","sequence","seq","sequential","chron","chronological"]),z=new Set(["freq","frequency","topk","repeats"]),V=new Set(["alpha","alphabetical"]);var B={unique:function(){let e=new Set,t=this.filter((t=>{let n=t.text("machine");return!e.has(n)&&(e.add(n),!0)}));return t},reverse:function(){let e=this.pointer||this.docs.map(((e,t)=>[t]));return e=[].concat(e),e=e.reverse(),this._cache&&(this._cache=this._cache.reverse()),this.update(e)},sort:function(e){let{docs:t,pointer:n}=this;if(this.uncache(),"function"==typeof e)return function(e,t){let n=e.fullPointer;return n=n.sort(((n,a)=>(n=e.update([n]),a=e.update([a]),t(n,a)))),e.ptrs=n,e}(this,e);e=e||"alpha";let a=n||t.map(((e,t)=>[t])),r=t.map(((e,t)=>({index:t,words:e.length,normal:e.map((e=>e.machine||e.normal||"")).join(" "),pointer:a[t]})));return O.has(e)&&(e="sequential"),V.has(e)&&(e="alpha"),z.has(e)?(r=F.byFreq(r),this.update(r.map((e=>e.pointer)))):"function"==typeof F[e]?(r=r.sort(F[e]),this.update(r.map((e=>e.pointer)))):this}};const S=function(e,t){if(e.length>0){let t=e[e.length-1],n=t[t.length-1];!1===/ /.test(n.post)&&(n.post+=" ")}return e=e.concat(t)};var K={concat:function(e){if("string"==typeof e){let t=this.fromText(e);if(this.found&&this.ptrs){let e=this.fullPointer,n=e[e.length-1][0];this.document.splice(n,0,...t.document)}else this.document=this.document.concat(t.document);return this.all().compute("index")}if("object"==typeof e&&e.isView)return function(e,t){if(e.document===t.document){let n=e.fullPointer.concat(t.fullPointer);return e.toView(n).compute("index")}return t.fullPointer.forEach((t=>{t[0]+=e.document.length})),e.document=S(e.document,t.docs),e.all()}(this,e);if(t=e,"[object Array]"===Object.prototype.toString.call(t)){let t=S(this.document,e);return this.document=t,this.all()}var t;return this}};var $={harden:function(){return this.ptrs=this.fullPointer,this},soften:function(){let e=this.ptrs;return!e||e.length<1||(e=e.map((e=>e.slice(0,3))),this.ptrs=e),this}};const L=Object.assign({},{toLowerCase:function(){return this.termList().forEach((e=>{e.text=e.text.toLowerCase()})),this},toUpperCase:function(){return this.termList().forEach((e=>{e.text=e.text.toUpperCase()})),this},toTitleCase:function(){return this.termList().forEach((e=>{e.text=e.text.replace(/^ *[a-z\u00C0-\u00FF]/,(e=>e.toUpperCase()))})),this},toCamelCase:function(){return this.docs.forEach((e=>{e.forEach(((t,n)=>{0!==n&&(t.text=t.text.replace(/^ *[a-z\u00C0-\u00FF]/,(e=>e.toUpperCase()))),n!==e.length-1&&(t.post="")}))})),this}},I,H,x,E,B,K,$),M={id:function(e){let t=e.docs;for(let e=0;e<t.length;e+=1)for(let n=0;n<t[e].length;n+=1){let a=t[e][n];a.id=a.id||A(a)}}};var J={api:function(e){Object.assign(e.prototype,L)},compute:M};const W=!0;var U={one:{contractions:[{word:"@",out:["at"]},{word:"arent",out:["are","not"]},{word:"alot",out:["a","lot"]},{word:"brb",out:["be","right","back"]},{word:"cannot",out:["can","not"]},{word:"dun",out:["do","not"]},{word:"can't",out:["can","not"]},{word:"shan't",out:["should","not"]},{word:"won't",out:["will","not"]},{word:"that's",out:["that","is"]},{word:"what's",out:["what","is"]},{word:"let's",out:["let","us"]},{word:"dunno",out:["do","not","know"]},{word:"gonna",out:["going","to"]},{word:"gotta",out:["have","got","to"]},{word:"gimme",out:["give","me"]},{word:"outta",out:["out","of"]},{word:"tryna",out:["trying","to"]},{word:"gtg",out:["got","to","go"]},{word:"im",out:["i","am"]},{word:"imma",out:["I","will"]},{word:"imo",out:["in","my","opinion"]},{word:"irl",out:["in","real","life"]},{word:"ive",out:["i","have"]},{word:"rn",out:["right","now"]},{word:"tbh",out:["to","be","honest"]},{word:"wanna",out:["want","to"]},{word:"c'mere",out:["come","here"]},{word:"c'mon",out:["come","on"]},{word:"shoulda",out:["should","have"]},{word:"coulda",out:["coulda","have"]},{word:"woulda",out:["woulda","have"]},{word:"musta",out:["must","have"]},{word:"tis",out:["it","is"]},{word:"twas",out:["it","was"]},{word:"y'know",out:["you","know"]},{word:"ne'er",out:["never"]},{word:"o'er",out:["over"]},{after:"ll",out:["will"]},{after:"ve",out:["have"]},{after:"re",out:["are"]},{after:"m",out:["am"]},{before:"c",out:["ce"]},{before:"m",out:["me"]},{before:"n",out:["ne"]},{before:"qu",out:["que"]},{before:"s",out:["se"]},{before:"t",out:["tu"]},{word:"shouldnt",out:["should","not"]},{word:"couldnt",out:["could","not"]},{word:"wouldnt",out:["would","not"]},{word:"hasnt",out:["has","not"]},{word:"wasnt",out:["was","not"]},{word:"isnt",out:["is","not"]},{word:"cant",out:["can","not"]},{word:"dont",out:["do","not"]},{word:"wont",out:["will","not"]},{word:"howd",out:["how","did"]},{word:"whatd",out:["what","did"]},{word:"whend",out:["when","did"]},{word:"whered",out:["where","did"]}],numberSuffixes:{st:W,nd:W,rd:W,th:W,am:W,pm:W,max:W,"°":W,s:W,e:W,er:W,"ère":W,"ème":W}}};const q=function(e,t,n){let[a,r]=t;n&&0!==n.length&&(n=n.map(((e,t)=>(e.implicit=e.text,e.machine=e.text,e.pre="",e.post="",e.text="",e.normal="",e.index=[a,r+t],e))),n[0]&&(n[0].pre=e[a][r].pre,n[n.length-1].post=e[a][r].post,n[0].text=e[a][r].text,n[0].normal=e[a][r].normal),e[a].splice(r,1,...n))},R=/'/,Q=new Set(["what","how","when","where","why"]),Z=new Set(["be","go","start","think","need"]),X=new Set(["been","gone"]),_=/'/,Y=/(e|é|aison|sion|tion)$/,ee=/(age|isme|acle|ege|oire)$/;var te=(e,t)=>["je",e[t].normal.split(_)[1]],ne=(e,t)=>{let n=e[t].normal.split(_)[1];return n&&n.endsWith("e")?["la",n]:["le",n]},ae=(e,t)=>{let n=e[t].normal.split(_)[1];return n&&Y.test(n)&&!ee.test(n)?["du",n]:n&&n.endsWith("s")?["des",n]:["de",n]};const re=/^([0-9.]{1,4}[a-z]{0,2}) ?[-–—] ?([0-9]{1,4}[a-z]{0,2})$/i,oe=/^([0-9]{1,2}(:[0-9][0-9])?(am|pm)?) ?[-–—] ?([0-9]{1,2}(:[0-9][0-9])?(am|pm)?)$/i,ie=/^[0-9]{3}-[0-9]{4}$/,se=function(e,t){let n=e[t],a=n.text.match(re);return null!==a?!0===n.tags.has("PhoneNumber")||ie.test(n.text)?null:[a[1],"to",a[2]]:(a=n.text.match(oe),null!==a?[a[1],"to",a[4]]:null)},le=/^([+-]?[0-9][.,0-9]*)([a-z°²³µ/]+)$/,ue=function(e,t,n){const a=n.model.one.numberSuffixes||{};let r=e[t].text.match(le);if(null!==r){let e=r[2].toLowerCase().trim();return a.hasOwnProperty(e)?null:[r[1],e]}return null},ce=/'/,de=/^[0-9][^-–—]*[-–—].*?[0-9]/,he=function(e,t,n,a){let r=t.update();r.document=[e];let o=n+a;n>0&&(n-=1),e[o]&&(o+=1),r.ptrs=[[0,n,o]]},ge={t:(e,t)=>function(e,t){return"ain't"===e[t].normal||"aint"===e[t].normal?null:[e[t].normal.replace(/n't/,""),"not"]}(e,t),d:(e,t)=>function(e,t){let n=e[t].normal.split(R)[0];if(Q.has(n))return[n,"did"];if(e[t+1]){if(X.has(e[t+1].normal))return[n,"had"];if(Z.has(e[t+1].normal))return[n,"would"]}return null}(e,t)},me={j:(e,t)=>te(e,t),l:(e,t)=>ne(e,t),d:(e,t)=>ae(e,t)},pe=function(e,t,n,a){for(let r=0;r<e.length;r+=1){let o=e[r];if(o.word===t.normal)return o.out;if(null!==a&&a===o.after)return[n].concat(o.out);if(null!==n&&n===o.before&&a&&a.length>2)return o.out.concat(a)}return null},fe=function(e,t){let n=t.fromText(e.join(" "));return n.compute(["id","alias"]),n.docs[0]},be=function(e,t){for(let n=t+1;n<5&&e[n];n+=1)if("been"===e[n].normal)return["there","has"];return["there","is"]};var ye={contractions:e=>{let{world:t,document:n}=e;const{model:a,methods:r}=t;let o=a.one.contractions||[];n.forEach(((a,i)=>{for(let s=a.length-1;s>=0;s-=1){let l=null,u=null;if(!0===ce.test(a[s].normal)){let e=a[s].normal.split(ce);l=e[0],u=e[1]}let c=pe(o,a[s],l,u);!c&&ge.hasOwnProperty(u)&&(c=ge[u](a,s,t)),!c&&me.hasOwnProperty(l)&&(c=me[l](a,s)),"there"===l&&"s"===u&&(c=be(a,s)),c?(c=fe(c,e),q(n,[i,s],c),he(n[i],e,s,c.length)):de.test(a[s].normal)?(c=se(a,s),c&&(c=fe(c,e),q(n,[i,s],c),r.one.setTag(c,"NumberRange",t),c[2]&&c[2].tags.has("Time")&&r.one.setTag([c[0]],"Time",t,null,"time-range"),he(n[i],e,s,c.length))):(c=ue(a,s,t),c&&(c=fe(c,e),q(n,[i,s],c),r.one.setTag([c[1]],"Unit",t,null,"contraction-unit")))}}))}};const ve={model:U,compute:ye,hooks:["contractions"]},we=function(e){const t=e.world,{model:n,methods:a}=e.world,r=a.one.setTag,{frozenLex:o}=n.one,i=n.one._multiCache||{};e.docs.forEach((e=>{for(let n=0;n<e.length;n+=1){let a=e[n],s=a.machine||a.normal;if(void 0!==i[s]&&e[n+1]){for(let a=n+i[s]-1;a>n;a-=1){let i=e.slice(n,a+1),s=i.map((e=>e.machine||e.normal)).join(" ");!0!==o.hasOwnProperty(s)||(r(i,o[s],t,!1,"1-frozen-multi-lexicon"),i.forEach((e=>e.frozen=!0)))}}void 0!==o[s]&&o.hasOwnProperty(s)&&(r([a],o[s],t,!1,"1-freeze-lexicon"),a.frozen=!0)}}))};const ke=e=>"[34m"+e+"[0m",Pe=e=>"[3m[2m"+e+"[0m",Ae=function(e){e.docs.forEach((e=>{console.log(ke("\n  ┌─────────")),e.forEach((e=>{let t=`  ${Pe("│")}  `,n=e.implicit||e.text||"-";!0===e.frozen?t+=`${ke(n)} ❄️`:t+=Pe(n),console.log(t)}))}))};var Ce={compute:{frozen:we,freeze:we,unfreeze:function(e){return e.docs.forEach((e=>{e.forEach((e=>{delete e.frozen}))})),e}},mutate:e=>{const t=e.methods.one;t.termMethods.isFrozen=e=>!0===e.frozen,t.debug.freeze=Ae,t.debug.frozen=Ae},api:function(e){e.prototype.freeze=function(){return this.docs.forEach((e=>{e.forEach((e=>{e.frozen=!0}))})),this},e.prototype.unfreeze=function(){this.compute("unfreeze")},e.prototype.isFrozen=function(){return this.match("@isFrozen+")}},hooks:["freeze"]};const je=function(e,t,n){const{model:a,methods:r}=n,o=r.one.setTag,i=a.one._multiCache||{},{lexicon:s}=a.one||{};let l=e[t],u=l.machine||l.normal;if(void 0!==i[u]&&e[t+1]){for(let a=t+i[u]-1;a>t;a-=1){let r=e.slice(t,a+1);if(r.length<=1)return!1;let i=r.map((e=>e.machine||e.normal)).join(" ");if(!0===s.hasOwnProperty(i)){let e=s[i];return o(r,e,n,!1,"1-multi-lexicon"),!e||2!==e.length||"PhrasalVerb"!==e[0]&&"PhrasalVerb"!==e[1]||o([r[1]],"Particle",n,!1,"1-phrasal-particle"),!0}}return!1}return null},Ne=/^(under|over|mis|re|un|dis|semi|pre|post)-?/,Ie=new Set(["Verb","Infinitive","PastTense","Gerund","PresentTense","Adjective","Participle"]),De=function(e,t,n){const{model:a,methods:r}=n,o=r.one.setTag,{lexicon:i}=a.one;let s=e[t],l=s.machine||s.normal;if(void 0!==i[l]&&i.hasOwnProperty(l))return o([s],i[l],n,!1,"1-lexicon"),!0;if(s.alias){let e=s.alias.find((e=>i.hasOwnProperty(e)));if(e)return o([s],i[e],n,!1,"1-lexicon-alias"),!0}if(!0===Ne.test(l)){let e=l.replace(Ne,"");if(i.hasOwnProperty(e)&&e.length>3&&Ie.has(i[e]))return o([s],i[e],n,!1,"1-lexicon-prefix"),!0}return null};var He={lexicon:function(e){const t=e.world;e.docs.forEach((e=>{for(let n=0;n<e.length;n+=1)if(0===e[n].tags.size){let a=null;a=a||je(e,n,t),a=a||De(e,n,t)}}))}};var Ge={one:{expandLexicon:function(e){let t={},n={};return Object.keys(e).forEach((a=>{let r=e[a],o=(a=(a=a.toLowerCase().trim()).replace(/'s\b/,"")).split(/ /);o.length>1&&(void 0===n[o[0]]||o.length>n[o[0]])&&(n[o[0]]=o.length),t[a]=t[a]||r})),delete t[""],delete t.null,delete t[" "],{lex:t,_multi:n}}}};var Te={addWords:function(e,t=!1){const n=this.world(),{methods:a,model:r}=n;if(!e)return;if(Object.keys(e).forEach((t=>{"string"==typeof e[t]&&e[t].startsWith("#")&&(e[t]=e[t].replace(/^#/,""))})),!0===t){let{lex:t,_multi:o}=a.one.expandLexicon(e,n);return Object.assign(r.one._multiCache,o),void Object.assign(r.one.frozenLex,t)}if(a.two.expandLexicon){let{lex:t,_multi:o}=a.two.expandLexicon(e,n);Object.assign(r.one.lexicon,t),Object.assign(r.one._multiCache,o)}let{lex:o,_multi:i}=a.one.expandLexicon(e,n);Object.assign(r.one.lexicon,o),Object.assign(r.one._multiCache,i)}};var xe={model:{one:{lexicon:{},_multiCache:{},frozenLex:{}}},methods:Ge,compute:He,lib:Te,hooks:["lexicon"]};const Ee=function(e,t){let n=[{}],a=[null],r=[0],o=[],i=0;e.forEach((function(e){let r=0,o=function(e,t){const{methods:n,model:a}=t;let r=n.one.tokenize.splitTerms(e,a).map((e=>n.one.tokenize.splitWhitespace(e,a)));return r.map((e=>e.text.toLowerCase()))}(e,t);for(let e=0;e<o.length;e++){let t=o[e];n[r]&&n[r].hasOwnProperty(t)?r=n[r][t]:(i++,n[r][t]=i,n[i]={},r=i,a[i]=null)}a[r]=[o.length]}));for(let e in n[0])i=n[0][e],r[i]=0,o.push(i);for(;o.length;){let e=o.shift(),t=Object.keys(n[e]);for(let s=0;s<t.length;s+=1){let l=t[s],u=n[e][l];for(o.push(u),i=r[e];i>0&&!n[i].hasOwnProperty(l);)i=r[i];if(n.hasOwnProperty(i)){let e=n[i][l];r[u]=e,a[e]&&(a[u]=a[u]||[],a[u]=a[u].concat(a[e]))}else r[u]=0}}return{goNext:n,endAs:a,failTo:r}},Fe=function(e,t,n){let a=0,r=[];for(let o=0;o<e.length;o++){let i=e[o][n.form]||e[o].normal;for(;a>0&&(void 0===t.goNext[a]||!t.goNext[a].hasOwnProperty(i));)a=t.failTo[a]||0;if(t.goNext[a].hasOwnProperty(i)&&(a=t.goNext[a][i],t.endAs[a])){let n=t.endAs[a];for(let t=0;t<n.length;t++){let a=n[t],i=e[o-a+1],[s,l]=i.index;r.push([s,l,l+a,i.id])}}}return r},Oe=function(e,t){for(let n=0;n<e.length;n+=1)if(!0===t.has(e[n]))return!1;return!0};const ze=(e,t)=>{for(let n=e.length-1;n>=0;n-=1)if(e[n]!==t)return e=e.slice(0,n+1);return e},Ve={buildTrie:function(e){return function(e){return e.goNext=e.goNext.map((e=>{if(0!==Object.keys(e).length)return e})),e.goNext=ze(e.goNext,void 0),e.failTo=ze(e.failTo,0),e.endAs=ze(e.endAs,null),e}(Ee(e,this.world()))}};Ve.compile=Ve.buildTrie;var Be={api:function(e){e.prototype.lookup=function(e,t={}){if(!e)return this.none();var n;"string"==typeof e&&(e=[e]);let a=function(e,t,n){let a=[];n.form=n.form||"normal";let r=e.docs;if(!t.goNext||!t.goNext[0])return console.error("Compromise invalid lookup trie"),e.none();let o=Object.keys(t.goNext[0]);for(let i=0;i<r.length;i++){if(e._cache&&e._cache[i]&&!0===Oe(o,e._cache[i]))continue;let s=r[i],l=Fe(s,t,n);l.length>0&&(a=a.concat(l))}return e.update(a)}(this,(n=e,"[object Object]"===Object.prototype.toString.call(n)?e:Ee(e,this.world)),t);return a=a.settle(),a}},lib:Ve};const Se=function(e,t){return t?(e.forEach((e=>{let n=e[0];t[n]&&(e[0]=t[n][0],e[1]+=t[n][1],e[2]+=t[n][1])})),e):e},Ke=function(e,t){let{ptrs:n,byGroup:a}=e;return n=Se(n,t),Object.keys(a).forEach((e=>{a[e]=Se(a[e],t)})),{ptrs:n,byGroup:a}},$e=function(e,t,n){const a=n.methods.one;return"number"==typeof e&&(e=String(e)),"string"==typeof e&&(e=a.killUnicode(e,n),e=a.parseMatch(e,t,n)),e},Le=e=>"[object Object]"===Object.prototype.toString.call(e),Me=e=>e&&Le(e)&&!0===e.isView,Je=e=>e&&Le(e)&&!0===e.isNet;var We={matchOne:function(e,t,n){const a=this.methods.one;if(Me(e))return this.intersection(e).eq(0);if(Je(e))return this.sweep(e,{tagger:!1,matchOne:!0}).view;let r={regs:e=$e(e,n,this.world),group:t,justOne:!0},o=a.match(this.docs,r,this._cache),{ptrs:i,byGroup:s}=Ke(o,this.fullPointer),l=this.toView(i);return l._groups=s,l},match:function(e,t,n){const a=this.methods.one;if(Me(e))return this.intersection(e);if(Je(e))return this.sweep(e,{tagger:!1}).view.settle();let r={regs:e=$e(e,n,this.world),group:t},o=a.match(this.docs,r,this._cache),{ptrs:i,byGroup:s}=Ke(o,this.fullPointer),l=this.toView(i);return l._groups=s,l},has:function(e,t,n){const a=this.methods.one;if(Me(e)){return this.intersection(e).fullPointer.length>0}if(Je(e))return this.sweep(e,{tagger:!1}).view.found;let r={regs:e=$e(e,n,this.world),group:t,justOne:!0};return a.match(this.docs,r,this._cache).ptrs.length>0},if:function(e,t,n){const a=this.methods.one;if(Me(e))return this.filter((t=>t.intersection(e).found));if(Je(e)){let t=this.sweep(e,{tagger:!1}).view.settle();return this.if(t)}let r={regs:e=$e(e,n,this.world),group:t,justOne:!0},o=this.fullPointer,i=this._cache||[];o=o.filter(((e,t)=>{let n=this.update([e]);return a.match(n.docs,r,i[t]).ptrs.length>0}));let s=this.update(o);return this._cache&&(s._cache=o.map((e=>i[e[0]]))),s},ifNo:function(e,t,n){const{methods:a}=this,r=a.one;if(Me(e))return this.filter((t=>!t.intersection(e).found));if(Je(e)){let t=this.sweep(e,{tagger:!1}).view.settle();return this.ifNo(t)}e=$e(e,n,this.world);let o=this._cache||[],i=this.filter(((n,a)=>{let i={regs:e,group:t,justOne:!0};return 0===r.match(n.docs,i,o[a]).ptrs.length}));return this._cache&&(i._cache=i.ptrs.map((e=>o[e[0]]))),i}};var Ue={before:function(e,t,n){const{indexN:a}=this.methods.one.pointer;let r=[],o=a(this.fullPointer);Object.keys(o).forEach((e=>{let t=o[e].sort(((e,t)=>e[1]>t[1]?1:-1))[0];t[1]>0&&r.push([t[0],0,t[1]])}));let i=this.toView(r);return e?i.match(e,t,n):i},after:function(e,t,n){const{indexN:a}=this.methods.one.pointer;let r=[],o=a(this.fullPointer),i=this.document;Object.keys(o).forEach((e=>{let t=o[e].sort(((e,t)=>e[1]>t[1]?-1:1))[0],[n,,a]=t;a<i[n].length&&r.push([n,a,i[n].length])}));let s=this.toView(r);return e?s.match(e,t,n):s},growLeft:function(e,t,n){"string"==typeof e&&(e=this.world.methods.one.parseMatch(e,n,this.world)),e[e.length-1].end=!0;let a=this.fullPointer;return this.forEach(((n,r)=>{let o=n.before(e,t);if(o.found){let e=o.terms();a[r][1]-=e.length,a[r][3]=e.docs[0][0].id}})),this.update(a)},growRight:function(e,t,n){"string"==typeof e&&(e=this.world.methods.one.parseMatch(e,n,this.world)),e[0].start=!0;let a=this.fullPointer;return this.forEach(((n,r)=>{let o=n.after(e,t);if(o.found){let e=o.terms();a[r][2]+=e.length,a[r][4]=null}})),this.update(a)},grow:function(e,t,n){return this.growRight(e,t,n).growLeft(e,t,n)}};const qe=function(e,t){return[e[0],e[1],t[2]]},Re=(e,t,n)=>{return"string"==typeof e||(a=e,"[object Array]"===Object.prototype.toString.call(a))?t.match(e,n):e||t.none();var a},Qe=function(e,t){let[n,a,r]=e;return t.document[n]&&t.document[n][a]&&(e[3]=e[3]||t.document[n][a].id,t.document[n][r-1]&&(e[4]=e[4]||t.document[n][r-1].id)),e},Ze={splitOn:function(e,t){const{splitAll:n}=this.methods.one.pointer;let a=Re(e,this,t).fullPointer,r=n(this.fullPointer,a),o=[];return r.forEach((e=>{o.push(e.passthrough),o.push(e.before),o.push(e.match),o.push(e.after)})),o=o.filter((e=>e)),o=o.map((e=>Qe(e,this))),this.update(o)},splitBefore:function(e,t){const{splitAll:n}=this.methods.one.pointer;let a=Re(e,this,t).fullPointer,r=n(this.fullPointer,a);for(let e=0;e<r.length;e+=1)!r[e].after&&r[e+1]&&r[e+1].before&&r[e].match&&r[e].match[0]===r[e+1].before[0]&&(r[e].after=r[e+1].before,delete r[e+1].before);let o=[];return r.forEach((e=>{o.push(e.passthrough),o.push(e.before),e.match&&e.after?o.push(qe(e.match,e.after)):o.push(e.match)})),o=o.filter((e=>e)),o=o.map((e=>Qe(e,this))),this.update(o)},splitAfter:function(e,t){const{splitAll:n}=this.methods.one.pointer;let a=Re(e,this,t).fullPointer,r=n(this.fullPointer,a),o=[];return r.forEach((e=>{o.push(e.passthrough),e.before&&e.match?o.push(qe(e.before,e.match)):(o.push(e.before),o.push(e.match)),o.push(e.after)})),o=o.filter((e=>e)),o=o.map((e=>Qe(e,this))),this.update(o)}};Ze.split=Ze.splitAfter;const Xe=function(e,t){return!(!e||!t)&&(e[0]===t[0]&&e[2]===t[1])},_e=function(e,t,n){const a=e.world,r=a.methods.one.parseMatch;n=n||"^.";let o=r(t=t||".$",{},a),i=r(n,{},a);o[o.length-1].end=!0,i[0].start=!0;let s=e.fullPointer,l=[s[0]];for(let t=1;t<s.length;t+=1){let n=l[l.length-1],a=s[t],r=e.update([n]),u=e.update([a]);Xe(n,a)&&r.has(o)&&u.has(i)?l[l.length-1]=[n[0],n[1],a[2],n[3],a[4]]:l.push(a)}return e.update(l)},Ye={joinIf:function(e,t){return _e(this,e,t)},join:function(){return _e(this)}},et=Object.assign({},We,Ue,Ze,Ye);et.lookBehind=et.before,et.lookBefore=et.before,et.lookAhead=et.after,et.lookAfter=et.after,et.notIf=et.ifNo;const tt=/(?:^|\s)([![^]*(?:<[^<]*>)?\/.*?[^\\/]\/[?\]+*$~]*)(?:\s|$)/,nt=/([!~[^]*(?:<[^<]*>)?\([^)]+[^\\)]\)[?\]+*$~]*)(?:\s|$)/,at=/ /g,rt=e=>/^[![^]*(<[^<]*>)?\//.test(e)&&/\/[?\]+*$~]*$/.test(e),ot=function(e){return e=(e=e.map((e=>e.trim()))).filter((e=>e))},it=/\{([0-9]+)?(, *[0-9]*)?\}/,st=/&&/,lt=new RegExp(/^<\s*(\S+)\s*>/),ut=e=>e.charAt(0).toUpperCase()+e.substring(1),ct=e=>e.charAt(e.length-1),dt=e=>e.charAt(0),ht=e=>e.substring(1),gt=e=>e.substring(0,e.length-1),mt=function(e){return e=ht(e),e=gt(e)},pt=function(e,t){let n={};for(let a=0;a<2;a+=1){if("$"===ct(e)&&(n.end=!0,e=gt(e)),"^"===dt(e)&&(n.start=!0,e=ht(e)),"?"===ct(e)&&(n.optional=!0,e=gt(e)),("["===dt(e)||"]"===ct(e))&&(n.group=null,"["===dt(e)&&(n.groupStart=!0),"]"===ct(e)&&(n.groupEnd=!0),e=(e=e.replace(/^\[/,"")).replace(/\]$/,""),"<"===dt(e))){const t=lt.exec(e);t.length>=2&&(n.group=t[1],e=e.replace(t[0],""))}if("+"===ct(e)&&(n.greedy=!0,e=gt(e)),"*"!==e&&"*"===ct(e)&&"\\*"!==e&&(n.greedy=!0,e=gt(e)),"!"===dt(e)&&(n.negative=!0,e=ht(e)),"~"===dt(e)&&"~"===ct(e)&&e.length>2&&(e=mt(e),n.fuzzy=!0,n.min=t.fuzzy||.85,!1===/\(/.test(e)))return n.word=e,n;if("/"===dt(e)&&"/"===ct(e))return e=mt(e),t.caseSensitive&&(n.use="text"),n.regex=new RegExp(e),n;if(!0===it.test(e)&&(e=e.replace(it,((e,t,a)=>(void 0===a?(n.min=Number(t),n.max=Number(t)):(a=a.replace(/, */,""),void 0===t?(n.min=0,n.max=Number(a)):(n.min=Number(t),n.max=Number(a||999))),n.greedy=!0,n.min||(n.optional=!0),"")))),"("===dt(e)&&")"===ct(e)){st.test(e)?(n.choices=e.split(st),n.operator="and"):(n.choices=e.split("|"),n.operator="or"),n.choices[0]=ht(n.choices[0]);let a=n.choices.length-1;n.choices[a]=gt(n.choices[a]),n.choices=n.choices.map((e=>e.trim())),n.choices=n.choices.filter((e=>e)),n.choices=n.choices.map((e=>e.split(/ /g).map((e=>pt(e,t))))),e=""}if("{"===dt(e)&&"}"===ct(e)){if(e=mt(e),n.root=e,/\//.test(e)){let e=n.root.split(/\//);n.root=e[0],n.pos=e[1],"adj"===n.pos&&(n.pos="Adjective"),n.pos=n.pos.charAt(0).toUpperCase()+n.pos.substr(1).toLowerCase(),void 0!==e[2]&&(n.sense=e[2])}return n}if("<"===dt(e)&&">"===ct(e))return e=mt(e),n.chunk=ut(e),n.greedy=!0,n;if("%"===dt(e)&&"%"===ct(e))return e=mt(e),n.switch=e,n}return"#"===dt(e)?(n.tag=ht(e),n.tag=ut(n.tag),n):"@"===dt(e)?(n.method=ht(e),n):"."===e?(n.anything=!0,n):"*"===e?(n.anything=!0,n.greedy=!0,n.optional=!0,n):(e&&(e=(e=e.replace("\\*","*")).replace("\\.","."),t.caseSensitive?n.use="text":e=e.toLowerCase(),n.word=e),n)},ft=/[a-z0-9][-–—][a-z]/i,bt=function(e,t){let{all:n}=t.methods.two.transform.verb||{},a=e.root;return n?n(a,t.model):[]},yt=function(e,t){let{all:n}=t.methods.two.transform.noun||{};return n?n(e.root,t.model):[e.root]},vt=function(e,t){let{all:n}=t.methods.two.transform.adjective||{};return n?n(e.root,t.model):[e.root]},wt=function(e){return e=function(e){let t=0,n=null;for(let a=0;a<e.length;a++){const r=e[a];!0===r.groupStart&&(n=r.group,null===n&&(n=String(t),t+=1)),null!==n&&(r.group=n),!0===r.groupEnd&&(n=null)}return e}(e),e=function(e){return e.map((e=>(e.fuzzy&&e.choices&&e.choices.forEach((t=>{1===t.length&&t[0].word&&(t[0].fuzzy=!0,t[0].min=e.min)})),e)))}(e=e.map((e=>{if(void 0!==e.choices){if("or"!==e.operator)return e;if(!0===e.fuzzy)return e;!0===e.choices.every((e=>{if(1!==e.length)return!1;let t=e[0];return!0!==t.fuzzy&&!t.start&&!t.end&&void 0!==t.word&&!0!==t.negative&&!0!==t.optional&&!0!==t.method}))&&(e.fastOr=new Set,e.choices.forEach((t=>{e.fastOr.add(t[0].word)})),delete e.choices)}return e}))),e},kt=function(e,t){for(let n of t)if(e.has(n))return!0;return!1},Pt=function(e,t){for(let n=0;n<e.length;n+=1){let a=e[n];if(!0!==a.optional&&!0!==a.negative&&!0!==a.fuzzy){if(void 0!==a.word&&!1===t.has(a.word))return!0;if(void 0!==a.tag&&!1===t.has("#"+a.tag))return!0;if(a.fastOr&&!1===kt(a.fastOr,t))return!1}}return!1},At=function(e,t,n=3){if(e===t)return 1;if(e.length<n||t.length<n)return 0;const a=function(e,t){let n=e.length,a=t.length;if(0===n)return a;if(0===a)return n;let r=(a>n?a:n)+1;if(Math.abs(n-a)>(r||100))return r||100;let o,i,s,l,u,c,d=[];for(let e=0;e<r;e++)d[e]=[e],d[e].length=r;for(let e=0;e<r;e++)d[0][e]=e;for(let r=1;r<=n;++r)for(i=e[r-1],o=1;o<=a;++o){if(r===o&&d[r][o]>4)return n;s=t[o-1],l=i===s?0:1,u=d[r-1][o]+1,(c=d[r][o-1]+1)<u&&(u=c),(c=d[r-1][o-1]+l)<u&&(u=c);let a=r>1&&o>1&&i===t[o-2]&&e[r-2]===s&&(c=d[r-2][o-2]+l)<u;d[r][o]=a?c:u}return d[n][a]}(e,t);let r=Math.max(e.length,t.length);return 1-(0===r?0:a/r)},Ct=/([\u0022\uFF02\u0027\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F])/,jt=/([\u0022\uFF02\u0027\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4])/,Nt=/^[-–—]$/,It=/ [-–—]{1,3} /,Dt=(e,t)=>-1!==e.post.indexOf(t),Ht={hasQuote:e=>Ct.test(e.pre)||jt.test(e.post),hasComma:e=>Dt(e,","),hasPeriod:e=>!0===Dt(e,".")&&!1===Dt(e,"..."),hasExclamation:e=>Dt(e,"!"),hasQuestionMark:e=>Dt(e,"?")||Dt(e,"¿"),hasEllipses:e=>Dt(e,"..")||Dt(e,"…"),hasSemicolon:e=>Dt(e,";"),hasColon:e=>Dt(e,":"),hasSlash:e=>/\//.test(e.text),hasHyphen:e=>Nt.test(e.post)||Nt.test(e.pre),hasDash:e=>It.test(e.post)||It.test(e.pre),hasContraction:e=>Boolean(e.implicit),isAcronym:e=>e.tags.has("Acronym"),isKnown:e=>e.tags.size>0,isTitleCase:e=>/^\p{Lu}[a-z'\u00C0-\u00FF]/u.test(e.text),isUpperCase:e=>/^\p{Lu}+$/u.test(e.text)};Ht.hasQuotation=Ht.hasQuote;let Gt=function(){};Gt=function(e,t,n,a){let r=function(e,t,n,a){if(!0===t.anything)return!0;if(!0===t.start&&0!==n)return!1;if(!0===t.end&&n!==a-1)return!1;if(void 0!==t.id&&t.id===e.id)return!0;if(void 0!==t.word){if(t.use)return t.word===e[t.use];if(null!==e.machine&&e.machine===t.word)return!0;if(void 0!==e.alias&&e.alias.hasOwnProperty(t.word))return!0;if(!0===t.fuzzy){if(t.word===e.root)return!0;if(At(t.word,e.normal)>=t.min)return!0}return!(!e.alias||!e.alias.some((e=>e===t.word)))||t.word===e.text||t.word===e.normal}if(void 0!==t.tag)return!0===e.tags.has(t.tag);if(void 0!==t.method)return"function"==typeof Ht[t.method]&&!0===Ht[t.method](e);if(void 0!==t.pre)return e.pre&&e.pre.includes(t.pre);if(void 0!==t.post)return e.post&&e.post.includes(t.post);if(void 0!==t.regex){let n=e.normal;return t.use&&(n=e[t.use]),t.regex.test(n)}if(void 0!==t.chunk)return e.chunk===t.chunk;if(void 0!==t.switch)return e.switch===t.switch;if(void 0!==t.machine)return e.normal===t.machine||e.machine===t.machine||e.root===t.machine;if(void 0!==t.sense)return e.sense===t.sense;if(void 0!==t.fastOr){if(t.pos&&!e.tags.has(t.pos))return null;let n=e.root||e.implicit||e.machine||e.normal;return t.fastOr.has(n)||t.fastOr.has(e.text)}return void 0!==t.choices&&("and"===t.operator?t.choices.every((t=>Gt(e,t,n,a))):t.choices.some((t=>Gt(e,t,n,a))))}(e,t,n,a);return!0===t.negative?!r:r};const Tt=function(e,t){if(!0===e.end&&!0===e.greedy&&t.start_i+t.t<t.phrase_length-1){let n=Object.assign({},e,{end:!1});if(!0===Gt(t.terms[t.t],n,t.start_i+t.t,t.phrase_length))return!0}return!1},xt=function(e,t){return e.groups[e.inGroup]||(e.groups[e.inGroup]={start:t,length:0}),e.groups[e.inGroup]},Et=function(e){let{regs:t}=e,n=t[e.r],a=function(e,t){let n=e.t;if(!t)return e.terms.length;for(;n<e.terms.length;n+=1)if(!0===Gt(e.terms[n],t,e.start_i+n,e.phrase_length))return n;return null}(e,t[e.r+1]);if(null===a||0===a)return null;if(void 0!==n.min&&a-e.t<n.min)return null;if(void 0!==n.max&&a-e.t>n.max)return e.t=e.t+n.max,!0;if(!0===e.hasGroup){xt(e,e.t).length=a-e.t}return e.t=a,!0},Ft=function(e,t=0){let n=e.regs[e.r],a=!1;for(let o=0;o<n.choices.length;o+=1){let i=n.choices[o];if(r=i,"[object Array]"!==Object.prototype.toString.call(r))return!1;if(a=i.every(((n,a)=>{let r=0,o=e.t+a+t+r;if(void 0===e.terms[o])return!1;let i=Gt(e.terms[o],n,o+e.start_i,e.phrase_length);if(!0===i&&!0===n.greedy)for(let t=1;t<e.terms.length;t+=1){let a=e.terms[o+t];if(a){if(!0!==Gt(a,n,e.start_i+t,e.phrase_length))break;r+=1}}return t+=r,i})),a){t+=i.length;break}}var r;return a&&!0===n.greedy?Ft(e,t):t},Ot=function(e){const{regs:t}=e;let n=t[e.r],a=Ft(e);if(a){if(!0===n.negative)return null;if(!0===e.hasGroup){xt(e,e.t).length+=a}if(!0===n.end){let t=e.phrase_length;if(e.t+e.start_i+a!==t)return null}return e.t+=a,!0}return!!n.optional||null},zt=function(e){const{regs:t}=e;let n=t[e.r],a=function(e){let t=0;return!0===e.regs[e.r].choices.every((n=>{let a=n.every(((t,n)=>{let a=e.t+n;return void 0!==e.terms[a]&&Gt(e.terms[a],t,a,e.phrase_length)}));return!0===a&&n.length>t&&(t=n.length),a}))&&t}(e);if(a){if(!0===n.negative)return null;if(!0===e.hasGroup){xt(e,e.t).length+=a}if(!0===n.end){let t=e.phrase_length-1;if(e.t+e.start_i!==t)return null}return e.t+=a,!0}return!!n.optional||null},Vt=function(e){const{regs:t}=e;let n=t[e.r],a=Object.assign({},n);if(a.negative=!1,Gt(e.terms[e.t],a,e.start_i+e.t,e.phrase_length))return!1;if(n.optional){let n=t[e.r+1];if(n){if(Gt(e.terms[e.t],n,e.start_i+e.t,e.phrase_length))e.r+=1;else if(n.optional&&t[e.r+2]){Gt(e.terms[e.t],t[e.r+2],e.start_i+e.t,e.phrase_length)&&(e.r+=2)}}}return n.greedy?function(e,t,n){let a=0;for(let r=e.t;r<e.terms.length;r+=1){let o=Gt(e.terms[r],t,e.start_i+e.t,e.phrase_length);if(o)break;if(n&&(o=Gt(e.terms[r],n,e.start_i+e.t,e.phrase_length),o))break;if(a+=1,void 0!==t.max&&a===t.max)break}return!(0===a||t.min&&t.min>a||(e.t+=a,0))}(e,a,t[e.r+1]):(e.t+=1,!0)},Bt=function(e){const{regs:t,phrase_length:n}=e;let a=t[e.r];return e.t=function(e,t){let n=Object.assign({},e.regs[e.r],{start:!1,end:!1}),a=e.t;for(;e.t<e.terms.length;e.t+=1){if(t&&Gt(e.terms[e.t],t,e.start_i+e.t,e.phrase_length))return e.t;let r=e.t-a+1;if(void 0!==n.max&&r===n.max)return e.t;if(!1===Gt(e.terms[e.t],n,e.start_i+e.t,e.phrase_length))return void 0!==n.min&&r<n.min?null:e.t}return e.t}(e,t[e.r+1]),null===e.t||a.min&&a.min>e.t?null:!0!==a.end||e.start_i+e.t===n||null},St=function(e){const{regs:t}=e;let n=t[e.r],a=e.terms[e.t],r=e.t;if(n.optional&&t[e.r+1]&&n.negative)return!0;if(n.optional&&t[e.r+1]&&function(e){const{regs:t}=e;let n=t[e.r],a=e.terms[e.t],r=Gt(a,t[e.r+1],e.start_i+e.t,e.phrase_length);if(n.negative||r){let n=e.terms[e.t+1];n&&Gt(n,t[e.r+1],e.start_i+e.t,e.phrase_length)||(e.r+=1)}}(e),a.implicit&&e.terms[e.t+1]&&function(e){let t=e.terms[e.t],n=e.regs[e.r];if(t.implicit&&e.terms[e.t+1]){if(!e.terms[e.t+1].implicit)return;n.word===t.normal&&(e.t+=1),"hasContraction"===n.method&&(e.t+=1)}}(e),e.t+=1,!0===n.end&&e.t!==e.terms.length&&!0!==n.greedy)return null;if(!0===n.greedy){if(!Bt(e))return null}return!0===e.hasGroup&&function(e,t){let n=e.regs[e.r];const a=xt(e,t);e.t>1&&n.greedy?a.length+=e.t-t:a.length++}(e,r),!0},Kt=function(e,t,n,a){if(0===e.length||0===t.length)return null;let r={t:0,terms:e,r:0,regs:t,groups:{},start_i:n,phrase_length:a,inGroup:null};for(;r.r<t.length;r.r+=1){let e=t[r.r];if(r.hasGroup=Boolean(e.group),!0===r.hasGroup?r.inGroup=e.group:r.inGroup=null,!r.terms[r.t]){if(!1===t.slice(r.r).some((e=>!e.optional)))break;return null}if(!0!==e.anything||!0!==e.greedy)if(void 0===e.choices||"or"!==e.operator)if(void 0===e.choices||"and"!==e.operator)if(!0!==e.anything)if(!0!==Tt(e,r))if(e.negative){if(!Vt(r))return null}else if(!0!==Gt(r.terms[r.t],e,r.start_i+r.t,r.phrase_length)){if(!0!==e.optional)return null}else{if(!St(r))return null}else{if(!St(r))return null}else{if(e.negative&&e.anything)return null;if(!St(r))return null}else{if(!zt(r))return null}else{if(!Ot(r))return null}else{if(!Et(r))return null}}let o=[null,n,r.t+n];if(o[1]===o[2])return null;let i={};return Object.keys(r.groups).forEach((e=>{let t=r.groups[e],a=n+t.start;i[e]=[null,a,a+t.length]})),{pointer:o,groups:i}},$t=function(e,t){return e.pointer[0]=t,Object.keys(e.groups).forEach((n=>{e.groups[n][0]=t})),e},Lt=function(e,t,n){let a=Kt(e,t,0,e.length);return a?(a=$t(a,n),a):null},Mt={one:{termMethods:Ht,parseMatch:function(e,t,n){if(null==e||""===e)return[];t=t||{},"number"==typeof e&&(e=String(e));let a=function(e){let t=e.split(tt),n=[];t.forEach((e=>{rt(e)?n.push(e):n=n.concat(e.split(nt))})),n=ot(n);let a=[];return n.forEach((e=>{(e=>/^[![^]*(<[^<]*>)?\(/.test(e)&&/\)[?\]+*$~]*$/.test(e))(e)||rt(e)?a.push(e):a=a.concat(e.split(at))})),a=ot(a),a}(e);return a=a.map((e=>pt(e,t))),a=function(e,t){let n=t.model.one.prefixes;for(let t=e.length-1;t>=0;t-=1){let a=e[t];if(a.word&&ft.test(a.word)){let r=a.word.split(/[-–—]/g);if(n.hasOwnProperty(r[0]))continue;r=r.filter((e=>e)).reverse(),e.splice(t,1),r.forEach((n=>{let r=Object.assign({},a);r.word=n,e.splice(t,0,r)}))}}return e}(a,n),a=function(e,t){return e.map((e=>{if(e.root)if(t.methods.two&&t.methods.two.transform){let n=[];e.pos?"Verb"===e.pos?n=n.concat(bt(e,t)):"Noun"===e.pos?n=n.concat(yt(e,t)):"Adjective"===e.pos&&(n=n.concat(vt(e,t))):(n=n.concat(bt(e,t)),n=n.concat(yt(e,t)),n=n.concat(vt(e,t))),n=n.filter((e=>e)),n.length>0&&(e.operator="or",e.fastOr=new Set(n))}else e.machine=e.root,delete e.id,delete e.root;return e}))}(a,n),a=wt(a),a},match:function(e,t,n){n=n||[];let{regs:a,group:r,justOne:o}=t,i=[];if(!a||0===a.length)return{ptrs:[],byGroup:{}};const s=a.filter((e=>!0!==e.optional&&!0!==e.negative)).length;e:for(let t=0;t<e.length;t+=1){let r=e[t];if(!n[t]||!Pt(a,n[t]))if(!0!==a[0].start)for(let e=0;e<r.length;e+=1){let n=r.slice(e);if(n.length<s)break;let l=Kt(n,a,e,r.length);if(l){if(l=$t(l,t),i.push(l),!0===o)break e;let n=l.pointer[2];Math.abs(n-1)>e&&(e=Math.abs(n-1))}}else{let e=Lt(r,a,t);e&&i.push(e)}}return!0===a[a.length-1].end&&(i=i.filter((t=>{let n=t.pointer[0];return e[n].length===t.pointer[2]}))),t.notIf&&(i=function(e,t,n){return e=e.filter((e=>{let[a,r,o]=e.pointer,i=n[a].slice(r,o);for(let e=0;e<i.length;e+=1){let n=i.slice(e);if(null!==Kt(n,t,e,i.length))return!1}return!0})),e}(i,t.notIf,e)),i=function(e,t){let n=[],a={};return 0===e.length||("number"==typeof t&&(t=String(t)),t?e.forEach((e=>{e.groups[t]&&n.push(e.groups[t])})):e.forEach((e=>{n.push(e.pointer),Object.keys(e.groups).forEach((t=>{a[t]=a[t]||[],a[t].push(e.groups[t])}))}))),{ptrs:n,byGroup:a}}(i,r),i.ptrs.forEach((t=>{let[n,a,r]=t;t[3]=e[n][a].id,t[4]=e[n][r-1].id})),i}}};var Jt={api:function(e){Object.assign(e.prototype,et)},methods:Mt,lib:{parseMatch:function(e,t){const n=this.world();let a=n.methods.one.killUnicode;return a&&(e=a(e,n)),n.methods.one.parseMatch(e,t,n)}}};const Wt=/^\../,Ut=/^#./,qt=function(e,t){let n={},a={};return Object.keys(t).forEach((r=>{let o=t[r],i=function(e){let t="",n="</span>";return e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"),Wt.test(e)?t=`<span class="${e.replace(/^\./,"")}"`:Ut.test(e)?t=`<span id="${e.replace(/^#/,"")}"`:(t=`<${e}`,n=`</${e}>`),t+=">",{start:t,end:n}}(r);"string"==typeof o&&(o=e.match(o)),o.docs.forEach((e=>{if(e.every((e=>e.implicit)))return;let t=e[0].id;n[t]=n[t]||[],n[t].push(i.start);let r=e[e.length-1].id;a[r]=a[r]||[],a[r].push(i.end)}))})),{starts:n,ends:a}};var Rt={html:function(e){let{starts:t,ends:n}=qt(this,e),a="";return this.docs.forEach((e=>{for(let r=0;r<e.length;r+=1){let o=e[r];t.hasOwnProperty(o.id)&&(a+=t[o.id].join("")),a+=o.pre||"",a+=o.text||"",n.hasOwnProperty(o.id)&&(a+=n[o.id].join("")),a+=o.post||""}})),a}};const Qt=/[,:;)\]*.?~!\u0022\uFF02\u201D\u2019\u00BB\u203A\u2032\u2033\u2034\u301E\u00B4—-]+$/,Zt=/^[(['"*~\uFF02\u201C\u2018\u201F\u201B\u201E\u2E42\u201A\u00AB\u2039\u2035\u2036\u2037\u301D\u0060\u301F]+/,Xt=/[,:;)('"\u201D\]]/,_t=/^[-–—]$/,Yt=/ /,en=function(e,t,n=!0){let a="";return e.forEach((e=>{let n=e.pre||"",r=e.post||"";"some"===t.punctuation&&(n=n.replace(Zt,""),_t.test(r)&&(r=" "),r=r.replace(Xt,""),r=r.replace(/\?!+/,"?"),r=r.replace(/!+/,"!"),r=r.replace(/\?+/,"?"),r=r.replace(/\.{2,}/,""),e.tags.has("Abbreviation")&&(r=r.replace(/\./,""))),"some"===t.whitespace&&(n=n.replace(/\s/,""),r=r.replace(/\s+/," ")),t.keepPunct||(n=n.replace(Zt,""),r="-"===r?" ":r.replace(Qt,""));let o=e[t.form||"text"]||e.normal||"";"implicit"===t.form&&(o=e.implicit||e.text),"root"===t.form&&e.implicit&&(o=e.root||e.implicit||e.normal),"machine"!==t.form&&"implicit"!==t.form&&"root"!==t.form||!e.implicit||r&&Yt.test(r)||(r+=" "),a+=n+o+r})),!1===n&&(a=a.trim()),!0===t.lowerCase&&(a=a.toLowerCase()),a},tn={text:{form:"text"},normal:{whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"normal"},machine:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"none",unicode:"some",form:"machine"},root:{keepSpace:!1,whitespace:"some",punctuation:"some",case:"some",unicode:"some",form:"root"},implicit:{form:"implicit"}};tn.clean=tn.normal,tn.reduced=tn.root;let nn=[],an=0;for(;an<64;)nn[an]=0|4294967296*Math.sin(++an%Math.PI);const rn=function(e){let t,n,a,r=[t=1732584193,n=4023233417,~t,~n],o=[],i=decodeURI(encodeURI(e))+"",s=i.length;for(e=--s/4+2|15,o[--e]=8*s;~s;)o[s>>2]|=i.charCodeAt(s)<<8*s--;for(an=i=0;an<e;an+=16){for(s=r;i<64;s=[a=s[3],t+((a=s[0]+[t&n|~t&a,a&t|~a&n,t^n^a,n^(t|~a)][s=i>>4]+nn[i]+~~o[an|15&[i,5*i+1,3*i+5,7*i][s]])<<(s=[7,12,17,22,5,9,14,20,4,11,16,23,6,10,15,21][4*s+i++%4])|a>>>-s),t,n])t=0|s[1],n=s[2];for(i=4;i;)r[--i]+=s[i]}for(e="";i<32;)e+=(r[i>>3]>>4*(1^i++)&15).toString(16);return e},on={text:!0,terms:!0};let sn={case:"none",unicode:"some",form:"machine",punctuation:"some"};const ln=function(e,t){return Object.assign({},e,t)},un={text:e=>en(e,{keepPunct:!0},!1),normal:e=>en(e,ln(tn.normal,{keepPunct:!0}),!1),implicit:e=>en(e,ln(tn.implicit,{keepPunct:!0}),!1),machine:e=>en(e,sn,!1),root:e=>en(e,ln(sn,{form:"root"}),!1),hash:e=>rn(en(e,{keepPunct:!0},!1)),offset:e=>{let t=un.text(e).length;return{index:e[0].offset.index,start:e[0].offset.start,length:t}},terms:e=>e.map((e=>{let t=Object.assign({},e);return t.tags=Array.from(e.tags),t})),confidence:(e,t,n)=>t.eq(n).confidence(),syllables:(e,t,n)=>t.eq(n).syllables(),sentence:(e,t,n)=>t.eq(n).fullSentence().text(),dirty:e=>e.some((e=>!0===e.dirty))};un.sentences=un.sentence,un.clean=un.normal,un.reduced=un.root;const cn={json:function(e){let t=(n=this,"string"==typeof(a=(a=e)||{})&&(a={}),(a=Object.assign({},on,a)).offset&&n.compute("offset"),n.docs.map(((e,t)=>{let r={};return Object.keys(a).forEach((o=>{a[o]&&un[o]&&(r[o]=un[o](e,n,t))})),r})));var n,a;return"number"==typeof e?t[e]:t}};cn.data=cn.json;const dn=function(e){let t=e.pre||"",n=e.post||"";return t+e.text+n},hn=function(e,t){let n=function(e,t){let n={};return Object.keys(t).forEach((a=>{e.match(a).fullPointer.forEach((e=>{n[e[3]]={fn:t[a],end:e[2]}}))})),n}(e,t),a="";return e.docs.forEach(((t,r)=>{for(let o=0;o<t.length;o+=1){let i=t[o];if(n.hasOwnProperty(i.id)){let{fn:s,end:l}=n[i.id],u=e.update([[r,o,l]]);a+=t[o].pre||"",a+=s(u),o=l-1,a+=t[o].post||""}else a+=dn(i)}})),a},gn={debug:function(e){let t=this.methods.one.debug||{};return e&&t.hasOwnProperty(e)?(t[e](this),this):"undefined"!=typeof window&&window.document?(t.clientSide(this),this):(t.tags(this),this)},out:function(e){if(t=e,"[object Object]"===Object.prototype.toString.call(t))return hn(this,e);var t;if("text"===e)return this.text();if("normal"===e)return this.text("normal");if("root"===e)return this.text("root");if("machine"===e||"reduced"===e)return this.text("machine");if("hash"===e||"md5"===e)return rn(this.text());if("json"===e)return this.json();if("offset"===e||"offsets"===e)return this.compute("offset"),this.json({offset:!0});if("array"===e){let e=this.docs.map((e=>e.reduce(((e,t)=>e+t.pre+t.text+t.post),"").trim()));return e.filter((e=>e))}if("freq"===e||"frequency"===e||"topk"===e)return function(e){let t={};e.forEach((e=>{t[e]=t[e]||0,t[e]+=1}));let n=Object.keys(t).map((e=>({normal:e,count:t[e]})));return n.sort(((e,t)=>e.count>t.count?-1:0))}(this.json({normal:!0}).map((e=>e.normal)));if("terms"===e){let e=[];return this.docs.forEach((t=>{let n=t.map((e=>e.text));n=n.filter((e=>e)),e=e.concat(n)})),e}return"tags"===e?this.docs.map((e=>e.reduce(((e,t)=>(e[t.implicit||t.normal]=Array.from(t.tags),e)),{}))):"debug"===e?this.debug():this.text()},wrap:function(e){return hn(this,e)}};var mn={text:function(e){let t={};var n;if(e&&"string"==typeof e&&tn.hasOwnProperty(e)?t=Object.assign({},tn[e]):e&&(n=e,"[object Object]"===Object.prototype.toString.call(n))&&(t=Object.assign({},e)),void 0!==t.keepSpace||this.isFull()||(t.keepSpace=!1),void 0===t.keepEndPunct&&this.pointer){let e=this.pointer[0];e&&e[1]?t.keepEndPunct=!1:t.keepEndPunct=!0}return void 0===t.keepPunct&&(t.keepPunct=!0),void 0===t.keepSpace&&(t.keepSpace=!0),function(e,t){let n="";if(!e||!e[0]||!e[0][0])return n;for(let a=0;a<e.length;a+=1)n+=en(e[a],t,!0);if(t.keepSpace||(n=n.trim()),!1===t.keepEndPunct){e[0][0].tags.has("Emoticon")||(n=n.replace(Zt,""));let t=e[e.length-1];t[t.length-1].tags.has("Emoticon")||(n=n.replace(Qt,"")),n.endsWith("'")&&!n.endsWith("s'")&&(n=n.replace(/'/,""))}return!0===t.cleanWhitespace&&(n=n.trim()),n}(this.docs,t)}};const pn=Object.assign({},gn,mn,cn,Rt),fn="[0m",bn={green:e=>"[32m"+e+fn,red:e=>"[31m"+e+fn,blue:e=>"[34m"+e+fn,magenta:e=>"[35m"+e+fn,cyan:e=>"[36m"+e+fn,yellow:e=>"[33m"+e+fn,black:e=>"[30m"+e+fn,dim:e=>"[2m"+e+fn,i:e=>"[3m"+e+fn},yn={tags:function(e){let{docs:t,model:n}=e;0===t.length&&console.log(bn.blue("\n     ──────")),t.forEach((t=>{console.log(bn.blue("\n  ┌─────────")),t.forEach((t=>{let a=[...t.tags||[]],r=t.text||"-";t.sense&&(r=`{${t.normal}/${t.sense}}`),t.implicit&&(r="["+t.implicit+"]"),r=bn.yellow(r);let o="'"+r+"'";if(t.reference){let n=e.update([t.reference]).text("normal");o+=` - ${bn.dim(bn.i("["+n+"]"))}`}o=o.padEnd(18);let i=bn.blue("  │ ")+bn.i(o)+"  - "+function(e,t){return t.one.tagSet&&(e=e.map((e=>{if(!t.one.tagSet.hasOwnProperty(e))return e;const n=t.one.tagSet[e].color||"blue";return bn[n](e)}))),e.join(", ")}(a,n);console.log(i)}))})),console.log("\n")},clientSide:function(e){console.log("%c -=-=- ","background-color:#6699cc;"),e.forEach((e=>{console.groupCollapsed(e.text());let t=e.docs[0].map((e=>{let t=e.text||"-";return e.implicit&&(t="["+e.implicit+"]"),{text:t,tags:"["+Array.from(e.tags).join(", ")+"]"}}));console.table(t,["text","tags"]),console.groupEnd()}))},chunks:function(e){let{docs:t}=e;console.log(""),t.forEach((e=>{let t=[];e.forEach((e=>{"Noun"===e.chunk?t.push(bn.blue(e.implicit||e.normal)):"Verb"===e.chunk?t.push(bn.green(e.implicit||e.normal)):"Adjective"===e.chunk?t.push(bn.yellow(e.implicit||e.normal)):"Pivot"===e.chunk?t.push(bn.red(e.implicit||e.normal)):t.push(e.implicit||e.normal)})),console.log(t.join(" "),"\n")})),console.log("\n")},highlight:function(e){if(!e.found)return;let t={};e.fullPointer.forEach((e=>{t[e[0]]=t[e[0]]||[],t[e[0]].push(e)})),Object.keys(t).forEach((n=>{let a=e.update([[Number(n)]]).text();e.update(t[n]).json({offset:!0}).forEach(((e,t)=>{a=function(e,t,n){let a=((e,t,n)=>{let a=9*n,r=t.start+a,o=r+t.length;return[e.substring(0,r),e.substring(r,o),e.substring(o,e.length)]})(e,t,n);return`${a[0]}${bn.blue(a[1])}${a[2]}`}(a,e.offset,t)})),console.log(a)})),console.log("\n")}};var vn={api:function(e){Object.assign(e.prototype,pn)},methods:{one:{hash:rn,debug:yn}}};const wn=function(e,t){if(e[0]!==t[0])return!1;let[,n,a]=e,[,r,o]=t;return n<=r&&a>r||r<=n&&o>n},kn=function(e){let t={};return e.forEach((e=>{t[e[0]]=t[e[0]]||[],t[e[0]].push(e)})),t},Pn=function(e,t){let n=kn(t),a=[];return e.forEach((e=>{let[t]=e,r=n[t]||[];if(r=r.filter((t=>function(e,t){return e[1]<=t[1]&&t[2]<=e[2]}(e,t))),0===r.length)return void a.push({passthrough:e});r=r.sort(((e,t)=>e[1]-t[1]));let o=e;r.forEach(((e,t)=>{let n=function(e,t){let[n,a]=e,r=t[1],o=t[2],i={};if(a<r){let t=r<e[2]?r:e[2];i.before=[n,a,t]}return i.match=t,e[2]>o&&(i.after=[n,o,e[2]]),i}(o,e);r[t+1]?(a.push({before:n.before,match:n.match}),n.after&&(o=n.after)):a.push(n)}))})),a};var An={one:{termList:function(e){let t=[];for(let n=0;n<e.length;n+=1)for(let a=0;a<e[n].length;a+=1)t.push(e[n][a]);return t},getDoc:function(e,t){let n=[];return e.forEach(((a,r)=>{if(!a)return;let[o,i,s,l,u]=a,c=t[o]||[];if(void 0===i&&(i=0),void 0===s&&(s=c.length),!l||c[i]&&c[i].id===l)c=c.slice(i,s);else{let n=function(e,t,n){for(let a=0;a<20;a+=1){if(t[n-a]){let r=t[n-a].findIndex((t=>t.id===e));if(-1!==r)return[n-a,r]}if(t[n+a]){let r=t[n+a].findIndex((t=>t.id===e));if(-1!==r)return[n+a,r]}}return null}(l,t,o);if(null!==n){let a=s-i;c=t[n[0]].slice(n[1],n[1]+a);let o=c[0]?c[0].id:null;e[r]=[n[0],n[1],n[1]+a,o]}}0!==c.length&&i!==s&&(u&&c[c.length-1].id!==u&&(c=function(e,t){let[n,a,,,r]=e,o=t[n],i=o.findIndex((e=>e.id===r));return-1===i?(e[2]=t[n].length,e[4]=o.length?o[o.length-1].id:null):e[2]=i,t[n].slice(a,e[2]+1)}(a,t)),n.push(c))})),n=n.filter((e=>e.length>0)),n},pointer:{indexN:kn,splitAll:Pn}}};const Cn=function(e,t){let n=e.concat(t),a=kn(n),r=[];return n.forEach((e=>{let[t]=e;if(1===a[t].length)return void r.push(e);let n=a[t].filter((t=>wn(e,t)));n.push(e);let o=function(e){let t=e[0][1],n=e[0][2];return e.forEach((e=>{e[1]<t&&(t=e[1]),e[2]>n&&(n=e[2])})),[e[0][0],t,n]}(n);r.push(o)})),r=function(e){let t={};for(let n=0;n<e.length;n+=1)t[e[n].join(",")]=e[n];return Object.values(t)}(r),r},jn=function(e,t){let n=[];return Pn(e,t).forEach((e=>{e.passthrough&&n.push(e.passthrough),e.before&&n.push(e.before),e.after&&n.push(e.after)})),n},Nn=(e,t)=>{return"string"==typeof e||(n=e,"[object Array]"===Object.prototype.toString.call(n))?t.match(e):e||t.none();var n},In=function(e,t){return e.map((e=>{let[n,a]=e;return t[n]&&t[n][a]&&(e[3]=t[n][a].id),e}))},Dn={union:function(e){e=Nn(e,this);let t=Cn(this.fullPointer,e.fullPointer);return t=In(t,this.document),this.toView(t)}};Dn.and=Dn.union,Dn.intersection=function(e){e=Nn(e,this);let t=function(e,t){let n=kn(t),a=[];return e.forEach((e=>{let t=n[e[0]]||[];t=t.filter((t=>wn(e,t))),0!==t.length&&t.forEach((t=>{let n=function(e,t){let n=e[1]<t[1]?t[1]:e[1],a=e[2]>t[2]?t[2]:e[2];return n<a?[e[0],n,a]:null}(e,t);n&&a.push(n)}))})),a}(this.fullPointer,e.fullPointer);return t=In(t,this.document),this.toView(t)},Dn.not=function(e){e=Nn(e,this);let t=jn(this.fullPointer,e.fullPointer);return t=In(t,this.document),this.toView(t)},Dn.difference=Dn.not,Dn.complement=function(){let e=this.all(),t=jn(e.fullPointer,this.fullPointer);return t=In(t,this.document),this.toView(t)},Dn.settle=function(){let e=this.fullPointer;return e.forEach((t=>{e=Cn(e,[t])})),e=In(e,this.document),this.update(e)};var Hn={methods:An,api:function(e){Object.assign(e.prototype,Dn)}};const Gn=function(e){return!0===e.optional||!0===e.negative?null:e.tag?"#"+e.tag:e.word?e.word:e.switch?`%${e.switch}%`:null},Tn=function(e,t){const n=t.methods.one.parseMatch;return e.forEach((e=>{e.regs=n(e.match,{},t),"string"==typeof e.ifNo&&(e.ifNo=[e.ifNo]),e.notIf&&(e.notIf=n(e.notIf,{},t)),e.needs=function(e){let t=[];return e.forEach((e=>{t.push(Gn(e)),"and"===e.operator&&e.choices&&e.choices.forEach((e=>{e.forEach((e=>{t.push(Gn(e))}))}))})),t.filter((e=>e))}(e.regs);let{wants:a,count:r}=function(e){let t=[],n=0;return e.forEach((e=>{"or"!==e.operator||e.optional||e.negative||(e.fastOr&&Array.from(e.fastOr).forEach((e=>{t.push(e)})),e.choices&&e.choices.forEach((e=>{e.forEach((e=>{let n=Gn(e);n&&t.push(n)}))})),n+=1)})),{wants:t,count:n}}(e.regs);e.wants=a,e.minWant=r,e.minWords=e.regs.filter((e=>!e.optional)).length})),e};var xn={buildNet:function(e,t){e=Tn(e,t);let n={};e.forEach((e=>{e.needs.forEach((t=>{n[t]=Array.isArray(n[t])?n[t]:[],n[t].push(e)})),e.wants.forEach((t=>{n[t]=Array.isArray(n[t])?n[t]:[],n[t].push(e)}))})),Object.keys(n).forEach((e=>{let t={};n[e]=n[e].filter((e=>"boolean"!=typeof t[e.match]&&(t[e.match]=!0,!0)))}));let a=e.filter((e=>0===e.needs.length&&0===e.wants.length));return{hooks:n,always:a}},bulkMatch:function(e,t,n,a={}){let r=n.one.cacheDoc(e),o=function(e,t){return e.map(((n,a)=>{let r=[];Object.keys(t).forEach((n=>{e[a].has(n)&&(r=r.concat(t[n]))}));let o={};return r=r.filter((e=>"boolean"!=typeof o[e.match]&&(o[e.match]=!0,!0))),r}))}(r,t.hooks);o=function(e,t){return e.map(((e,n)=>{let a=t[n];return(e=(e=e.filter((e=>e.needs.every((e=>a.has(e)))))).filter((e=>void 0===e.ifNo||!0!==e.ifNo.some((e=>a.has(e)))))).filter((e=>0===e.wants.length||e.wants.filter((e=>a.has(e))).length>=e.minWant))}))}(o,r),t.always.length>0&&(o=o.map((e=>e.concat(t.always)))),o=function(e,t){return e.map(((e,n)=>{let a=t[n].length;return e=e.filter((e=>a>=e.minWords)),e}))}(o,e);let i=function(e,t,n,a,r){let o=[];for(let n=0;n<e.length;n+=1)for(let i=0;i<e[n].length;i+=1){let s=e[n][i],l=a.one.match([t[n]],s);if(l.ptrs.length>0&&(l.ptrs.forEach((e=>{e[0]=n;let t=Object.assign({},s,{pointer:e});void 0!==s.unTag&&(t.unTag=s.unTag),o.push(t)})),!0===r.matchOne))return[o[0]]}return o}(o,e,0,n,a);return i},bulkTagger:function(e,t,n){const{model:a,methods:r}=n,{getDoc:o,setTag:i,unTag:s}=r.one,l=r.two.looksPlural;if(0===e.length)return e;return("undefined"!=typeof process&&process.env?process.env:self.env||{}).DEBUG_TAGS&&console.log(`\n\n  [32m→ ${e.length} post-tagger:[0m`),e.map((e=>{if(!e.tag&&!e.chunk&&!e.unTag)return;let r=e.reason||e.match,u=o([e.pointer],t)[0];if(!0===e.safe){if(!1===function(e,t,n){let a=n.one.tagSet;if(!a.hasOwnProperty(t))return!0;let r=a[t].not||[];for(let t=0;t<e.length;t+=1){let n=e[t];for(let e=0;e<r.length;e+=1)if(!0===n.tags.has(r[e]))return!1}return!0}(u,e.tag,a))return;if("-"===u[u.length-1].post)return}if(void 0!==e.tag){if(i(u,e.tag,n,e.safe,`[post] '${r}'`),"Noun"===e.tag&&l){let t=u[u.length-1];l(t.text)?i([t],"Plural",n,e.safe,"quick-plural"):i([t],"Singular",n,e.safe,"quick-singular")}!0===e.freeze&&u.forEach((e=>e.frozen=!0))}void 0!==e.unTag&&s(u,e.unTag,n,e.safe,r),e.chunk&&u.forEach((t=>t.chunk=e.chunk))}))}},En={lib:{buildNet:function(e){let t=this.methods().one.buildNet(e,this.world());return t.isNet=!0,t}},api:function(e){e.prototype.sweep=function(e,t={}){const{world:n,docs:a}=this,{methods:r}=n;let o=r.one.bulkMatch(a,e,this.methods,t);!1!==t.tagger&&r.one.bulkTagger(o,a,this.world),o=o.map((e=>{let t=e.pointer,n=a[t[0]][t[1]],r=t[2]-t[1];return n.index&&(e.pointer=[n.index[0],n.index[1],t[1]+r]),e}));let i=o.map((e=>e.pointer));return o=o.map((e=>(e.view=this.update([e.pointer]),delete e.regs,delete e.needs,delete e.pointer,delete e._expanded,e))),{view:this.update(i),found:o}}},methods:{one:xn}};const Fn=/ /,On=function(e,t){"Noun"===t&&(e.chunk=t),"Verb"===t&&(e.chunk=t)},zn=function(e,t,n,a){if(!0===e.tags.has(t))return null;if("."===t)return null;!0===e.frozen&&(a=!0);let r=n[t];if(r){if(r.not&&r.not.length>0)for(let t=0;t<r.not.length;t+=1){if(!0===a&&e.tags.has(r.not[t]))return null;e.tags.delete(r.not[t])}if(r.parents&&r.parents.length>0)for(let t=0;t<r.parents.length;t+=1)e.tags.add(r.parents[t]),On(e,r.parents[t])}return e.tags.add(t),e.dirty=!0,On(e,t),!0},Vn=function(e,t,n={},a,r){const o=n.model.one.tagSet||{};if(!t)return;const i="undefined"!=typeof process&&process.env?process.env:self.env||{};var s;if(i&&i.DEBUG_TAGS&&((e,t,n="")=>{let a=e.map((e=>e.text||"["+e.implicit+"]")).join(" ");var r;"string"!=typeof t&&t.length>2&&(t=t.slice(0,2).join(", #")+" +"),t="string"!=typeof t?t.join(", #"):t,console.log(` ${(r=a,"[33m[3m"+r+"[0m").padEnd(24)} [32m→[0m #${t.padEnd(22)}  ${(e=>"[3m"+e+"[0m")(n)}`)})(e,t,r),!0!=(s=t,"[object Array]"===Object.prototype.toString.call(s)))if("string"==typeof t)if(t=t.trim(),Fn.test(t))!function(e,t,n,a){let r=t.split(Fn);e.forEach(((e,t)=>{let o=r[t];o&&(o=o.replace(/^#/,""),zn(e,o,n,a))}))}(e,t,o,a);else{t=t.replace(/^#/,"");for(let n=0;n<e.length;n+=1)zn(e[n],t,o,a)}else console.warn(`compromise: Invalid tag '${t}'`);else t.forEach((t=>Vn(e,t,n,a)))},Bn=function(e){return e.children=e.children||[],e._cache=e._cache||{},e.props=e.props||{},e._cache.parents=e._cache.parents||[],e._cache.children=e._cache.children||[],e},Sn=/^ *(#|\/\/)/,Kn=function(e){let t=e.trim().split(/->/),n=[];t.forEach((e=>{n=n.concat(function(e){if(!(e=e.trim()))return null;if(/^\[/.test(e)&&/\]$/.test(e)){let t=(e=(e=e.replace(/^\[/,"")).replace(/\]$/,"")).split(/,/);return t=t.map((e=>e.trim())).filter((e=>e)),t=t.map((e=>Bn({id:e}))),t}return[Bn({id:e})]}(e))})),n=n.filter((e=>e));let a=n[0];for(let e=1;e<n.length;e+=1)a.children.push(n[e]),a=n[e];return n[0]},$n=(e,t)=>{let n=[],a=[e];for(;a.length>0;){let e=a.pop();n.push(e),e.children&&e.children.forEach((n=>{t&&t(e,n),a.push(n)}))}return n},Ln=e=>"[object Array]"===Object.prototype.toString.call(e),Mn=e=>(e=e||"").trim(),Jn=function(e=[]){return"string"==typeof e?function(e){let t=e.split(/\r?\n/),n=[];t.forEach((e=>{if(!e.trim()||Sn.test(e))return;let t=(e=>{const t=/^( {2}|\t)/;let n=0;for(;t.test(e);)e=e.replace(t,""),n+=1;return n})(e);n.push({indent:t,node:Kn(e)})}));let a=function(e){let t={children:[]};return e.forEach(((n,a)=>{0===n.indent?t.children=t.children.concat(n.node):e[a-1]&&function(e,t){let n=e[t].indent;for(;t>=0;t-=1)if(e[t].indent<n)return e[t];return e[0]}(e,a).node.children.push(n.node)})),t}(n);return a=Bn(a),a}(e):Ln(e)?function(e){let t={};e.forEach((e=>{t[e.id]=e}));let n=Bn({});return e.forEach((e=>{if((e=Bn(e)).parent)if(t.hasOwnProperty(e.parent)){let n=t[e.parent];delete e.parent,n.children.push(e)}else console.warn(`[Grad] - missing node '${e.parent}'`);else n.children.push(e)})),n}(e):($n(t=e).forEach(Bn),t);var t},Wn=function(e,t){let n="-> ";t&&(n=(e=>"[2m"+e+"[0m")("→ "));let a="";return $n(e).forEach(((e,r)=>{let o=e.id||"";if(t&&(o=(e=>"[31m"+e+"[0m")(o)),0===r&&!e.id)return;let i=e._cache.parents.length;a+="    ".repeat(i)+n+o+"\n"})),a},Un=function(e){let t=$n(e);t.forEach((e=>{delete(e=Object.assign({},e)).children}));let n=t[0];return n&&!n.id&&0===Object.keys(n.props).length&&t.shift(),t},qn={text:Wn,txt:Wn,array:Un,flat:Un},Rn=function(e,t){return"nested"===t||"json"===t?e:"debug"===t?(console.log(Wn(e,!0)),null):qn.hasOwnProperty(t)?qn[t](e):e},Qn=e=>{$n(e,((e,t)=>{e.id&&(e._cache.parents=e._cache.parents||[],t._cache.parents=e._cache.parents.concat([e.id]))}))},Zn=/\//;let Xn=class g{constructor(e={}){Object.defineProperty(this,"json",{enumerable:!1,value:e,writable:!0})}get children(){return this.json.children}get id(){return this.json.id}get found(){return this.json.id||this.json.children.length>0}props(e={}){let t=this.json.props||{};return"string"==typeof e&&(t[e]=!0),this.json.props=Object.assign(t,e),this}get(e){if(e=Mn(e),!Zn.test(e)){let t=this.json.children.find((t=>t.id===e));return new g(t)}let t=((e,t)=>{let n=(e=>"string"!=typeof e?e:(e=e.replace(/^\//,"")).split(/\//))(t=t||"");for(let t=0;t<n.length;t+=1){let a=e.children.find((e=>e.id===n[t]));if(!a)return null;e=a}return e})(this.json,e)||Bn({});return new g(t)}add(e,t={}){if(Ln(e))return e.forEach((e=>this.add(Mn(e),t))),this;e=Mn(e);let n=Bn({id:e,props:t});return this.json.children.push(n),new g(n)}remove(e){return e=Mn(e),this.json.children=this.json.children.filter((t=>t.id!==e)),this}nodes(){return $n(this.json).map((e=>(delete(e=Object.assign({},e)).children,e)))}cache(){return(e=>{let t=$n(e,((e,t)=>{e.id&&(e._cache.parents=e._cache.parents||[],e._cache.children=e._cache.children||[],t._cache.parents=e._cache.parents.concat([e.id]))})),n={};t.forEach((e=>{e.id&&(n[e.id]=e)})),t.forEach((e=>{e._cache.parents.forEach((t=>{n.hasOwnProperty(t)&&n[t]._cache.children.push(e.id)}))})),e._cache.children=Object.keys(n)})(this.json),this}list(){return $n(this.json)}fillDown(){var e;return e=this.json,$n(e,((e,t)=>{t.props=((e,t)=>(Object.keys(t).forEach((n=>{if(t[n]instanceof Set){let a=e[n]||new Set;e[n]=new Set([...a,...t[n]])}else if((e=>e&&"object"==typeof e&&!Array.isArray(e))(t[n])){let a=e[n]||{};e[n]=Object.assign({},t[n],a)}else Ln(t[n])?e[n]=t[n].concat(e[n]||[]):void 0===e[n]&&(e[n]=t[n])})),e))(t.props,e.props)})),this}depth(){Qn(this.json);let e=$n(this.json),t=e.length>1?1:0;return e.forEach((e=>{if(0===e._cache.parents.length)return;let n=e._cache.parents.length+1;n>t&&(t=n)})),t}out(e){return Qn(this.json),Rn(this.json,e)}debug(){return Qn(this.json),Rn(this.json,"debug"),this}};const _n=function(e){let t=Jn(e);return new Xn(t)};_n.prototype.plugin=function(e){e(this)};const Yn={Noun:"blue",Verb:"green",Negative:"green",Date:"red",Value:"red",Adjective:"magenta",Preposition:"cyan",Conjunction:"cyan",Determiner:"cyan",Hyphenated:"cyan",Adverb:"cyan"},ea=function(e){if(Yn.hasOwnProperty(e.id))return Yn[e.id];if(Yn.hasOwnProperty(e.is))return Yn[e.is];let t=e._cache.parents.find((e=>Yn[e]));return Yn[t]},ta=function(e){return e?"string"==typeof e?[e]:e:[]},na=function(e,t){return e=function(e,t){return Object.keys(e).forEach((n=>{e[n].isA&&(e[n].is=e[n].isA),e[n].notA&&(e[n].not=e[n].notA),e[n].is&&"string"==typeof e[n].is&&(t.hasOwnProperty(e[n].is)||e.hasOwnProperty(e[n].is)||(e[e[n].is]={})),e[n].not&&"string"==typeof e[n].not&&!e.hasOwnProperty(e[n].not)&&(t.hasOwnProperty(e[n].not)||e.hasOwnProperty(e[n].not)||(e[e[n].not]={}))})),e}(e,t),Object.keys(e).forEach((t=>{e[t].children=ta(e[t].children),e[t].not=ta(e[t].not)})),Object.keys(e).forEach((t=>{(e[t].not||[]).forEach((n=>{e[n]&&e[n].not&&e[n].not.push(t)}))})),e};var aa={one:{setTag:Vn,unTag:function(e,t,n){t=t.trim().replace(/^#/,"");for(let a=0;a<e.length;a+=1){let r=e[a];if(!0===r.frozen)continue;if("*"===t){r.tags.clear();continue}let o=n[t];if(o&&o.children.length>0)for(let e=0;e<o.children.length;e+=1)r.tags.delete(o.children[e]);r.tags.delete(t)}},addTags:function(e,t){Object.keys(t).length>0&&(e=function(e){return Object.keys(e).forEach((t=>{e[t]=Object.assign({},e[t]),e[t].novel=!0})),e}(e)),e=na(e,t);const n=function(e){const t=Object.keys(e).map((t=>{let n=e[t];const a={not:new Set(n.not),also:n.also,is:n.is,novel:n.novel};return{id:t,parent:n.is,props:a,children:[]}}));return _n(t).cache().fillDown().out("array")}(Object.assign({},t,e)),a=function(e){const t={};return e.forEach((e=>{let{not:n,also:a,is:r,novel:o}=e.props,i=e._cache.parents;a&&(i=i.concat(a)),t[e.id]={is:r,not:n,novel:o,also:a,parents:i,children:e._cache.children,color:ea(e)}})),Object.keys(t).forEach((e=>{let n=new Set(t[e].not);t[e].not.forEach((e=>{t[e]&&t[e].children.forEach((e=>n.add(e)))})),t[e].not=Array.from(n)})),t}(n);return a},canBe:function(e,t,n){if(!n.hasOwnProperty(t))return!0;let a=n[t].not||[];for(let t=0;t<a.length;t+=1)if(e.tags.has(a[t]))return!1;return!0}}};const ra=function(e){return"[object Array]"===Object.prototype.toString.call(e)},oa={tag:function(e,t="",n){if(!this.found||!e)return this;let a=this.termList();if(0===a.length)return this;const{methods:r,verbose:o,world:i}=this;return!0===o&&console.log(" +  ",e,t||""),ra(e)?e.forEach((e=>r.one.setTag(a,e,i,n,t))):r.one.setTag(a,e,i,n,t),this.uncache(),this},tagSafe:function(e,t=""){return this.tag(e,t,!0)},unTag:function(e,t){if(!this.found||!e)return this;let n=this.termList();if(0===n.length)return this;const{methods:a,verbose:r,model:o}=this;!0===r&&console.log(" -  ",e,t||"");let i=o.one.tagSet;return ra(e)?e.forEach((e=>a.one.unTag(n,e,i))):a.one.unTag(n,e,i),this.uncache(),this},canBe:function(e){e=e.replace(/^#/,"");let t=this.model.one.tagSet,n=this.methods.one.canBe,a=[];this.document.forEach(((r,o)=>{r.forEach(((r,i)=>{n(r,e,t)||a.push([o,i,i+1])}))}));let r=this.update(a);return this.difference(r)}};var ia={addTags:function(e){const{model:t,methods:n}=this.world(),a=t.one.tagSet;let r=(0,n.one.addTags)(e,a);return t.one.tagSet=r,this}};const sa=new Set(["Auxiliary","Possessive"]);var la={model:{one:{tagSet:{}}},compute:{tagRank:function(e){const{document:t,world:n}=e,a=n.model.one.tagSet;t.forEach((e=>{e.forEach((e=>{let t=Array.from(e.tags);e.tagRank=function(e,t){return e=e.sort(((e,n)=>{if(sa.has(e)||!t.hasOwnProperty(n))return 1;if(sa.has(n)||!t.hasOwnProperty(e))return-1;let a=t[e].children||[],r=a.length;return a=t[n].children||[],r-a.length})),e}(t,a)}))}))}},methods:aa,api:function(e){Object.assign(e.prototype,oa)},lib:ia};const ua=/([.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s)/g,ca=/^[.!?\u203D\u2E18\u203C\u2047-\u2049\u3002]+\s$/,da=/((?:\r?\n|\r)+)/,ha=/[a-z0-9\u00C0-\u00FF\u00a9\u00ae\u2000-\u3300\ud000-\udfff]/i,ga=/\S/,ma={'"':'"',"＂":"＂","“":"”","‟":"”","„":"”","⹂":"”","‚":"’","«":"»","‹":"›","‵":"′","‶":"″","‷":"‴","〝":"〞","〟":"〞"},pa=RegExp("["+Object.keys(ma).join("")+"]","g"),fa=RegExp("["+Object.values(ma).join("")+"]","g"),ba=function(e){if(!e)return!1;let t=e.match(fa);return null!==t&&1===t.length},ya=/\(/g,va=/\)/g,wa=/\S/,ka=/^\s+/,Pa=function(e,t){let n=e.split(/[-–—]/);if(n.length<=1)return!1;const{prefixes:a,suffixes:r}=t.one;if(1===n[0].length&&/[a-z]/i.test(n[0]))return!1;if(a.hasOwnProperty(n[0]))return!1;if(n[1]=n[1].trim().replace(/[.?!]$/,""),r.hasOwnProperty(n[1]))return!1;if(!0===/^([a-z\u00C0-\u00FF`"'/]+)[-–—]([a-z0-9\u00C0-\u00FF].*)/i.test(e))return!0;return!0===/^[('"]?([0-9]{1,4})[-–—]([a-z\u00C0-\u00FF`"'/-]+[)'"]?$)/i.test(e)},Aa=function(e){let t=[];const n=e.split(/[-–—]/);let a="-",r=e.match(/[-–—]/);r&&r[0]&&(a=r);for(let e=0;e<n.length;e++)e===n.length-1?t.push(n[e]):t.push(n[e]+a);return t},Ca=/\p{L} ?\/ ?\p{L}+$/u,ja=/\S/,Na=/^[!?.]+$/,Ia=/(\S+)/;let Da=[".","?","!",":",";","-","–","—","--","...","(",")","[","]",'"',"'","`","«","»","*","•"];Da=Da.reduce(((e,t)=>(e[t]=!0,e)),{});const Ha=/\p{Letter}/u,Ga=/[\p{Number}\p{Currency_Symbol}]/u,Ta=/^[a-z]\.([a-z]\.)+/i,xa=/[sn]['’]$/,Ea=/([A-Z]\.)+[A-Z]?,?$/,Fa=/^[A-Z]\.,?$/,Oa=/[A-Z]{2,}('s|,)?$/,za=/([a-z]\.)+[a-z]\.?$/,Va=function(e){return function(e){return!0===Ea.test(e)||!0===za.test(e)||!0===Fa.test(e)||!0===Oa.test(e)}(e)&&(e=e.replace(/\./g,"")),e},Ba=function(e,t){const n=t.methods.one.killUnicode;let a=e.text||"";a=function(e){let t=e=(e=(e=e||"").toLowerCase()).trim();return e=(e=(e=e.replace(/[,;.!?]+$/,"")).replace(/\u2026/g,"...")).replace(/\u2013/g,"-"),!1===/^[:;]/.test(e)&&(e=(e=(e=e.replace(/\.{3,}$/g,"")).replace(/[",.!:;?)]+$/g,"")).replace(/^['"(]+/g,"")),""===(e=(e=e.replace(/[\u200B-\u200D\uFEFF]/g,"")).trim())&&(e=t),e.replace(/([0-9]),([0-9])/g,"$1$2")}(a),a=n(a,t),a=Va(a),e.normal=a},Sa=/[ .][A-Z]\.? *$/i,Ka=/(?:\u2026|\.{2,}) *$/,$a=/\p{L}/u,La=/\. *$/,Ma=/^[A-Z]\. $/;var Ja={one:{killUnicode:function(e,t){const n=t.model.one.unicode||{};let a=(e=e||"").split("");return a.forEach(((e,t)=>{n[e]&&(a[t]=n[e])})),a.join("")},tokenize:{splitSentences:function(e,t){if(e=e||"",!(e=String(e))||"string"!=typeof e||!1===wa.test(e))return[];let n=function(e){let t=[],n=e.split(da);for(let e=0;e<n.length;e++){let a=n[e].split(ua);for(let e=0;e<a.length;e++)a[e+1]&&!0===ca.test(a[e+1])&&(a[e]+=a[e+1],a[e+1]=""),""!==a[e]&&t.push(a[e])}return t}(e=e.replace(" "," ")),a=function(e){let t=[];for(let n=0;n<e.length;n++){let a=e[n];if(void 0!==a&&""!==a){if(!1===ga.test(a)||!1===ha.test(a)){if(t[t.length-1]){t[t.length-1]+=a;continue}if(e[n+1]){e[n+1]=a+e[n+1];continue}}t.push(a)}}return t}(n);if(a=function(e,t){const n=t.methods.one.tokenize.isSentence,a=t.model.one.abbreviations||new Set;let r=[];for(let t=0;t<e.length;t++){let o=e[t];e[t+1]&&!1===n(o,a)?e[t+1]=o+(e[t+1]||""):o&&o.length>0&&(r.push(o),e[t]="")}return r}(a,t),a=function(e){let t=[];for(let n=0;n<e.length;n+=1){let a=e[n].match(pa);if(null!==a&&1===a.length){if(ba(e[n+1])&&e[n+1].length<280){e[n]+=e[n+1],t.push(e[n]),e[n+1]="",n+=1;continue}if(ba(e[n+2])){let a=e[n+1]+e[n+2];if(a.length<280){e[n]+=a,t.push(e[n]),e[n+1]="",e[n+2]="",n+=2;continue}}}t.push(e[n])}return t}(a),a=function(e){let t=[];for(let n=0;n<e.length;n+=1){let a=e[n].match(ya);null!==a&&1===a.length&&e[n+1]&&e[n+1].length<250&&null!==e[n+1].match(va)&&1===a.length&&!ya.test(e[n+1])?(e[n]+=e[n+1],t.push(e[n]),e[n+1]="",n+=1):t.push(e[n])}return t}(a),0===a.length)return[e];for(let e=1;e<a.length;e+=1){let t=a[e].match(ka);null!==t&&(a[e-1]+=t[0],a[e]=a[e].replace(ka,""))}return a},isSentence:function(e,t){if(!1===$a.test(e))return!1;if(!0===Sa.test(e))return!1;if(3===e.length&&Ma.test(e))return!1;if(!0===Ka.test(e))return!1;let n=e.replace(/[.!?\u203D\u2E18\u203C\u2047-\u2049] *$/,"").split(" "),a=n[n.length-1].toLowerCase();return!0!==t.hasOwnProperty(a)||!0!==La.test(e)},splitTerms:function(e,t){let n=[],a=[];if("number"==typeof(e=e||"")&&(e=String(e)),function(e){return"[object Array]"===Object.prototype.toString.call(e)}(e))return e;const r=e.split(Ia);for(let e=0;e<r.length;e++)!0!==Pa(r[e],t)?a.push(r[e]):a=a.concat(Aa(r[e]));let o="";for(let e=0;e<a.length;e++){let t=a[e];!0===ja.test(t)&&!1===Da.hasOwnProperty(t)&&!1===Na.test(t)?(n.length>0?(n[n.length-1]+=o,n.push(t)):n.push(o+t),o=""):o+=t}return o&&(0===n.length&&(n[0]=""),n[n.length-1]+=o),n=function(e){for(let t=1;t<e.length-1;t++)Ca.test(e[t])&&(e[t-1]+=e[t]+e[t+1],e[t]=null,e[t+1]=null);return e}(n),n=function(e){const t=/^[0-9]{1,4}(:[0-9][0-9])?([a-z]{1,2})? ?[-–—] ?$/,n=/^[0-9]{1,4}([a-z]{1,2})? ?$/;for(let a=0;a<e.length-1;a+=1)e[a+1]&&t.test(e[a])&&n.test(e[a+1])&&(e[a]=e[a]+e[a+1],e[a+1]=null);return e}(n),n=n.filter((e=>e)),n},splitWhitespace:(e,t)=>{let{str:n,pre:a,post:r}=function(e,t){let{prePunctuation:n,postPunctuation:a,emoticons:r}=t.one,o=e,i="",s="",l=Array.from(e);if(r.hasOwnProperty(e.trim()))return{str:e.trim(),pre:i,post:" "};let u=l.length;for(let e=0;e<u;e+=1){let e=l[0];if(!0!==n[e]){if(("+"===e||"-"===e)&&Ga.test(l[1]))break;if("'"===e&&3===e.length&&Ga.test(l[1]))break;if(Ha.test(e)||Ga.test(e))break;i+=l.shift()}}u=l.length;for(let e=0;e<u;e+=1){let e=l[l.length-1];if(!0!==a[e]){if(Ha.test(e)||Ga.test(e))break;"."===e&&!0===Ta.test(o)||"'"===e&&!0===xa.test(o)||(s=l.pop()+s)}}return""===(e=l.join(""))&&(o=o.replace(/ *$/,(e=>(s=e||"",""))),e=o,i=""),{str:e,pre:i,post:s}}(e,t);return{text:n,pre:a,post:r,tags:new Set}},fromString:function(e,t){const{methods:n,model:a}=t,{splitSentences:r,splitTerms:o,splitWhitespace:i}=n.one.tokenize;return e=r(e=e||"",t).map((e=>{let n=o(e,a);return n=n.map((e=>i(e,a))),n.forEach((e=>{Ba(e,t)})),n})),e}}}};let Wa={},Ua={};[[["approx","apt","bc","cyn","eg","esp","est","etc","ex","exp","prob","pron","gal","min","pseud","fig","jd","lat","lng","vol","fm","def","misc","plz","ea","ps","sec","pt","pref","pl","pp","qt","fr","sq","nee","ss","tel","temp","vet","ver","fem","masc","eng","adj","vb","rb","inf","situ","vivo","vitro","wr"]],[["dl","ml","gal","qt","pt","tbl","tsp","tbsp","km","dm","cm","mm","mi","td","hr","hrs","kg","hg","dg","cg","mg","µg","lb","oz","sq ft","hz","mps","mph","kmph","kb","mb","tb","lx","lm","fl oz","yb"],"Unit"],[["ad","al","arc","ba","bl","ca","cca","col","corp","ft","fy","ie","lit","ma","md","pd","tce"],"Noun"],[["adj","adm","adv","asst","atty","bldg","brig","capt","cmdr","comdr","cpl","det","dr","esq","gen","gov","hon","jr","llb","lt","maj","messrs","mlle","mme","mr","mrs","ms","mstr","phd","prof","pvt","rep","reps","res","rev","sen","sens","sfc","sgt","sir","sr","supt","surg"],"Honorific"],[["jan","feb","mar","apr","jun","jul","aug","sep","sept","oct","nov","dec"],"Month"],[["dept","univ","assn","bros","inc","ltd","co"],"Organization"],[["rd","st","dist","mt","ave","blvd","cl","cres","hwy","ariz","cal","calif","colo","conn","fla","fl","ga","ida","ia","kan","kans","minn","neb","nebr","okla","penna","penn","pa","dak","tenn","tex","ut","vt","va","wis","wisc","wy","wyo","usafa","alta","ont","que","sask"],"Place"]].forEach((e=>{e[0].forEach((t=>{Wa[t]=!0,Ua[t]="Abbreviation",void 0!==e[1]&&(Ua[t]=[Ua[t],e[1]])}))}));var qa=["anti","bi","co","contra","de","extra","infra","inter","intra","macro","micro","mis","mono","multi","peri","pre","pro","proto","pseudo","re","sub","supra","trans","tri","un","out","ex"].reduce(((e,t)=>(e[t]=!0,e)),{});let Ra={"!":"¡","?":"¿Ɂ",'"':'“”"❝❞',"'":"‘‛❛❜’","-":"—–",a:"ªÀÁÂÃÄÅàáâãäåĀāĂăĄąǍǎǞǟǠǡǺǻȀȁȂȃȦȧȺΆΑΔΛάαλАаѦѧӐӑӒӓƛæ",b:"ßþƀƁƂƃƄƅɃΒβϐϦБВЪЬвъьѢѣҌҍ",c:"¢©ÇçĆćĈĉĊċČčƆƇƈȻȼͻͼϲϹϽϾСсєҀҁҪҫ",d:"ÐĎďĐđƉƊȡƋƌ",e:"ÈÉÊËèéêëĒēĔĕĖėĘęĚěƐȄȅȆȇȨȩɆɇΈΕΞΣέεξϵЀЁЕеѐёҼҽҾҿӖӗễ",f:"ƑƒϜϝӺӻҒғſ",g:"ĜĝĞğĠġĢģƓǤǥǦǧǴǵ",h:"ĤĥĦħƕǶȞȟΉΗЂЊЋНнђћҢңҤҥҺһӉӊ",I:"ÌÍÎÏ",i:"ìíîïĨĩĪīĬĭĮįİıƖƗȈȉȊȋΊΐΪίιϊІЇіїi̇",j:"ĴĵǰȷɈɉϳЈј",k:"ĶķĸƘƙǨǩΚκЌЖКжкќҚқҜҝҞҟҠҡ",l:"ĹĺĻļĽľĿŀŁłƚƪǀǏǐȴȽΙӀӏ",m:"ΜϺϻМмӍӎ",n:"ÑñŃńŅņŇňŉŊŋƝƞǸǹȠȵΝΠήηϞЍИЙЛПийлпѝҊҋӅӆӢӣӤӥπ",o:"ÒÓÔÕÖØðòóôõöøŌōŎŏŐőƟƠơǑǒǪǫǬǭǾǿȌȍȎȏȪȫȬȭȮȯȰȱΌΘΟθοσόϕϘϙϬϴОФоѲѳӦӧӨөӪӫ",p:"ƤΡρϷϸϼРрҎҏÞ",q:"Ɋɋ",r:"ŔŕŖŗŘřƦȐȑȒȓɌɍЃГЯгяѓҐґ",s:"ŚśŜŝŞşŠšƧƨȘșȿЅѕ",t:"ŢţŤťŦŧƫƬƭƮȚțȶȾΓΤτϮТт",u:"ÙÚÛÜùúûüŨũŪūŬŭŮůŰűŲųƯưƱƲǓǔǕǖǗǘǙǚǛǜȔȕȖȗɄΰυϋύ",v:"νѴѵѶѷ",w:"ŴŵƜωώϖϢϣШЩшщѡѿ",x:"×ΧχϗϰХхҲҳӼӽӾӿ",y:"ÝýÿŶŷŸƳƴȲȳɎɏΎΥΫγψϒϓϔЎУучўѰѱҮүҰұӮӯӰӱӲӳ",z:"ŹźŻżŽžƵƶȤȥɀΖ"},Qa={};Object.keys(Ra).forEach((function(e){Ra[e].split("").forEach((function(t){Qa[t]=e}))}));const Za=/\//,Xa=/[a-z]\.[a-z]/i,_a=/[0-9]/,Ya=function(e,t){let n=e.normal||e.text||e.machine;const a=t.model.one.aliases;if(a.hasOwnProperty(n)&&(e.alias=e.alias||[],e.alias.push(a[n])),Za.test(n)&&!Xa.test(n)&&!_a.test(n)){let t=n.split(Za);t.length<=3&&t.forEach((t=>{""!==(t=t.trim())&&(e.alias=e.alias||[],e.alias.push(t))}))}return e},er=/^\p{Letter}+-\p{Letter}+$/u,tr=function(e){let t=e.implicit||e.normal||e.text;t=t.replace(/['’]s$/,""),t=t.replace(/s['’]$/,"s"),t=t.replace(/([aeiou][ktrp])in'$/,"$1ing"),er.test(t)&&(t=t.replace(/-/g,"")),t=t.replace(/^[#@]/,""),t!==e.normal&&(e.machine=t)},nr=function(e,t){let n=e.docs;for(let a=0;a<n.length;a+=1)for(let r=0;r<n[a].length;r+=1)t(n[a][r],e.world)},ar={alias:e=>nr(e,Ya),machine:e=>nr(e,tr),normal:e=>nr(e,Ba),freq:function(e){let t=e.docs,n={};for(let e=0;e<t.length;e+=1)for(let a=0;a<t[e].length;a+=1){let r=t[e][a],o=r.machine||r.normal;n[o]=n[o]||0,n[o]+=1}for(let e=0;e<t.length;e+=1)for(let a=0;a<t[e].length;a+=1){let r=t[e][a],o=r.machine||r.normal;r.freq=n[o]}},offset:function(e){let t=0,n=0,a=e.document;for(let e=0;e<a.length;e+=1)for(let r=0;r<a[e].length;r+=1){let o=a[e][r];o.offset={index:n,start:t+o.pre.length,length:o.text.length},t+=o.pre.length+o.text.length+o.post.length,n+=1}},index:function(e){let t=e.document;for(let e=0;e<t.length;e+=1)for(let n=0;n<t[e].length;n+=1)t[e][n].index=[e,n]},wordCount:function(e){let t=0,n=e.docs;for(let e=0;e<n.length;e+=1)for(let a=0;a<n[e].length;a+=1)""!==n[e][a].normal&&(t+=1,n[e][a].wordCount=t)}};var rr={compute:ar,methods:Ja,model:{one:{aliases:{"&":"and","@":"at","%":"percent",plz:"please",bein:"being"},abbreviations:Wa,prefixes:qa,suffixes:{like:!0,ish:!0,less:!0,able:!0,elect:!0,type:!0,designate:!0},prePunctuation:{"#":!0,"@":!0,_:!0,"°":!0,"​":!0,"‌":!0,"‍":!0,"\ufeff":!0},postPunctuation:{"%":!0,_:!0,"°":!0,"​":!0,"‌":!0,"‍":!0,"\ufeff":!0},lexicon:Ua,unicode:Qa,emoticons:{"<3":!0,"</3":!0,"<\\3":!0,":^P":!0,":^p":!0,":^O":!0,":^3":!0}}},hooks:["alias","machine","index","id"]};var or={typeahead:function(e){const t=e.model.one.typeahead,n=e.docs;if(0===n.length||0===Object.keys(t).length)return;let a=n[n.length-1]||[],r=a[a.length-1];if(!r.post&&t.hasOwnProperty(r.normal)){let n=t[r.normal];r.implicit=n,r.machine=n,r.typeahead=!0,e.compute.preTagger&&e.last().unTag("*").compute(["lexicon","preTagger"])}}};const ir=function(){const e=this.docs;if(0===e.length)return this;let t=e[e.length-1]||[],n=t[t.length-1];return!0===n.typeahead&&n.machine&&(n.text=n.machine,n.normal=n.machine),this},sr={safe:!0,min:3};var lr={typeahead:function(e=[],t={}){let n=this.model();var a;t=Object.assign({},sr,t),a=e,"[object Object]"===Object.prototype.toString.call(a)&&(Object.assign(n.one.lexicon,e),e=Object.keys(e));let r=function(e,t,n){let a={},r=[],o=n.prefixes||{};return e.forEach((e=>{let i=(e=e.toLowerCase().trim()).length;t.max&&i>t.max&&(i=t.max);for(let s=t.min;s<i;s+=1){let i=e.substring(0,s);t.safe&&n.model.one.lexicon.hasOwnProperty(i)||(!0!==o.hasOwnProperty(i)&&!0!==a.hasOwnProperty(i)?a[i]=e:r.push(i))}})),a=Object.assign({},o,a),r.forEach((e=>{delete a[e]})),a}(e,t,this.world());return Object.keys(r).forEach((e=>{n.one.typeahead.hasOwnProperty(e)?delete n.one.typeahead[e]:n.one.typeahead[e]=r[e]})),this}};var ur={model:{one:{typeahead:{}}},api:function(e){e.prototype.autoFill=ir},lib:lr,compute:or,hooks:["typeahead"]};d.extend(J),d.extend(vn),d.extend(Jt),d.extend(Hn),d.extend(la),d.plugin(ve),d.extend(rr),d.extend(Ce),d.plugin(p),d.extend(Be),d.extend(ur),d.extend(xe),d.extend(En);var cr={addendum:"addenda",corpus:"corpora",criterion:"criteria",curriculum:"curricula",genus:"genera",memorandum:"memoranda",opus:"opera",ovum:"ova",phenomenon:"phenomena",referendum:"referenda",alga:"algae",alumna:"alumnae",antenna:"antennae",formula:"formulae",larva:"larvae",nebula:"nebulae",vertebra:"vertebrae",analysis:"analyses",axis:"axes",diagnosis:"diagnoses",parenthesis:"parentheses",prognosis:"prognoses",synopsis:"synopses",thesis:"theses",neurosis:"neuroses",appendix:"appendices",index:"indices",matrix:"matrices",ox:"oxen",sex:"sexes",alumnus:"alumni",bacillus:"bacilli",cactus:"cacti",fungus:"fungi",hippopotamus:"hippopotami",libretto:"libretti",modulus:"moduli",nucleus:"nuclei",octopus:"octopi",radius:"radii",stimulus:"stimuli",syllabus:"syllabi",cookie:"cookies",calorie:"calories",auntie:"aunties",movie:"movies",pie:"pies",rookie:"rookies",tie:"ties",zombie:"zombies",leaf:"leaves",loaf:"loaves",thief:"thieves",foot:"feet",goose:"geese",tooth:"teeth",beau:"beaux",chateau:"chateaux",tableau:"tableaux",bus:"buses",gas:"gases",circus:"circuses",crisis:"crises",virus:"viruses",database:"databases",excuse:"excuses",abuse:"abuses",avocado:"avocados",barracks:"barracks",child:"children",clothes:"clothes",echo:"echoes",embargo:"embargoes",epoch:"epochs",deer:"deer",halo:"halos",man:"men",woman:"women",mosquito:"mosquitoes",mouse:"mice",person:"people",quiz:"quizzes",rodeo:"rodeos",shoe:"shoes",sombrero:"sombreros",stomach:"stomachs",tornado:"tornados",tuxedo:"tuxedos",volcano:"volcanoes"},dr={Comparative:"true¦bett1f0;arth0ew0in0;er",Superlative:"true¦earlier",PresentTense:"true¦bests,sounds",Condition:"true¦lest,unless",PastTense:"true¦began,came,d4had,kneel3l2m0sa4we1;ea0sg2;nt;eap0i0;ed;id",Participle:"true¦0:09;a06b01cZdXeat0fSgQhPoJprov0rHs7t6u4w1;ak0ithdra02o2r1;i02uY;k0v0;nd1pr04;ergoJoJ;ak0hHo3;e9h7lain,o6p5t4un3w1;o1um;rn;g,k;ol0reS;iQok0;ught,wn;ak0o1runk;ne,wn;en,wn;ewriNi1uJ;dd0s0;ut3ver1;do4se0t1;ak0h2;do2g1;roG;ne;ast0i7;iv0o1;ne,tt0;all0loBor1;bi3g2s1;ak0e0;iv0o9;dd0;ove,r1;a5eamt,iv0;hos0lu1;ng;e4i3lo2ui1;lt;wn;tt0;at0en,gun;r2w1;ak0ok0;is0;en",Gerund:"true¦accord0be0doin,go0result0stain0;ing",Expression:"true¦a0Yb0Uc0Sd0Oe0Mfarew0Lg0FhZjeez,lWmVnToOpLsJtIuFvEw7y0;a5e3i1u0;ck,p;k04p0;ee,pee;a0p,s;!h;!a,h,y;a5h2o1t0;af,f;rd up,w;atsoever,e1o0;a,ops;e,w;hoo,t;ery w06oi0L;gh,h0;! 0h,m;huh,oh;here nPsk,ut tut;h0ic;eesh,hh,it,oo;ff,h1l0ow,sst;ease,s,z;ew,ooey;h1i,mg,o0uch,w,y;h,o,ps;! 0h;hTmy go0wT;d,sh;a7evertheless,o0;!pe;eh,mm;ah,eh,m1ol0;!s;ao,fao;aCeBi9o2u0;h,mph,rra0zzC;h,y;l1o0;r6y9;la,y0;! 0;c1moCsmok0;es;ow;!p hip hoor0;ay;ck,e,llo,y;ha1i,lleluj0;ah;!ha;ah,ee4o1r0;eat scott,r;l1od0sh; grief,bye;ly;! whiz;ell;e0h,t cetera,ureka,ww,xcuse me;k,p;'oh,a0rat,uh;m0ng;mit,n0;!it;mon,o0;ngratulations,wabunga;a2oo1r0tw,ye;avo,r;!ya;h,m; 1h0ka,las,men,rgh,ye;!a,em,h,oy;la",Negative:"true¦n0;ever,o0;n,t",QuestionWord:"true¦how3wh0;at,e1ich,o0y;!m,se;n,re; come,'s",Reflexive:"true¦h4it5my5o1the0your2;ir1m1;ne3ur0;sel0;f,ves;er0im0;self",Plural:"true¦dick0gre0ones,records;ens","Unit|Noun":"true¦cEfDgChBinchAk9lb,m6newt5oz,p4qt,t1y0;ardEd;able1b0ea1sp;!l,sp;spo1;a,t,x;on9;!b,g,i1l,m,p0;h,s;!les;!b,elvin,g,m;!es;g,z;al,b;eet,oot,t;m,up0;!s",Value:"true¦a few",Imperative:"true¦bewa0come he0;re","Plural|Verb":"true¦leaves",Demonym:"true¦0:15;1:12;a0Vb0Oc0Dd0Ce08f07g04h02iYjVkTlPmLnIomHpEqatari,rCs7t5u4v3welAz2;am0Gimbabwe0;enezuel0ietnam0I;gAkrai1;aiwTex0hai,rinida0Ju2;ni0Prkmen;a5cotti4e3ingapoOlovak,oma0Spaniard,udRw2y0W;ede,iss;negal0Cr09;sh;mo0uT;o5us0Jw2;and0;a2eru0Fhilippi0Nortugu07uerto r0S;kist3lesti1na2raguay0;ma1;ani;ami00i2orweP;caragu0geri2;an,en;a3ex0Lo2;ngo0Drocc0;cedo1la2;gasy,y07;a4eb9i2;b2thua1;e0Cy0;o,t01;azakh,eny0o2uwaiI;re0;a2orda1;ma0Ap2;anO;celandic,nd4r2sraeli,ta01vo05;a2iB;ni0qi;i0oneU;aiAin2ondur0unO;di;amEe2hanai0reek,uatemal0;or2rm0;gi0;ilipino,ren8;cuadoVgyp4mira3ngli2sto1thiopi0urope0;shm0;ti;ti0;aPominUut3;a9h6o4roat3ub0ze2;ch;!i0;lom2ngol5;bi0;a6i2;le0n2;ese;lifor1m2na3;bo2eroo1;di0;angladeshi,el6o4r3ul2;gaE;azi9it;li2s1;vi0;aru2gi0;si0;fAl7merBngol0r5si0us2;sie,tr2;a2i0;li0;genti2me1;ne;ba1ge2;ri0;ni0;gh0r2;ic0;an",Organization:"true¦0:4Q;a3Tb3Bc2Od2He2Df27g1Zh1Ti1Pj1Nk1Ll1Gm12n0Po0Mp0Cqu0Br02sTtHuCv9w3xiaomi,y1;amaha,m1Bou1w1B;gov,tu3C;a4e2iki1orld trade organizati33;leaRped0O;lls fargo,st1;fie2Hinghou2R;l1rner br3U;gree3Jl street journ2Im1E;an halOeriz2Xisa,o1;dafo2Yl1;kswagMvo;b4kip,n2ps,s1;a tod3Aps;es3Mi1;lev3Fted natio3C;er,s; mobi32aco beRd bOe9gi frida3Lh3im horto3Amz,o1witt3D;shi49y1;ota,s r 05;e 1in lizzy;b3carpen3Jdaily ma3Dguess w2holli0s1w2;mashing pumpki35uprem0;ho;ea1lack eyed pe3Xyr0Q;ch bo3Dtl0;l2n3Qs1xas instrumen1U;co,la m1F;efoni0Kus;a8cientology,e5ieme2Ymirnoff,np,o3pice gir6quare0Ata1ubaru;rbuc1to34;ks;ny,undgard1;en;a2x pisto1;ls;g1Wrs;few2Minsbur31lesfor03msu2E;adiohead,b8e4o1yana3C;man empi1Xyal 1;b1dutch she4;ank;a3d 1max,vl20;bu1c2Ahot chili peppe2Ylobst2N;ll;ders dige1Ll madrid;c,s;ant3Aizn2Q;a8bs,e5fiz2Ihilip4i3r1;emier 1udenti1D;leagTo2K;nk floyd,zza hut; morrBs;psi2tro1uge0E;br33chi0Tn33;!co;lant2Un1yp16; 2ason27da2P;ld navy,pec,range juli2xf1;am;us;aAb9e6fl,h5i4o1sa,vid3wa;k2tre dame,vart1;is;ia;ke,ntendo,ss0QvZ;l,s;c,st1Otflix,w1; 1sweek;kids on the block,york0D;a,c;nd22s2t1;ional aca2Po,we0U;a,c02d0S;aDcdonalCe9i6lb,o3tv,y1;spa1;ce;b1Tnsanto,ody blu0t1;ley cr1or0T;ue;c2t1;as,subisO;helin,rosoft;dica2rcedes benz,talli1;ca;id,re;ds;cs milk,tt19z24;a3e1g,ittle caesa1P; ore09novo,x1;is,mark,us; 1bour party;pres0Dz boy;atv,fc,kk,lm,m1od1O;art;iffy lu0Roy divisi0Jpmorgan1sa;! cha09;bm,hop,k3n1tv;g,te1;l,rpol;ea;a5ewlett pack1Vi3o1sbc,yundai;me dep1n1P;ot;tac1zbollah;hi;lliburt08sbro;eneral 6hq,ithub,l5mb,o2reen d0Ou1;cci,ns n ros0;ldman sachs,o1;dye1g0H;ar;axo smith kli04encoW;electr0Nm1;oto0Z;a5bi,c barcelo4da,edex,i2leetwood m03o1rito l0G;rd,xcY;at,fa,nancial1restoZ; tim0;na;cebook,nnie mae;b0Asa,u3xxon1; m1m1;ob0J;!rosceptics;aiml0De5isney,o4u1;nkin donu2po0Zran dur1;an;ts;j,w jon0;a,f lepp12ll,peche mode,r spieg02stiny's chi1;ld;aJbc,hFiDloudflaCnn,o3r1;aigsli5eedence clearwater reviv1ossra09;al;c7inba6l4m1o0Est09;ca2p1;aq;st;dplSg1;ate;se;a c1o chanQ;ola;re;a,sco1tigroup;! systems;ev2i1;ck fil a,na daily;r1y;on;d2pital o1rls jr;ne;bury,ill1;ac;aEbc,eBf9l5mw,ni,o1p,rexiteeU;ei3mbardiIston 1;glo1pizza;be;ng;o2ue c1;roV;ckbuster video,omingda1;le; g1g1;oodriL;cht2e ge0rkshire hathaw1;ay;el;cardi,idu,nana republ3s1xt5y5;f,kin robbi1;ns;ic;bYcTdidSerosmith,iRlKmEnheuser busDol,ppleAr6s4u3v2y1;er;is,on;di,todesk;hland o1sociated E;il;b3g2m1;co;os;ys; compu1be0;te1;rs;ch;c,d,erican3t1;!r1;ak; ex1;pre1;ss; 5catel2ta1;ir;! lu1;ce1;nt;jazeera,qae1;da;g,rbnb;as;/dc,a3er,tivision1;! blizz1;ard;demy of scienc0;es;ba",Possessive:"true¦its,my,our0thy;!s","Noun|Verb":"true¦0:9W;1:AA;2:96;3:A3;4:9R;5:A2;6:9K;7:8N;8:7L;9:A8;A:93;B:8D;C:8X;a9Ob8Qc7Id6Re6Gf5Sg5Hh55i4Xj4Uk4Rl4Em40n3Vo3Sp2Squ2Rr21s0Jt02u00vVwGyFzD;ip,oD;ne,om;awn,e6Fie68;aOeMhJiHoErD;ap,e9Oink2;nd0rDuC;kDry,sh5Hth;!shop;ck,nDpe,re,sh;!d,g;e86iD;p,sD;k,p0t2;aDed,lco8W;r,th0;it,lk,rEsDt4ve,x;h,te;!ehou1ra9;aGen5FiFoD;iDmAte,w;ce,d;be,ew,sA;cuum,l4B;pDr7;da5gra6Elo6A;aReQhrPiOoMrGuEwiDy5Z;n,st;nDrn;e,n7O;aGeFiEoDu6;t,ub2;bu5ck4Jgg0m,p;at,k,nd;ck,de,in,nsDp,v7J;f0i8R;ll,ne,p,r4Yss,t94uD;ch,r;ck,de,e,le,me,p,re;e5Wow,u6;ar,e,ll,mp0st,xt;g,lDng2rg7Ps5x;k,ly;a0Sc0Ne0Kh0Fi0Dk0Cl0Am08n06o05pXquaBtKuFwD;ea88iD;ng,pe,t4;bGit,m,ppErD;fa3ge,pri1v2U;lDo6S;e6Py;!je8;aMeLiKoHrEuDy2;dy,ff,mb2;a85eEiDo5Pugg2;ke,ng;am,ss,t4;ckEop,p,rD;e,m;ing,pi2;ck,nk,t4;er,m,p;ck,ff,ge,in,ke,lEmp,nd,p2rDte,y;!e,t;k,l;aJeIiHlGoFrDur,y;ay,e56inDu3;g,k2;ns8Bt;a5Qit;ll,n,r87te;ed,ll;m,n,rk;b,uC;aDee1Tow;ke,p;a5Je4FiDo53;le,rk;eep,iDou4;ce,p,t;ateboa7Ii;de,gnDl2Vnk,p,ze;!al;aGeFiEoDuff2;ck,p,re,w;ft,p,v0;d,i3Ylt0;ck,de,pe,re,ve;aEed,nDrv1It;se,t2N;l,r4t;aGhedu2oBrD;aEeDibb2o3Z;en,w;pe,t4;le,n,r2M;cDfegua72il,mp2;k,rifi3;aZeHhy6LiGoEuD;b,in,le,n,s5X;a6ck,ll,oDpe,u5;f,t;de,ng,ot,p,s1W;aTcSdo,el,fQgPje8lOmMnLo17pJque6sFturn,vDwa6V;eDi27;al,r1;er74oFpe8tEuD;lt,me;!a55;l71rt;air,eaDly,o53;l,t;dezvo2Zt;aDedy;ke,rk;ea1i4G;a6Iist0r5N;act6Yer1Vo71uD;nd,se;a38o6F;ch,s6G;c1Dge,iEke,lly,nDp1Wt1W;ge,k,t;n,se;es6Biv0;a04e00hYiXlToNrEsy4uD;mp,n4rcha1sh;aKeIiHoDu4O;be,ceFdu3fi2grDje8mi1p,te6;amDe6W;!me;ed,ss;ce,de,nt;sDy;er6Cs;cti3i1;iHlFoEp,re,sDuCw0;e,i5Yt;l,p;iDl;ce,sh;nt,s5V;aEce,e32uD;g,mp,n7;ce,nDy;!t;ck,le,n17pe,tNvot;a1oD;ne,tograph;ak,eFnErDt;fu55mA;!c32;!l,r;ckJiInHrFsEtDu1y;ch,e9;s,te;k,tD;!y;!ic;nt,r,se;!a7;bje8ff0il,oErDutli3Qver4B;bAd0ie9;ze;a4ReFoDur1;d,tD;e,i3;ed,gle8tD;!work;aMeKiIoEuD;rd0;ck,d3Rld,nEp,uDve;nt,th;it5EkD;ey;lk,n4Brr5CsDx;s,ta2B;asuBn4UrDss;ge,it;il,nFp,rk3WsEtD;ch,t0;h,k,t0;da5n0oeuvB;aLeJiHoEuD;mp,st;aEbby,ck,g,oDve;k,t;d,n;cDe,ft,mAnIst;en1k;aDc0Pe4vK;ch,d,k,p,se;bFcEnd,p,t4uD;gh,n4;e,k;el,o2U;eEiDno4E;ck,d,ll,ss;el,y;aEo1OuD;i3mp;m,zz;mpJnEr46ssD;ue;c1Rdex,fluGha2k,se2HteDvoi3;nt,rD;e6fa3viD;ew;en3;a8le2A;aJeHiGoEuD;g,nt;l3Ano2Dok,pDr1u1;!e;ghli1Fke,nt,re,t;aDd7lp;d,t;ck,mGndFrEsh,tDu9;ch,e;bo3Xm,ne4Eve6;!le;!m0;aMear,ift,lKossJrFuD;arDe4Alp,n;antee,d;aFiEoDumb2;uCwth;ll,nd,p;de,sp;ip;aBoDue;ss,w;g,in,me,ng,s,te,ze;aZeWiRlNoJrFuD;ck,el,nDss,zz;c38d;aEoDy;st,wn;cDgme,me,nchi1;tuB;cFg,il,ld,rD;ce,e29mDwa31;!at;us;aFe0Vip,oDy;at,ck,od,wD;!er;g,ke,me,re,sh,vo1E;eGgFlEnDre,sh,t,x;an3i0Q;e,m,t0;ht,uB;ld;aEeDn3;d,l;r,tuB;ce,il,ll,rm,vo2W;cho,d7ffe8nMsKxFyeD;!baD;ll;cGerci1hFpDtra8;eriDo0W;en3me9;au6ibA;el,han7u1;caDtima5;pe;count0d,vy;a01eSiMoJrEuDye;b,el,mp,pli2X;aGeFiEoD;ne,p;ft,ll,nk,p,ve;am,ss;ft,g,in;cEd7ubt,wnloD;ad;k,u0E;ge6p,sFt4vD;e,iDor3;de;char7gui1h,liEpD;at4lay,u5;ke;al,bKcJfeIlGmaCposAsEtaD;il;e07iD;gn,re;ay,ega5iD;ght;at,ct;li04rea1;a5ut;b,ma7n3rDte;e,t;a0Eent0Dh06irc2l03oKrFuD;be,e,rDt;b,e,l,ve;aGeFoEuDy;sh;p,ss,wd;dAep;ck,ft,sh;at,de,in,lTmMnFordina5py,re,st,uDv0;gh,nDp2rt;s01t;ceHdu8fli8glomeIsFtDveN;a8rD;a6ol;e9tru8;ct;ntDrn;ra5;bHfoGmFpD;leDouCromi1;me9;aCe9it,u5;rt;at,iD;ne;lap1oD;r,ur;aEiDoud,ub;ck,p;im,w;aEeDip;at,ck,er;iGllen7nErD;ge,m,t;ge,nD;el;n,r;er,re;ke,ll,mp,noe,pGrXsFtEuDve;se,ti0I;alog,ch;h,t;!tuB;re;a03eZiXlToPrHuEyD;pa11;bb2ck2dgEff0mp,rDst,zz;den,n;et;anJeHiFoadEuD;i1sh;ca6;be,d7;ge;aDed;ch,k;ch,d;aFg,mb,nEoDrd0tt2x,ycott;k,st,t;d,e;rd,st;aFeCiDoYur;nk,tz;nd;me;as,d,ke,nd,opsy,tD;!ch,e;aFef,lt,nDt;d,efA;it;r,t;ck,il,lan3nIrFsEtt2;le;e,h;!gDk;aDe;in;!d,g,k;bu1c05dZge,iYlVnTppQrLsIttGucEwaD;rd;tiD;on;aDempt;ck;k,sD;i6ocia5;st;chFmD;!oD;ur;!iD;ve;eEroa4;ch;al;chDg0sw0;or;aEt0;er;rm;d,m,r;dreHvD;an3oD;ca5;te;ce;ss;cDe,he,t;eFoD;rd,u9;nt;nt,ss;se",Actor:"true¦0:7B;1:7G;2:6A;3:7F;4:7O;5:7K;a6Nb62c4Ud4Be41f3Sg3Bh30i2Uj2Qkin2Pl2Km26n1Zo1Sp0Vqu0Tr0JsQtJuHvEw8yo6;gi,ut6;h,ub0;aAe9i8o7r6;estl0it0;m2rk0;fe,nn0t2Bza2H;atherm2ld0;ge earn0it0nder0rri1;eter7i6oyF;ll5Qp,s3Z;an,ina2U;n6s0;c6Uder03;aoisea23e9herapi5iktok0o8r6ut1yco6S;a6endseLo43;d0mp,nscri0Bvel0;ddl0u1G;a0Qchn7en6na4st0;ag0;i3Oo0D;aiXcUeRhPiMki0mu26oJpGquaFtBu7wee6;p0theart;lt2per7r6;f0ge6Iviv1;h6inten0Ist5Ivis1;ero,um2;a8ep7r6;ang0eam0;bro2Nc2Ofa2Nmo2Nsi20;ff0tesm2;tt0;ec7ir2Do6;kesp59u0M;ia5Jt3;l7me6An,rcere6ul;r,ss;di0oi5;n7s6;sy,t0;g0n0;am2ephe1Iow6;girl,m2r2Q;cretInior cit3Fr6;gea4v6;a4it1;hol4Xi7reen6ulpt1;wr2C;e01on;l1nt;aEe9o8u6;l0nn6;er up,ingE;g40le mod3Zof0;a4Zc8fug2Ppo32searQv6;ere4Uolution6;ary;e6luYru22;ptio3T;bbi,dic5Vpp0;arter6e2Z;back;aYeWhSiRlOoKr8sycho7u6;nk,p31;logi5;aGeDiBo6;d9fess1g7ph47s6;pe2Ktitu51;en6ramm0;it1y;igy,uc0;est4Nme mini0Unce6s3E;!ss;a7si6;de4;ch0;ctiti39nk0P;dca0Oet,li6pula50rnst42;c2Itic6;al scie6i2;nti5;a6umb0;nn0y6;er,ma4Lwright;lgrim,one0;a8iloso7otogra7ra6ysi1V;se;ph0;ntom,rmaci5;r6ssi1T;form0s4O;i3El,nel3Yr8st1tr6wn;i6on;arWot;ent4Wi42tn0;ccupa4ffBp8r7ut6;ca5l0B;ac4Iganiz0ig2Fph2;er3t6;i1Jomet6;ri5;ic0spring;aBe9ie4Xo7u6;n,rser3J;b6mad,vi4V;le2Vo4D;i6mesis,phew;ce,ghb1;nny,rr3t1X;aEeDiAo7u6yst1Y;m8si16;der3gul,m7n6th0;arDk;!my;ni7s6;f02s0Jt0;on,st0;chan1Qnt1rcha4;gi9k0n8rtyr,t6y1;e,riar6;ch;ag0iac;ci2stra3I;a7e2Aieutena4o6;rd,s0v0;bor0d7ndlo6ss,urea3Fwy0ym2;rd;!y;!s28;e8o7u6;ggl0;gg0urna2U;st0;c3Hdol,llu3Ummigra4n6; l9c1Qfa4habi42nov3s7ve6;nt1stig3;pe0Nt6;a1Fig3ru0M;aw;airFeBistoAo8u6ygie1K;man6sba2H;!ita8;bo,st6usekN;age,e3P;ri2;ir,r6;m7o6;!ine;it;dress0sty2C;aLeIhostGirl26ladi3oCrand7u6;e5ru;c9daug0Jfa8m7pa6s2Y;!re4;a,o6;th0;hi1B;al7d6lf0;!de3A;ie,k6te26;eep0;!wr6;it0;isha,n6;i6tl04;us;mbl0rden0;aDella,iAo7r6;eela2Nie1P;e,re6ster pare4;be1Hm2r6st0;unn0;an2ZgZlmm17nanci0r6tt0;e6st la2H; marsh2OfigXm2;rm0th0;conoEdDlectriCm8n7x6;amin0cellency,i2A;emy,trepreneur,vironmenta1J;c8p6;er1loye6;e,r;ee;ci2;it1;mi5;aKeBi8ork,ri7u6we02;de,tche2H;ft0v0;ct3eti7plom2Hre6va;ct1;ci2ti2;aDcor3fencCi0InAput9s7tectLvel6;op0;ce1Ge6ign0;rt0;ee,y;iz6;en;em2;c1Ml0;d8nc0redev7ug6;ht0;il;!dy;a06e04fo,hXitizenWlToBr9u6;r3stomer6;! representat6;ive;e3it6;ic;lJmGnAord9rpor1Nu7w6;boy,ork0;n6ri0;ciTte1Q;in3;fidantAgressSs9t6;e0Kr6;ibut1o6;ll0;tab13ul1O;!e;edi2m6pos0rade;a0EeQissi6;on0;leag8on7um6;ni5;el;ue;e6own;an0r6;ic,k;!s;a9e7i6um;ld;erle6f;ad0;ir7nce6plFract0;ll1;m2wI;lebri6o;ty;dBptAr6shi0;e7pe6;nt0;r,t6;ak0;ain;et;aMeLiJlogg0oErBu6;dd0Fild0rgl9siness6;m2p7w6;om2;ers05;ar;i7o6;!k0th0;cklay0de,gadi0;hemi2oge8y6;!frie6;nd;ym2;an;cyc6sR;li5;atbox0ings;by,nk0r6;b0on7te6;nd0;!e07;c04dWge4nQpLrHsFtAu7yatull6;ah;nt7t6;h1oG;!ie;h8t6;e6orney;nda4;ie5le6;te;sis00tron6;aut,om0;chbis8isto7tis6;an,t;crU;hop;ost9p6;ari6rentiS;ti6;on;le;a9cest1im3nou8y6;bo6;dy;nc0;ly5rc6;hi5;mi8v6;entur0is1;er;ni7r6;al;str3;at1;or;counBquaintanArob9t6;ivi5or,re6;ss;st;at;ce;ta4;nt","Adj|Noun":"true¦0:16;a1Db17c0Ud0Re0Mf0Dg0Ah08i06ju05l02mWnUoSpNrIsBt7u4v1watershed;a1ision0Z;gabo4nilla,ria1;b0Vnt;ndergr1pstairs;adua14ou1;nd;a3e1oken,ri0;en,r1;min0rori13;boo,n;age,e5ilv0Flack,o3quat,ta2u1well;bordina0Xper5;b0Lndard;ciali0Yl1vereign;e,ve16;cret,n1ri0;ior;a4e2ou1ubbiL;nd,tiY;ar,bBl0Wnt0p1side11;resent0Vublican;ci0Qsh;a4eriodic0last0Zotenti0r1;emi2incip0o1;!fession0;er,um;rall4st,tie0U;ff1pposi0Hv0;ens0Oi0C;agg01ov1uts;el;a5e3iniatJo1;bi01der07r1;al,t0;di1tr0N;an,um;le,riG;attOi2u1;sh;ber0ght,qC;stice,veniT;de0mpressioYn1;cumbe0Edividu0no0Dsta0Eterim;alf,o1umdrum;bby,melF;en2old,ra1;ph0Bve;er0ious;a7e5i4l3u1;git03t1;ure;uid;ne;llow,m1;aFiL;ir,t,vo1;riOuriO;l3p00x1;c1ecutUpeV;ess;d1iK;er;ar2e1;mographUrivO;k,l2;hiGlassSo2rude,unn1;ing;m5n1operK;creCstitueOte2vertab1;le;mpor1nt;ary;ic,m2p1;anion,lex;er2u1;ni8;ci0;al;e5lank,o4r1;i2u1;te;ef;ttom,urgeois;st;cadem9d6l2ntarct9r1;ab,ct8;e3tern1;at1;ive;rt;oles1ult;ce1;nt;ic","Adj|Past":"true¦0:4Q;1:4C;2:4H;3:4E;a44b3Tc36d2Je29f20g1Wh1Si1Jj1Gkno1Fl1Am15n12o0Xp0Mqu0Kr08sLtEuAv9w4yellow0;a7ea6o4rinkl0;r4u3Y;n,ri0;k31th3;rp0sh0tZ;ari0e1O;n5p4s0;d1li1Rset;cov3derstood,i4;fi0t0;a8e3Rhr7i6ouTr4urn0wi4C;a4imm0ou2G;ck0in0pp0;ed,r0;eat2Qi37;m0nn0r4;get0ni2T;aOcKeIhGimFm0Hoak0pDt7u4;bsid3Ogge44s4;pe4ta2Y;ct0nd0;a8e7i2Eok0r5u4;ff0mp0nn0;ength2Hip4;ed,p0;am0reotyp0;in0t0;eci4ik0oH;al3Efi0;pRul1;a4ock0ut;d0r0;a4c1Jle2t31;l0s3Ut0;a6or5r4;at4e25;ch0;r0tt3;t4ut0;is2Mur1;aEe5o4;tt0;cAdJf2Bg9je2l8m0Knew0p7qu6s4;eTpe2t4;or0ri2;e3Dir0;e1lac0;at0e2Q;i0Rul1;eiv0o4ycl0;mme2Lrd0v3;in0lli0ti2A;a4ot0;li28;aCer30iBlAo9r5u4;mp0zzl0;e6i2Oo4;ce2Fd4lo1Anou30pos0te2v0;uc0;fe1CocCp0Iss0;i2Kli1L;ann0e2CuS;ck0erc0ss0;ck0i2Hr4st0;allLk0;bse7c6pp13rgan2Dver4;lo4whelm0;ok0;cupi0;rv0;aJe5o4;t0uri1A;ed0gle2;a6e5ix0o4ut0ys1N;di1Nt15u26;as0Clt0;n4rk0;ag0ufact0A;e6i5o4;ad0ck0st,v0;cens0m04st0;ft,v4;el0;tt0wn;a5o15u4;dg0s1B;gg0;llumSmpAn4sol1;br0cre1Ldebt0f8jZspir0t5v4;it0olv0;e4ox0Y;gr1n4re23;d0si15;e2l1o1Wuri1;li0o01r4;ov0;a6e1o4um03;ok0r4;ri0Z;mm3rm0;i6r5u4;a1Bid0;a0Ui0Rown;ft0;aAe9i8l6oc0Ir4;a4i0oz0Y;ctHg19m0;avo0Ju4;st3;ni08tt0x0;ar0;d0il0sc4;in1;dCl1mBn9quipp0s8x4;agger1c6p4te0T;a0Se4os0;ct0rie1D;it0;cap0tabliZ;cha0XgFha1As4;ur0;a0Zbarra0N;i0Buc1;aMeDi5r4;a01i0;gni08miniSre2s4;a9c6grun0Ft4;o4re0Hu17;rt0;iplWou4;nt0r4;ag0;bl0;cBdRf9l8p7ra6t5v4;elop0ot0;ail0ermQ;ng0;re07;ay0ight0;e4in0o0M;rr0;ay0enTor1;m5t0z4;ed,zl0;ag0p4;en0;aPeLhIlHo9r6u4;lt4r0stom03;iv1;a5owd0u4;sh0;ck0mp0;d0loAm7n4ok0v3;centr1f5s4troC;id3olid1;us0;b5pl4;ic1;in0;r0ur0;assi9os0utt3;ar5i4;ll0;g0m0;lebr1n6r4;ti4;fi0;tralJ;g0lcul1;aDewild3iCl9o7r5urn4;ed,t;ok4uis0;en;il0r0t4und;tl0;e5i4;nd0;ss0;as0;ffl0k0laMs0tt3;bPcNdKfIg0lFmaz0nDppBrm0ss9u5wa4;rd0;g5thor4;iz0;me4;nt0;o6u4;m0r0;li0re4;ci1;im1ticip1;at0;a5leg0t3;er0;rm0;fe2;ct0;ju5o7va4;nc0;st0;ce4knowledg0;pt0;and5so4;rb0;on0;ed",Singular:"true¦0:5J;1:5H;2:4W;3:4S;4:52;5:57;6:5L;7:56;8:5B;a52b4Lc3Nd35e2Xf2Og2Jh28in24j23k22l1Um1Ln1Ho1Bp0Rqu0Qr0FsZtMuHvCw9x r58yo yo;a9ha3Po3Q;f3i4Rt0Gy9;! arou39;arCeAideo ga2Qo9;cabu4Jl5C;gOr9t;di4Zt1Y;iety,ni4P;nBp30rAs 9;do43s5E;bani1in0;coordinat3Ader9;estima1to24we41; rex,aKeJhHiFoErBuAv9;! show;m2On2rntLto1D;agedy,ib9o4E;e,u9;n0ta46;ni1p2rq3L;c,er,m9;etF;ing9ree26;!y;am,mp3F;ct2le6x return;aNcMeKhor4QiJkHoGpin off,tDuBy9;ll9ner7st4T;ab2X;b9i1n28per bowl,rro1X;st3Ltot0;atAipe2Go1Lrate7udent9;! lo0I;i39u1;ft ser4Lmeo1I;elet5i9;ll,r3V;b38gn2Tte;ab2Jc9min3B;t,urity gua2N;e6ho2Y;bbatic0la3Jndwi0Qpi5;av5eDhetor2iAo9;de6om,w;tAv9;erb2C;e,u0;bDcBf9publ2r10spi1;er9orm3;e6r0;i9ord label;p2Ht0;a1u46;estion mark,ot2F;aPeMhoLiIlGoErAu9yram1F;ddi3HpErpo1Js3J;eBo9;bl3Zs9;pe3Jta1;dic1Rmi1Fp1Qroga8ss relea1F;p9rt0;py;a9ebisci1;q2Dte;cn2eAg9;!gy;!r;ne call,tocoK;anut,dAr9t0yo1;cen3Jsp3K;al,est0;nop4rAt9;e,hog5;adi11i2V;atme0bj3FcBpia1rde0thers,utspok5ve9wn3;n,r9;ti0Pview;cuAe9;an;pi3;arBitAot9umb3;a2Fhi2R;e,ra1;cot2ra8;aFeCiAo9ur0;nopo4p18rni2Nsq1Rti36uld;c,li11n0As9tt5;chief,si34;dAnu,t9;al,i3;al,ic;gna1mm0nd15rsupi0te9yf4;ri0;aDegCiBu9;ddi1n9;ch;me,p09; Be0M;bor14y9; 9er;up;eyno1itt5;el4ourn0;cBdices,itia8ni25sAtel0Lvert9;eb1J;e28titu1;en8i2T;aIeEighDoAu9;man right,s22;me9rmoFsp1Ftb0K;! r9;un; scho0YriY;a9i1N;d9v5; start,pho9;ne;ndful,sh brown,v5ze;aBelat0Ilaci3r9ul4yp1S;an9enadi3id;a1Cd slam,ny;df4r9;l2ni1I;aGeti1HiFlu1oCrAun9;er0;ee market,i9onti3;ga1;l4ur9;so9;me;ePref4;br2mi4;conoFffi7gg,lecto0Rmbas1EnCpidem2s1Zth2venBxAyel9;id;ampZempl0Nte6;i19t;er7terp9;ri9;se;my;eLiEoBr9ump tru0U;agonf4i9;er,ve thru;cAg7i4or,ssi3wn9;side;to0EumenE;aEgniDnn3sAvide9;nd;conte6incen8p9tri11;osi9;ti0C;ta0H;le0X;athBcAf9ni0terre6;ault 05err0;al,im0;!b9;ed;aWeThMiLlJoDr9;edit caBuc9;ib9;le;rd;efficDke,lCmmuniqLnsApi3rr0t0Xus9yo1;in;erv9uI;ato02;ic,lQ;ie6;er7i9oth;e6n2;ty,vil wM;aDeqCick5ocoBr9;istmas car9ysanthemum;ol;la1;ue;ndeli3racteri9;st2;iAllEr9;e0tifica1;liZ;hi3nFpErCt9ucus;erpi9hedr0;ll9;ar;!bohyd9ri3;ra1;it0;aAe,nib0t9;on;l,ry;aMeLiop2leJoHrDu9;nny,r9tterf4;g9i0;la9;ry;eakAi9;ck;fa9throB;st;dy,ro9wl;ugh;mi9;sh;an,l4;nkiArri3;er;ng;cSdMlInFppeti1rDsBtt2utop9;sy;ic;ce6pe9;ct;r9sen0;ay;ecAoma4tiA;ly;do1;i5l9;er7y;gy;en; hominDjAvan9;tage;ec8;ti9;ve;em;cCeAqui9;tt0;ta1;te;iAru0;al;de6;nt","Person|Noun":"true¦a0Eb07c03dWeUfQgOhLjHkiGlFmCnBolive,p7r4s3trini06v1wa0;ng,rd,tts;an,enus,iol0;a,et;ky,onPumm09;ay,e1o0uby;bin,d,se;ed,x;a2e1o0;l,tt04;aLnJ;dYge,tR;at,orm;a0eloW;t0x,ya;!s;a9eo,iH;ng,tP;a2e1o0;lGy;an,w3;de,smi4y;a0erb,iOolBuntR;ll,z0;el;ail,e0iLuy;ne;a1ern,i0lo;elds,nn;ith,n0;ny;a0dEmir,ula,ve;rl;a4e3i1j,ol0;ly;ck,x0;ie;an,ja;i0wn;sy;am,h0liff,rystal;a0in,ristian;mbers,ri0;ty;a4e3i2o,r0ud;an0ook;dy;ll;nedict,rg;k0nks;er;l0rt;fredo,ma","Actor|Verb":"true¦aCb8c5doctor,engineAfool,g3host,judge,m2nerd,p1recruit,scout,ushAvolunteAwi0;mp,tneA;arent,ilot;an,ime;eek,oof,r0uide;adu8oom;ha1o0;ach,nscript,ok;mpion,uffeur;o2u0;lly,tch0;er;ss;ddi1ffili0rchite1;ate;ct",MaleName:"true¦0:H6;1:FZ;2:DS;3:GQ;4:CZ;5:FV;6:GM;7:FP;8:GW;9:ET;A:C2;B:GD;aF8bE1cCQdBMeASfA1g8Yh88i7Uj6Sk6Bl5Mm48n3So3Ip33qu31r26s1Et0Ru0Ov0CwTxSyHzC;aCor0;cChC1karia,nAT;!hDkC;!aF6;!ar7CeF5;aJevgenBSoEuC;en,rFVsCu3FvEF;if,uf;nDs6OusC;ouf,s6N;aCg;s,tC;an,h0;hli,nCrosE1ss09;is,nC;!iBU;avi2ho5;aPeNiDoCyaEL;jcieBJlfgang,odrFutR;lFnC;f8TsC;lCt1;ow;bGey,frEhe4QlC;aE5iCy;am,e,s;ed8iC;d,ed;eAur;i,ndeD2rn2sC;!l9t1;lDyC;l1ne;lDtC;!er;aCHy;aKernDAiFladDoC;jteB0lodymyr;!iC;mFQsDB;cFha0ktBZnceDrgCOvC;a0ek;!nC;t,zo;!e4StBV;lCnC7sily;!entC;in9J;ghE2lCm70nax,ri,sm0;riCyss87;ch,k;aWeRhNiLoGrEuDyC;!l2roEDs1;n6r6E;avD0eCist0oy,um0;ntCRvBKy;bFdAWmCny;!asDmCoharu;aFFie,y;!z;iA6y;mCt4;!my,othy;adEeoDia0SomC;!as;!dor91;!de4;dFrC;enBKrC;anBJeCy;ll,nBI;!dy;dgh,ha,iCnn2req,tsu5V;cDAka;aYcotWeThPiMlobod0oKpenc2tEurDvenAEyCzym1;ed,lvest2;aj,e9V;anFeDuC;!aA;fan17phEQvCwaA;e77ie;!islaCl9;v,w;lom1rBuC;leymaDHta;dDgmu9UlCm1yabonga;as,v8B;!dhart8Yn9;aEeClo75;lCrm0;d1t1;h9Jne,qu1Jun,wn,yne;aDbastiEDk2Yl5Mpp,rgCth,ymoCU;e1Dio;m4n;!tC;!ie,y;eDPlFmEnCq67tosCMul;dCj2UtiA5;e01ro;!iATkeB6mC4u5;!ik,vato9K;aZeUheC8iRoGuDyC;an,ou;b99dDf4peAssC;!elEG;ol00y;an,bLc7MdJel,geIh0lHmGnEry,sDyC;!ce;ar7Ocoe,s;!aCnBU;ld,n;an,eo;a7Ef;l7Jr;e3Eg2n9olfo,riC;go;bBNeDH;cCl9;ar87c86h54kCo;!ey,ie,y;cFeA3gDid,ubByCza;an8Ln06;g85iC;naC6s;ep;ch8Kfa5hHin2je8HlGmFndEoHpha5sDul,wi36yC;an,mo8O;h9Im4;alDSol3O;iD0on;f,ph;ul;e9CinC;cy,t1;aOeLhilJiFrCyoG;aDeC;m,st1;ka85v2O;eDoC;tr;r8GtC;er,ro;!ipCl6H;!p6U;dCLrcy,tC;ar,e9JrC;!o7;b9Udra8So9UscAHtri62ulCv8I;!ie,o7;ctav6Ji2lImHndrBRrGsDtCum6wB;is,to;aDc6k6m0vCwaBE;al79;ma;i,vR;ar,er;aDeksandr,ivC;er,i2;f,v;aNeLguyBiFoCu3O;aDel,j4l0ma0rC;beAm0;h,m;cFels,g5i9EkDlC;es,s;!au,h96l78olaC;!i,y;hCkCol76;ol75;al,d,il,ls1vC;ilAF;hom,tC;e,hC;anCy;!a5i5;aYeViLoGuDyC;l4Nr1;hamDr84staC;fa,p6E;ed,mG;di10e,hamEis4JntDritz,sCussa;es,he;e,y;ad,ed,mC;ad,ed;cGgu5hai,kFlEnDtchC;!e8O;a9Pik;house,o7t1;ae73eC3ha8Iolaj;ah,hDkC;!ey,y;aDeC;al,l;el,l;hDlv3rC;le,ri8Ev4T;di,met;ay0c00gn4hWjd,ks2NlTmadZnSrKsXtDuric7VxC;imilBKwe8B;eHhEi69tCus,y69;!eo,hCia7;ew,i67;eDiC;as,eu,s;us,w;j,o;cHiGkFlEqu8Qsha83tCv3;iCy;!m,n;in,on;el,o7us;a6Yo7us;!elCin,o7us;!l8o;frAEi5Zny,u5;achDcoCik;lm;ai,y;amDdi,e5VmC;oud;adCm6W;ou;aulCi9P;ay;aWeOiMloyd,oJuDyC;le,nd1;cFdEiDkCth2uk;a7e;gi,s,z;ov7Cv6Hw6H;!as,iC;a6Een;g0nn52renDuCvA4we7D;!iS;!zo;am,n4oC;n5r;a9Yevi,la5KnHoFst2thaEvC;eCi;nte;bo;nCpo8V;!a82el,id;!nC;aAy;mEnd1rDsz73urenCwr6K;ce,t;ry,s;ar,beAont;aOeIhalHiFla4onr63rDu5SylC;e,s;istCzysztof;i0oph2;er0ngsl9p,rC;ilA9k,ollos;ed,id;en0iGnDrmCv4Z;it;!dDnCt1;e2Ny;ri4Z;r,th;cp2j4mEna8BrDsp6them,uC;ri;im,l;al,il;a03eXiVoFuC;an,lCst3;en,iC;an,en,o,us;aQeOhKkub4AnIrGsDzC;ef;eDhCi9Wue;!ua;!f,ph;dCge;i,on;!aCny;h,s,th6J;anDnC;!ath6Hie,n72;!nC;!es;!l,sCy;ph;o,qu3;an,mC;!i,m6V;d,ffFns,rCs4;a7JemDmai7QoCry;me,ni1H;i9Dy;!e73rC;ey,y;cKdBkImHrEsDvi2yC;dBs1;on,p2;ed,oDrCv67;e6Qod;d,s61;al,es5Wis1;a,e,oCub;b,v;ob,qu13;aTbNchiMgLke53lija,nuKonut,rIsEtCv0;ai,suC;ki;aDha0i8XmaCsac;el,il;ac,iaC;h,s;a,vinCw3;!g;k,nngu6X;nac1Xor;ka;ai,rahC;im;aReLoIuCyd6;beAgGmFsC;eyDsC;a3e3;in,n;ber5W;h,o;m2raDsse3wC;a5Pie;c49t1K;a0Qct3XiGnDrC;beAman08;dr7VrC;iCy2N;!k,q1R;n0Tt3S;bKlJmza,nIo,rEsDyC;a5KdB;an,s0;lEo67r2IuCv9;hi5Hki,tC;a,o;an,ey;k,s;!im;ib;a08e00iUlenToQrMuCyorgy;iHnFsC;!taC;f,vC;!e,o;n6tC;er,h2;do,lC;herDlC;auCerQ;me;aEegCov2;!g,orC;!io,y;dy,h7C;dfr9nza3XrDttfC;ri6C;an,d47;!n;acoGlEno,oCuseppe;rgiCvan6O;!o,s;be6Ies,lC;es;mo;oFrC;aDha4HrC;it,y;ld,rd8;ffErgC;!e7iCy;!os;!r9;bElBrCv3;eCla1Nr4Hth,y;th;e,rC;e3YielC;!i4;aXeSiQlOorrest,rCyod2E;aHedFiC;edDtC;s,z;ri18;!d42eri11riC;ck,k;nCs2;cEkC;ie,lC;in,yn;esLisC;!co,z3M;etch2oC;ri0yd;d5lConn;ip;deriFliEng,rC;dinaCg4nan0B;nd8;pe,x;co;bCdi,hd;iEriC;ce,zC;io;an,en,o;benez2dZfrYit0lTmMnJo3rFsteb0th0ugenEvCymBzra;an,eCge4D;ns,re3K;!e;gi,iDnCrol,v3w3;est8ie,st;cCk;!h,k;o0DriCzo;co,qC;ue;aHerGiDmC;aGe3A;lCrh0;!iC;a10o,s;s1y;nu5;beAd1iEliDm2t1viCwood;n,s;ot28s;!as,j5Hot,sC;ha;a3en;!dGg6mFoDua2QwC;a2Pin;arC;do;oZuZ;ie;a04eTiOmitrNoFrag0uEwDylC;an,l0;ay3Hig4D;a3Gdl9nc0st3;minFnDri0ugCvydGy2S;!lF;!a36nCov0;e1Eie,y;go,iDykC;as;cCk;!k;i,y;armuFetDll1mitri7neCon,rk;sh;er,m6riC;ch;id;andLepak,j0lbeAmetri4nIon,rGsEvDwCxt2;ay30ey;en,in;hawn,moC;nd;ek,riC;ck;is,nC;is,y;rt;re;an,le,mKnIrEvC;e,iC;!d;en,iEne0PrCyl;eCin,yl;l45n;n,o,us;!iCny;el,lo;iCon;an,en,on;a0Fe0Ch03iar0lRoJrFuDyrC;il,us;rtC;!is;aEistC;iaCob12;no;ig;dy,lInErC;ey,neliCy;s,us;nEor,rDstaC;nt3;ad;or;by,e,in,l3t1;aHeEiCyde;fCnt,ve;fo0Xt1;menDt4;us;s,t;rFuDyC;!t1;dCs;e,io;enC;ce;aHeGrisC;!toC;phCs;!eC;!r;st2t;d,rCs;b5leC;s,y;cDdrCs6;ic;il;lHmFrC;ey,lDroCy;ll;!o7t1;er1iC;lo;!eb,v3;a09eZiVjorn,laUoSrEuCyr1;ddy,rtKst2;er;aKeFiEuDyC;an,ce,on;ce,no;an,ce;nDtC;!t;dDtC;!on;an,on;dFnC;dDisC;lav;en,on;!foOl9y;bby,gd0rCyd;is;i0Lke;bElDshC;al;al,lL;ek;nIrCshoi;at,nEtC;!raC;m,nd;aDhaCie;rd;rd8;!iDjam3nCs1;ie,y;to;kaMlazs,nHrC;n9rDtC;!holomew;eCy;tt;ey;dCeD;ar,iC;le;ar1Nb1Dd16fon15gust3hm12i0Zja0Yl0Bm07nTputsiSrGsaFugustEveDyCziz;a0kh0;ry;o,us;hi;aMchiKiJjun,mHnEon,tCy0;em,hCie,ur8;ur;aDoC;!ld;ud,v;aCin;an,nd8;!el,ki;baCe;ld;ta;aq;aMdHgel8tCw6;hoFoC;iDnC;!i8y;ne;ny;er7rCy;eDzC;ej;!as,i,j,s,w;!s;s,tolC;iCy;!y;ar,iEmaCos;nu5r;el;ne,r,t;aVbSdBeJfHiGl01onFphonsEt1vC;aPin;on;e,o;so,zo;!sR;!onZrC;ed;c,jaHksFssaHxC;!andC;er,rC;e,os,u;andCei;ar,er,r;ndC;ro;en;eDrecC;ht;rt8;dd3in,n,sC;taC;ir;ni;dDm6;ar;an,en;ad,eC;d,t;in;so;aGi,olErDvC;ik;ian8;f8ph;!o;mCn;!a;dGeFraDuC;!bakr,lfazl;hCm;am;!l;allFel,oulaye,ulC;!lDrahm0;an;ah,o;ah;av,on",Uncountable:"true¦0:2E;1:2L;2:33;a2Ub2Lc29d22e1Rf1Ng1Eh16i11j0Yk0Wl0Rm0Hn0Do0Cp03rZsLt9uran2Jv7w3you gu0E;a5his17i4oo3;d,l;ldlife,ne;rm8t1;apor,ernacul29i3;neg28ol1Otae;eDhBiAo8r4un3yranny;a,gst1B;aff2Oea1Ko4ue nor3;th;o08u3;bleshoot2Ose1Tt;night,othpas1Vwn3;foEsfoE;me off,n;er3und1;e,mod2S;a,nnis;aDcCeBhAi9ki8o7p6t4u3weepstak0;g1Unshi2Hshi;ati08e3;am,el;ace2Keci0;ap,cc1meth2C;n,ttl0;lk;eep,ingl0or1C;lf,na1Gri0;ene1Kisso1C;d0Wfe2l4nd,t3;i0Iurn;m1Ut;abi0e4ic3;e,ke15;c3i01laxa11search;ogni10rea10;a9e8hys7luto,o5re3ut2;amble,mis0s3ten20;en1Zs0L;l3rk;i28l0EyH; 16i28;a24tr0F;nt3ti0M;i0s;bstetri24vercrowd1Qxyg09;a5e4owada3utella;ys;ptu1Ows;il poliZtional securi2;aAe8o5u3;m3s1H;ps;n3o1K;ey,o3;gamy;a3cha0Elancholy,rchandi1Htallurgy;sl0t;chine3g1Aj1Hrs,thema1Q; learn1Cry;aught1e6i5ogi4u3;ck,g12;c,s1M;ce,ghtn18nguis1LteratWv1;ath1isVss;ara0EindergartPn3;icke0Aowled0Y;e3upit1;a3llyfiGwel0G;ns;ce,gnor6mp5n3;forma00ter3;net,sta07;atiSort3rov;an18;a7e6isto09o3ung1;ckey,mework,ne4o3rseradi8spitali2use arrest;ky;s2y;adquarteXre;ir,libut,ppiHs3;hi3te;sh;ene8l6o5r3um,ymnas11;a3eZ;niUss;lf,re;ut3yce0F;en; 3ti0W;edit0Hpo3;ol;aNicFlour,o4urnit3;ure;od,rgive3uri1wl;ness;arCcono0LducaBlectr9n7quip8thi0Pvery6x3;ist4per3;ti0B;en0J;body,o08th07;joy3tertain3;ment;ici2o3;ni0H;tiS;nings,th;emi02i6o4raugh3ynas2;ts;pe,wnstai3;rs;abet0ce,s3;honZrepu3;te;aDelciChAivi07l8o3urrency;al,ld w6mmenta5n3ral,ttIuscoB;fusiHt 3;ed;ry;ar;assi01oth0;es;aos,e3;eMwK;us;d,rO;a8i6lood,owlHread5u3;ntGtt1;er;!th;lliarJs3;on;g3ss;ga3;ge;cKdviJeroGirFmBn6ppeal court,r4spi3thleL;rin;ithmet3sen3;ic;i6y3;o4th3;ing;ne;se;en5n3;es2;ty;ds;craft;bi8d3nau7;yna3;mi6;ce;id,ous3;ti3;cs",Infinitive:"true¦0:9G;1:9T;2:AD;3:90;4:9Z;5:84;6:AH;7:A9;8:92;9:A0;A:AG;B:AI;C:9V;D:8R;E:8O;F:97;G:6H;H:7D;a94b8Hc7Jd68e4Zf4Mg4Gh4Ai3Qj3Nk3Kl3Bm34nou48o2Vp2Equ2Dr1Es0CtZuTvRwI;aOeNiLors5rI;eJiI;ng,te;ak,st3;d5e8TthI;draw,er;a2d,ep;i2ke,nIrn;d1t;aIie;liADniAry;nJpI;ho8Llift;cov1dJear8Hfound8DlIplug,rav82tie,ve94;eaAo3X;erIo;cut,go,staAFvalA3w2G;aSeQhNoMrIu73;aIe72;ffi3Smp3nsI;aBfo7CpI;i8oD;pp3ugh5;aJiJrIwaD;eat5i2;nk;aImA0;ch,se;ck3ilor,keImp1r8L;! paD;a0Ic0He0Fh0Bi0Al08mugg3n07o05p02qu01tUuLwI;aJeeIim;p,t5;ll7Wy;bNccMffLggeCmmKppJrI;mouFpa6Zvi2;o0re6Y;ari0on;er,i4;e7Numb;li9KmJsiIveD;de,st;er9it;aMe8MiKrI;ang3eIi2;ng27w;fIng;f5le;b,gg1rI;t3ve;a4AiA;a4UeJit,l7DoI;il,of;ak,nd;lIot7Kw;icEve;atGeak,i0O;aIi6;m,y;ft,ng,t;aKi6CoJriIun;nk,v6Q;ot,rt5;ke,rp5tt1;eIll,nd,que8Gv1w;!k,m;aven9ul8W;dd5tis1Iy;a0FeKiJoI;am,t,ut;d,p5;a0Ab08c06d05f01group,hea00iZjoi4lXmWnVpTq3MsOtMup,vI;amp,eJiIo3B;sEve;l,rI;e,t;i8rI;ie2ofE;eLiKpo8PtIurfa4;o24rI;aHiBuctu8;de,gn,st;mb3nt;el,hra0lIreseF;a4e71;d1ew,o07;aHe3Fo2;a7eFiIo6Jy;e2nq41ve;mbur0nf38;r0t;inKleBocus,rJuI;el,rbiA;aBeA;an4e;aBu4;ei2k8Bla43oIyc3;gni39nci3up,v1;oot,uI;ff;ct,d,liIp;se,ze;tt3viA;aAenGit,o7;aWerUinpoiFlumm1LoTrLuI;b47ke,niArIt;poDsuI;aFe;eMoI;cKd,fe4XhibEmo7noJpo0sp1tru6vI;e,i6o5L;un4;la3Nu8;aGclu6dJf1occupy,sup0JvI;a6BeF;etermi4TiB;aGllu7rtr5Ksse4Q;cei2fo4NiAmea7plex,sIva6;eve8iCua6;mp1rItrol,ve;a6It6E;bOccuNmEpMutLverIwe;l07sJtu6Yu0wI;helm;ee,h1F;gr5Cnu2Cpa4;era7i4Ipo0;py,r;ey,seItaH;r2ss;aMe0ViJoIultiply;leCu6Pw;micJnIspla4;ce,g3us;!k;iIke,na9;m,ntaH;aPeLiIo0u3N;ke,ng1quIv5;eIi6S;fy;aKnIss5;d,gI;th5;rn,ve;ng2Gu1N;eep,idnJnI;e4Cow;ap;oHuI;gg3xtaI;po0;gno8mVnIrk;cTdRfQgeChPitia7ju8q1CsNtKun6EvI;a6eIo11;nt,rt,st;erJimi6BoxiPrI;odu4u6;aBn,pr03ru6C;iCpi8tIu8;all,il,ruB;abEibE;eCo3Eu0;iIul9;ca7;i7lu6;b5Xmer0pI;aLer4Uin9ly,oJrI;e3Ais6Bo2;rt,se,veI;riA;le,rt;aLeKiIoiCuD;de,jaInd1;ck;ar,iT;mp1ng,pp5raIve;ng5Mss;ath1et,iMle27oLrI;aJeIow;et;b,pp3ze;!ve5A;gg3ve;aTer45i5RlSorMrJuI;lf4Cndrai0r48;eJiIolic;ght5;e0Qsh5;b3XeLfeEgJsI;a3Dee;eIi2;!t;clo0go,shIwa4Z;ad3F;att1ee,i36;lt1st5;a0OdEl0Mm0FnXquip,rWsVtGvTxI;aRcPeDhOiNpJtIu6;ing0Yol;eKi8lIo0un9;aHoI;it,re;ct,di7l;st,t;a3oDu3B;e30lI;a10u6;lt,mi28;alua7oI;ke,l2;chew,pou0tab19;a0u4U;aYcVdTfSgQhan4joy,lPqOrNsuMtKvI;e0YisI;a9i50;er,i4rI;aHenGuC;e,re;iGol0F;ui8;ar9iC;a9eIra2ulf;nd1;or4;ang1oIu8;r0w;irc3lo0ou0ErJuI;mb1;oaGy4D;b3ct;bKer9pI;hasiIow1;ze;aKody,rI;a4oiI;d1l;lm,rk;ap0eBuI;ci40de;rIt;ma0Rn;a0Re04iKo,rIwind3;aw,ed9oI;wn;agno0e,ff1g,mi2Kne,sLvI;eIul9;rIst;ge,t;aWbVcQlod9mant3pNru3TsMtI;iIoDu37;lJngI;uiA;!l;ol2ua6;eJlIo0ro2;a4ea0;n0r0;a2Xe36lKoIu0S;uIv1;ra9;aIo0;im;a3Kur0;b3rm;af5b01cVduBep5fUliTmQnOpMrLsiCtaGvI;eIol2;lop;ch;a20i2;aDiBloIoD;re,y;oIy;te,un4;eJoI;liA;an;mEv1;a4i0Ao06raud,y;ei2iMla8oKrI;ee,yI;!pt;de,mIup3;missi34po0;de,ma7ph1;aJrief,uI;g,nk;rk;mp5rk5uF;a0Dea0h0Ai09l08oKrIurta1G;a2ea7ipp3uI;mb3;ales4e04habEinci6ll03m00nIrro6;cXdUfQju8no7qu1sLtKvI;eIin4;ne,r9y;aHin2Bribu7;er2iLoli2Epi8tJuI;lt,me;itu7raH;in;d1st;eKiJoIroFu0;rm;de,gu8rm;ss;eJoI;ne;mn,n0;eIlu6ur;al,i2;buCe,men4pI;eIi3ly;l,te;eBi6u6;r4xiC;ean0iT;rcumveFte;eJirp,oI;o0p;riAw;ncIre5t1ulk;el;a02eSi6lQoPrKuI;iXrIy;st,y;aLeaKiJoad5;en;ng;stfeLtX;ke;il,l11mba0WrrMth1;eIow;ed;!coQfrie1LgPhMliLqueaKstJtrIwild1;ay;ow;th;e2tt3;a2eJoI;ld;ad;!in,ui3;me;bysEckfi8ff3tI;he;b15c0Rd0Iff0Ggree,l0Cm09n03ppZrXsQttOuMvJwaE;it;eDoI;id;rt;gIto0X;meF;aIeCraB;ch,in;pi8sJtoI;niA;aKeIi04u8;mb3rt,ss;le;il;re;g0Hi0ou0rI;an9i2;eaKly,oiFrI;ai0o2;nt;r,se;aMi0GnJtI;icipa7;eJoIul;un4y;al;ly0;aJu0;se;lga08ze;iKlI;e9oIu6;t,w;gn;ix,oI;rd;a03jNmiKoJsoI;rb;pt,rn;niIt;st1;er;ouJuC;st;rn;cLhie2knowled9quiItiva7;es4re;ce;ge;eQliOoKrJusI;e,tom;ue;mIst;moJpI;any,liA;da7;ma7;te;pt;andPduBet,i6oKsI;coKol2;ve;liArt,uI;nd;sh;de;ct;on",Person:"true¦0:1Q;a29b1Zc1Md1Ee18f15g13h0Ri0Qj0Nk0Jl0Gm09n06o05p00rPsItCusain bolt,v9w4xzibit,y1;anni,oko on2uji,v1;an,es;en,o;a3ednesday adams,i2o1;lfram,o0Q;ll ferrell,z khalifa;lt disn1Qr1;hol,r0G;a2i1oltai06;n dies0Zrginia wo17;lentino rossi,n goG;a4h3i2ripp,u1yra banks;lZpac shakur;ger woods,mba07;eresa may,or;kashi,t1ylor;um,ya1B;a5carlett johanss0h4i3lobodan milosevic,no2ocr1Lpider1uperm0Fwami; m0Em0E;op dogg,w whi1H;egfried,nbad;akespeaTerlock holm1Sia labeouf;ddam hussa16nt1;a cla11ig9;aAe6i5o3u1za;mi,n dmc,paul,sh limbau1;gh;bin hood,d stew16nald1thko;in0Mo;han0Yngo starr,valdo;ese witherspo0i1mbrandt;ll2nh1;old;ey,y;chmaninoff,ffi,iJshid,y roma1H;a4e3i2la16o1uff daddy;cahont0Ie;lar,p19;le,rZ;lm17ris hilt0;leg,prah winfr0Sra;a2e1iles cra1Bostradam0J; yo,l5tt06wmQ;pole0s;a5e4i2o1ubar03;by,lie5net,rriss0N;randa ju1tt romn0M;ly;rl0GssiaB;cklemo1rkov,s0ta hari,ya angelou;re;ady gaga,e1ibera0Pu;bron jam0Xch wale1e;sa;anye west,e3i1obe bryant;d cudi,efer suther1;la0P;ats,sha;a2effers0fk,k rowling,rr tolki1;en;ck the ripp0Mwaharlal nehru,y z;liTnez,ron m7;a7e5i3u1;lk hog5mphrey1sa01;! bog05;l1tl0H;de; m1dwig,nry 4;an;ile selassFlle ber4m3rrison1;! 1;ford;id,mo09;ry;ast0iannis,o1;odwPtye;ergus0lorence nightinga08r1;an1ederic chopN;s,z;ff5m2nya,ustaXzeki1;el;eril lagasse,i1;le zatop1nem;ek;ie;a6e4i2octor w1rake;ho;ck w1ego maradoC;olf;g1mi lovaOnzel washingt0;as;l1nHrth vadR;ai lNt0;a8h5lint0o1thulhu;n1olio;an,fuci1;us;on;aucKop2ristian baMy1;na;in;millo,ptain beefhe4r1;dinal wols2son1;! palmF;ey;art;a8e5hatt,i3oHro1;ck,n1;te;ll g1ng crosby;atB;ck,nazir bhut2rtil,yon1;ce;to;nksy,rack ob1;ama;l 6r3shton kutch2vril lavig8yn ra1;nd;er;chimed2istot1;le;es;capo2paci1;no;ne",Adjective:"true¦0:AI;1:BS;2:BI;3:BA;4:A8;5:84;6:AV;7:AN;8:AF;9:7H;A:BQ;B:AY;C:BC;D:BH;E:9Y;aA2b9Ec8Fd7We79f6Ng6Eh61i4Xj4Wk4Tl4Im41n3Po36p2Oquart7Pr2Ds1Dt14uSvOwFye29;aMeKhIiHoF;man5oFrth7G;dADzy;despreB1n w97s86;acked1UoleF;!sa6;ather1PeFll o70ste1D;!k5;nt1Ist6Ate4;aHeGiFola5T;bBUce versa,gi3Lle;ng67rsa5R;ca1gBSluAV;lt0PnLpHrGsFttermoBL;ef9Ku3;b96ge1; Hb32pGsFtiAH;ca6ide d4R;er,i85;f52to da2;a0Fbeco0Hc0Bd04e02f01gu1XheaBGiXkn4OmUnTopp06pRrNsJtHus0wF;aFiel3K;nt0rra0P;app0eXoF;ld,uS;eHi37o5ApGuF;perv06spec39;e1ok9O;en,ttl0;eFu5;cogn06gul2RlGqu84sF;erv0olv0;at0en33;aFrecede0E;id,rallel0;am0otic0;aFet;rri0tF;ch0;nFq26vers3;sur0terFv7U;eFrupt0;st0;air,inish0orese98;mploy0n7Ov97xpF;ect0lain0;eHisFocume01ue;clFput0;os0;cid0rF;!a8Scov9ha8Jlyi8nea8Gprivileg0sMwF;aFei9I;t9y;hGircumcFonvin2U;is0;aFeck0;lleng0rt0;b20ppea85ssuGttend0uthorF;iz0;mi8;i4Ara;aLeIhoHip 25oGrF;anspare1encha1i2;geth9leADp notch,rpB;rny,ugh6H;ena8DmpGrFs6U;r49tia4;eCo8P;leFst4M;nt0;a0Dc09e07h06i04ki03l01mug,nobbi4XoVpRqueami4XtKuFymb94;bHccinAi generis,pFr5;erFre7N;! dup9b,vi70;du0li7Lp6IsFurb7J;eq9Atanda9X;aKeJi16o2QrGubboFy4Q;rn;aightFin5GungS; fFfF;or7V;adfa9Pri6;lwa6Ftu82;arHeGir6NlendBot Fry;on;c3Qe1S;k5se; call0lImb9phistic16rHuFviV;ndFth1B;proof;dBry;dFub6; o2A;e60ipF;pe4shod;ll0n d7R;g2HnF;ceEg6ist9;am3Se9;co1Zem5lfFn6Are7; suf4Xi43;aGholFient3A;ar5;rlFt4A;et;cr0me,tisfac7F;aOeIheumatoBiGoF;bu8Ztt7Gy3;ghtFv3; 1Sf6X;cJdu8PlInown0pro69sGtF;ard0;is47oF;lu2na1;e1Suc45;alcit8Xe1ondi2;bBci3mpa1;aSePicayu7laOoNrGuF;bl7Tnjabi;eKiIoF;b7VfGmi49pFxi2M;er,ort81;a7uD;maFor,sti7va2;!ry;ciDexis0Ima2CpaB;in55puli8G;cBid;ac2Ynt 3IrFti2;ma40tFv7W;!i3Z;i2YrFss7R;anoBtF; 5XiF;al,s5V;bSffQkPld OnMrLth9utKverF;!aIbMdHhGni75seas,t,wF;ei74rou74;a63e7A;ue;ll;do1Ger,si6A;d3Qg2Aotu5Z; bFbFe on o7g3Uli7;oa80;fashion0school;!ay; gua7XbFha5Uli7;eat;eHligGsF;ce7er0So1C;at0;diFse;a1e1;aOeNiMoGuF;anc0de; moEnHrthFt6V;!eFwe7L;a7Krn;chaGdescri7Iprof30sF;top;la1;ght5;arby,cessa4ighbor5wlyw0xt;k0usiaFv3;ti8;aQeNiLoHuF;dIltiF;facet0p6;deHlGnFot,rbBst;ochro4Xth5;dy;rn,st;ddle ag0nF;dbloZi,or;ag9diocEga,naGrFtropolit4Q;e,ry;ci8;cIgenta,inHj0Fkeshift,mmGnFri4Oscu61ver18;da5Dy;ali4Lo4U;!stream;abEho;aOeLiIoFumberi8;ngFuti1R;stan3RtF;erm,i4H;ghtGteraF;l,ry,te;heart0wei5O;ft JgFss9th3;al,eFi0M;nda4;nguBps0te5;apGind5noF;wi8;ut;ad0itte4uniW;ce co0Hgno6Mll0Cm04nHpso 2UrF;a2releF;va1; ZaYcoWdReQfOgrNhibi4Ri05nMoLsHtFvalu5M;aAeF;nDrdepe2K;a7iGolFuboI;ub6ve1;de,gF;nifica1;rdi5N;a2er;own;eriIiLluenVrF;ar0eq5H;pt,rt;eHiGoFul1O;or;e,reA;fiFpe26termi5E;ni2;mpFnsideCrreA;le2;ccuCdeq5Ene,ppr4J;fFsitu,vitro;ro1;mJpF;arHeGl15oFrop9;li2r11;n2LrfeA;ti3;aGeFi18;d4BnD;tuE;egGiF;c0YteC;al,iF;tiF;ma2;ld;aOelNiLoFuma7;a4meInHrrGsFur5;ti6;if4E;e58o3U; ma3GsF;ick;ghfalut2HspF;an49;li00pf33;i4llow0ndGrdFtM; 05coEworki8;sy,y;aLener44iga3Blob3oKrGuF;il1Nng ho;aFea1Fizzl0;cGtF;ef2Vis;ef2U;ld3Aod;iFuc2D;nf2R;aVeSiQlOoJrF;aGeFil5ug3;q43tf2O;gFnt3S;i6ra1;lk13oHrF; keeps,eFge0Vm9tu41;g0Ei2Ds3R;liF;sh;ag4Mowe4uF;e1or45;e4nF;al,i2;d Gmini7rF;ti6ve1;up;bl0lDmIr Fst pac0ux;oGreacF;hi8;ff;ed,ili0R;aXfVlTmQnOqu3rMthere3veryday,xF;aApIquisi2traHuF;be48lF;ta1;!va2L;edRlF;icF;it;eAstF;whi6; Famor0ough,tiE;rou2sui2;erGiF;ne1;ge1;dFe2Aoq34;er5;ficF;ie1;g9sF;t,ygF;oi8;er;aWeMiHoGrFue;ea4owY;ci6mina1ne,r31ti8ubQ;dact2Jfficult,m,sGverF;ge1se;creGePjoi1paCtF;a1inA;et,te; Nadp0WceMfiLgeneCliJmuEpeIreliAsGvoF;id,ut;pFtitu2ul1L;eCoF;nde1;ca2ghF;tf13;a1ni2;as0;facto;i5ngero0I;ar0Ce09h07i06l05oOrIuF;rmudgeon5stoma4teF;sy;ly;aIeHu1EystalF; cleFli7;ar;epy;fFv17z0;ty;erUgTloSmPnGrpoCunterclVveFy;rt;cLdJgr21jIsHtrF;aFi2;dic0Yry;eq1Yta1;oi1ug3;escenFuN;di8;a1QeFiD;it0;atoDmensuCpF;ass1SulF;so4;ni3ss3;e1niza1;ci1J;ockwiD;rcumspeAvil;eFintzy;e4wy;leGrtaF;in;ba2;diac,ef00;a00ePiLliJoGrFuck nak0;and new,isk,on22;gGldface,naF; fi05fi05;us;nd,tF;he;gGpartisFzarE;an;tiF;me;autifOhiNlLnHsFyoN;iWtselF;li8;eGiFt;gn;aFfi03;th;at0oF;v0w;nd;ul;ckwards,rF;e,rT; priori,b13c0Zd0Tf0Ng0Ihe0Hl09mp6nt06pZrTsQttracti0MuLvIwF;aGkF;wa1B;ke,re;ant garGeraF;ge;de;diIsteEtF;heFoimmu7;nt07;re;to4;hGlFtu2;eep;en;bitIchiv3roHtF;ifiFsy;ci3;ga1;ra4;ry;pFt;aHetizi8rF;oprF;ia2;llFre1;ed,i8;ng;iquFsy;at0e;ed;cohKiJkaHl,oGriFterX;ght;ne,of;li7;ne;ke,ve;olF;ic;ad;ain07gressiIi6rF;eeF;ab6;le;ve;fGraB;id;ectGlF;ue1;ioF;na2; JaIeGvF;erD;pt,qF;ua2;ma1;hoc,infinitum;cuCquiGtu3u2;al;esce1;ra2;erSjeAlPoNrKsGuF;nda1;e1olu2trF;aAuD;se;te;eaGuF;pt;st;aFve;rd;aFe;ze;ct;ra1;nt",Pronoun:"true¦elle,h3i2me,she,th0us,we,you;e0ou;e,m,y;!l,t;e,im",Preposition:"true¦aPbMcLdKexcept,fIinGmid,notwithstandiWoDpXqua,sCt7u4v2w0;/o,hereSith0;! whHin,oW;ersus,i0;a,s a vis;n1p0;!on;like,til;h1ill,oward0;!s;an,ereby,r0;ough0u;!oM;ans,ince,o that,uch G;f1n0ut;!to;!f;! 0to;effect,part;or,r0;om;espite,own,u3;hez,irca;ar1e0oBy;sides,tween;ri7;bo8cross,ft7lo6m4propos,round,s1t0;!op;! 0;a whole,long 0;as;id0ong0;!st;ng;er;ut",SportsTeam:"true¦0:18;1:1E;2:1D;3:14;a1Db15c0Sd0Kfc dallas,g0Ihouston 0Hindiana0Gjacksonville jagua0k0El0Am01new UoRpKqueens parkJreal salt lake,sBt6utah jazz,vancouver whitecaps,w4yW;ashington 4h10;natio1Mredski2wizar0W;ampa bay 7e6o4;ronto 4ttenham hotspur;blue ja0Mrapto0;nnessee tita2xasD;buccanee0ra0K;a8eattle 6porting kansas0Wt4; louis 4oke0V;c1Drams;marine0s4;eah13ounH;cramento Rn 4;antonio spu0diego 4francisco gJjose earthquak1;char08paB; ran07;a9h6ittsburgh 5ortland t4;imbe0rail blaze0;pirat1steele0;il4oenix su2;adelphia 4li1;eagl1philNunE;dr1;akland 4klahoma city thunder,rlando magic;athle0Lrai4;de0;england 8orleans 7york 4;g5je3knYme3red bul0Xy4;anke1;ian3;pelica2sain3;patrio3revolut4;ion;anchEeAi4ontreal impact;ami 8lwaukee b7nnesota 4;t5vi4;kings;imberwolv1wi2;rewe0uc0J;dolphi2heat,marli2;mphis grizz4ts;li1;a6eic5os angeles 4;clippe0dodFlaB;esterV; galaxy,ke0;ansas city 4nF;chiefs,roya0D; pace0polis col3;astr05dynamo,rocke3texa2;olden state warrio0reen bay pac4;ke0;allas 8e4i04od6;nver 6troit 4;lio2pisto2ti4;ge0;broncYnugge3;cowbo5maver4;icZ;ys;arEelLhAincinnati 8leveland 6ol4;orado r4umbus crew sc;api7ocki1;brow2cavalie0guar4in4;dia2;bengaVre4;ds;arlotte horAicago 4;b5cubs,fire,wh4;iteB;ea0ulQ;diff4olina panthe0; city;altimore Alackburn rove0oston 6rooklyn 4uffalo bilN;ne3;ts;cel5red4; sox;tics;rs;oriol1rave2;rizona Ast8tlanta 4;brav1falco2h4;awA;ns;es;on villa,r4;os;c6di4;amondbac4;ks;ardi4;na4;ls",Unit:"true¦a07b04cXdWexVfTgRhePinYjoule0BkMlJmDnan08oCp9quart0Bsq ft,t7volts,w6y2ze3°1µ0;g,s;c,f,n;dVear1o0;ttR; 0s 0;old;att,b;erNon0;!ne02;ascals,e1i0;cXnt00;rcent,tJ;hms,unceY;/s,e4i0m²,²,³;/h,cro2l0;e0liK;!²;grLsR;gCtJ;it1u0;menQx;erPreP;b5elvins,ilo1m0notO;/h,ph,²;!byGgrEmCs;ct0rtzL;aJogrC;allonJb0ig3rB;ps;a0emtEl oz,t4;hrenheit,radG;aby9;eci3m1;aratDe1m0oulombD;²,³;lsius,nti0;gr2lit1m0;et0;er8;am7;b1y0;te5;l,ps;c2tt0;os0;econd1;re0;!s","Noun|Gerund":"true¦0:3O;1:3M;2:3N;3:3D;4:32;5:2V;6:3E;7:3K;8:36;9:3J;A:3B;a3Pb37c2Jd27e23f1Vg1Sh1Mi1Ij1Gk1Dl18m13n11o0Wp0Pques0Sr0EsTtNunderMvKwFyDzB;eroi0oB;ni0o3P;aw2eB;ar2l3;aEed4hispe5i5oCrB;ap8est3i1;n0ErB;ki0r31;i1r2s9tc9;isualizi0oB;lunt1Vti0;stan4ta6;aFeDhin6iCraBy8;c6di0i2vel1M;mi0p8;aBs1;c9si0;l6n2s1;aUcReQhOiMkatKl2Wmo6nowJpeItFuCwB;ea5im37;b35f0FrB;fi0vB;e2Mi2J;aAoryt1KrCuB;d2KfS;etc9ugg3;l3n4;bCi0;ebBi0;oar4;gnBnAt1;a3i0;ip8oB;p8rte2u1;a1r27t1;hCo5reBulp1;a2Qe2;edu3oo3;i3yi0;aKeEi4oCuB;li0n2;oBwi0;fi0;aFcEhear7laxi0nDpor1sB;pon4tructB;r2Iu5;de5;or4yc3;di0so2;p8ti0;aFeacek20laEoCrBublis9;a1Teten4in1oces7;iso2siB;tio2;n2yi0;ckaAin1rB;ki0t1O;fEpeDrganiCvB;erco24ula1;si0zi0;ni0ra1;fe5;avi0QeBur7;gotia1twor6;aDeCi2oB;de3nito5;a2dita1e1ssaA;int0XnBrke1;ifUufactu5;aEeaDiBodAyi0;cen7f1mi1stB;e2i0;r2si0;n4ug9;iCnB;ea4it1;c6l3;ogAuB;dAgg3stif12;ci0llust0VmDnBro2;nova1sp0NterBven1;ac1vie02;agi2plo4;aDea1iCoBun1;l4w3;ki0ri0;nd3rB;roWvB;es1;aCene0Lli4rBui4;ee1ie0N;rde2the5;aHeGiDlCorBros1un4;e0Pmat1;ir1oo4;gh1lCnBs9;anZdi0;i0li0;e3nX;r0Zscina1;a1du01nCxB;erci7plo5;chan1di0ginB;ee5;aLeHiGoub1rCum8wB;el3;aDeCiB;bb3n6vi0;a0Qs7;wi0;rTscoDvi0;ba1coZlBvelo8;eCiB;ve5;ga1;nGti0;aVelebUhSlPoDrBur3yc3;aBos7yi0;f1w3;aLdi0lJmFnBo6pi0ve5;dDsCvinB;ci0;trBul1;uc1;muniDpB;lBo7;ai2;ca1;lBo5;ec1;c9ti0;ap8eaCimToBubT;ni0t9;ni0ri0;aBee5;n1t1;ra1;m8rCs1te5;ri0;vi0;aPeNitMlLoGrDuB;dge1il4llBr8;yi0;an4eat9oadB;cas1;di0;a1mEokB;i0kB;ee8;pi0;bi0;es7oa1;c9i0;gin2lonAt1;gi0;bysit1c6ki0tt3;li0;ki0;bando2cGdverti7gi0pproac9rgDssuCtB;trac1;mi0;ui0;hi0;si0;coun1ti0;ti0;ni0;ng",PhrasalVerb:"true¦0:92;1:96;2:8H;3:8V;4:8A;5:83;6:85;7:98;8:90;9:8G;A:8X;B:8R;C:8U;D:8S;E:70;F:97;G:8Y;H:81;I:7H;J:79;a9Fb7Uc6Rd6Le6Jf5Ig50h4Biron0j47k40l3Em31n2Yo2Wp2Cquiet Hr1Xs0KtZuXvacuu6QwNyammerBzK;ero Dip LonK;e0k0;by,ov9up;aQeMhLiKor0Mrit19;mp0n3Fpe0r5s5;ackAeel Di0S;aLiKn33;gh 3Wrd0;n Dr K;do1in,oJ;it 79k5lk Lrm 69sh Kt83v60;aw3do1o7up;aw3in,oC;rgeBsK;e 2herE;a00eYhViRoQrMuKypP;ckErn K;do1in,oJup;aLiKot0y 30;ckl7Zp F;ck HdK;e 5Y;n7Wp 3Es5K;ck MdLe Kghten 6me0p o0Rre0;aw3ba4do1in,up;e Iy 2;by,oG;ink Lrow K;aw3ba4in,up;ba4ov9up;aKe 77ll62;m 2r 5M;ckBke Llk K;ov9shit,u47;aKba4do1in,leave,o4Dup;ba4ft9pa69w3;a0Vc0Te0Mh0Ii0Fl09m08n07o06p01quar5GtQuOwK;earMiK;ngLtch K;aw3ba4o8K; by;cKi6Bm 2ss0;k 64;aReQiPoNrKud35;aigh2Det75iK;ke 7Sng K;al6Yup;p Krm2F;by,in,oG;c3Ln3Lr 2tc4O;p F;c3Jmp0nd LrKveAy 2O;e Ht 2L;ba4do1up;ar3GeNiMlLrKurB;ead0ingBuc5;a49it 6H;c5ll o3Cn 2;ak Fe1Xll0;a3Bber 2rt0und like;ap 5Vow Duggl5;ash 6Noke0;eep NiKow 6;cLp K;o6Dup;e 68;in,oK;ff,v9;de19gn 4NnKt 6Gz5;gKkE; al6Ale0;aMoKu5W;ot Kut0w 7M;aw3ba4f48oC;c2WdeEk6EveA;e Pll1Nnd Orv5tK; Ktl5J;do1foLin,o7upK;!on;ot,r5Z;aw3ba4do1in,o33up;oCto;al66out0rK;ap65ew 6J;ilAv5;aXeUiSoOuK;b 5Yle0n Kstl5;aLba4do1inKo2Ith4Nu5P;!to;c2Xr8w3;ll Mot LpeAuK;g3Ind17;a2Wf3Po7;ar8in,o7up;ng 68p oKs5;ff,p18;aKelAinEnt0;c6Hd K;o4Dup;c27t0;aZeYiWlToQrOsyc35uK;ll Mn5Kt K;aKba4do1in,oJto47up;pa4Dw3;a3Jdo1in,o21to45up;attleBess KiNop 2;ah2Fon;iLp Kr4Zu1Gwer 6N;do1in,o6Nup;nt0;aLuK;gEmp 6;ce u20y 6D;ck Kg0le 4An 6p5B;oJup;el 5NncilE;c53ir 39n0ss MtLy K;ba4oG; Hc2R;aw3ba4in,oJ;pKw4Y;e4Xt D;aLerd0oK;dAt53;il Hrrow H;aTeQiPoLuK;ddl5ll I;c1FnkeyMp 6uthAve K;aKdo1in,o4Lup;l4Nw3; wi4K;ss0x 2;asur5e3SlLss K;a21up;t 6;ke Ln 6rKs2Ax0;k 6ryA;do,fun,oCsure,up;a02eViQoLuK;ck0st I;aNc4Fg MoKse0;k Kse4D;aft9ba4do1forw37in56o0Zu46;in,oJ;d 6;e NghtMnLsKve 00;ten F;e 2k 2; 2e46;ar8do1in;aMt LvelK; oC;do1go,in,o7up;nEve K;in,oK;pKut;en;c5p 2sh LtchBughAy K;do1o59;in4Po7;eMick Lnock K;do1oCup;oCup;eLy K;in,up;l Ip K;aw3ba4do1f04in,oJto,up;aMoLuK;ic5mpE;ke3St H;c43zz 2;a01eWiToPuK;nLrrKsh 6;y 2;keLt K;ar8do1;r H;lKneErse3K;d Ke 2;ba4dKfast,o0Cup;ear,o1;de Lt K;ba4on,up;aw3o7;aKlp0;d Ml Ir Kt 2;fKof;rom;f11in,o03uW;cPm 2nLsh0ve Kz2P;at,it,to;d Lg KkerP;do1in,o2Tup;do1in,oK;ut,v9;k 2;aZeTive Rloss IoMrLunK; f0S;ab hold,in43ow 2U; Kof 2I;aMb1Mit,oLr8th1IuK;nd9;ff,n,v9;bo7ft9hQw3;aw3bKdo1in,oJrise,up,w3;a4ir2H;ar 6ek0t K;aLb1Fdo1in,oKr8up;ff,n,ut,v9;cLhKl2Fr8t,w3;ead;ross;d aKng 2;bo7;a0Ee07iYlUoQrMuK;ck Ke2N;ar8up;eLighten KownBy 2;aw3oG;eKshe27; 2z5;g 2lMol Krk I;aKwi20;bo7r8;d 6low 2;aLeKip0;sh0;g 6ke0mKrKtten H;e F;gRlPnNrLsKzzle0;h F;e Km 2;aw3ba4up;d0isK;h 2;e Kl 1T;aw3fPin,o7;ht ba4ure0;ePnLsK;s 2;cMd K;fKoG;or;e D;d04l 2;cNll Krm0t1G;aLbKdo1in,o09sho0Eth08victim;a4ehi2O;pa0C;e K;do1oGup;at Kdge0nd 12y5;in,o7up;aOi1HoNrK;aLess 6op KuN;aw3b03in,oC;gBwB; Ile0ubl1B;m 2;a0Ah05l02oOrLut K;aw3ba4do1oCup;ackBeep LoKy0;ss Dwd0;by,do1in,o0Uup;me NoLuntK; o2A;k 6l K;do1oG;aRbQforOin,oNtKu0O;hLoKrue;geth9;rough;ff,ut,v9;th,wK;ard;a4y;paKr8w3;rt;eaLose K;in,oCup;n 6r F;aNeLiK;ll0pE;ck Der Kw F;on,up;t 2;lRncel0rOsMtch LveE; in;o1Nup;h Dt K;doubt,oG;ry LvK;e 08;aw3oJ;l Km H;aLba4do1oJup;ff,n,ut;r8w3;a0Ve0MiteAl0Fo04rQuK;bblNckl05il0Dlk 6ndl05rLsKtMy FzzA;t 00;n 0HsK;t D;e I;ov9;anWeaUiLush K;oGup;ghQng K;aNba4do1forMin,oLuK;nd9p;n,ut;th;bo7lKr8w3;ong;teK;n 2;k K;do1in,o7up;ch0;arTg 6iRn5oPrNssMttlLunce Kx D;aw3ba4;e 6; ar8;e H;do1;k Dt 2;e 2;l 6;do1up;d 2;aPeed0oKurt0;cMw K;aw3ba4do1o7up;ck;k K;in,oC;ck0nk0stA; oQaNef 2lt0nd K;do1ov9up;er;up;r Lt K;do1in,oCup;do1o7;ff,nK;to;ck Pil0nMrgLsK;h D;ainBe D;g DkB; on;in,o7;aw3do1in,oCup;ff,ut;ay;ct FdQir0sk MuctionA; oG;ff;ar8o7;ouK;nd; o7;d K;do1oKup;ff,n;wn;o7up;ut",ProperNoun:"true¦aIbDc8dalhousHe7f5gosford,h4iron maiden,kirby,landsdowne,m2nis,r1s0wembF;herwood,paldiB;iel,othwe1;cgi0ercedes,issy;ll;intBudsB;airview,lorence,ra0;mpt9nco;lmo,uro;a1h0;arlt6es5risti;rl0talina;et4i0;ng;arb3e0;et1nt0rke0;ley;on;ie;bid,jax","Person|Place":"true¦a8d6h4jordan,k3orlando,s1vi0;ctor9rgin9;a0ydney;lvador,mara,ntia4;ent,obe;amil0ous0;ton;arw2ie0;go;lexandr1ust0;in;ia",LastName:"true¦0:BR;1:BF;2:B5;3:BH;4:AX;5:9Y;6:B6;7:BK;8:B0;9:AV;A:AL;B:8Q;C:8G;D:7K;E:BM;F:AH;aBDb9Zc8Wd88e81f7Kg6Wh64i60j5Lk4Vl4Dm39n2Wo2Op25quispe,r1Ls0Pt0Ev03wTxSyKzG;aIhGimmerm6A;aGou,u;ng,o;khar5ytsE;aKeun9BiHoGun;koya32shiBU;!lG;diGmaz;rim,z;maGng;da,g52mo83sGzaC;aChiBV;iao,u;aLeJiHoGright,u;jcA5lff,ng;lGmm0nkl0sniewsC;kiB1liams33s3;bGiss,lt0;b,er,st0;a6Vgn0lHtG;anabe,s3;k0sh,tG;e2Non;aLeKiHoGukD;gt,lk5roby5;dHllalGnogr3Kr1Css0val3S;ba,ob1W;al,ov4;lasHsel8W;lJn dIrgBEsHzG;qu7;ilyEqu7siljE;en b6Aijk,yk;enzueAIverde;aPeix1VhKi2j8ka43oJrIsui,uG;om5UrG;c2n0un1;an,emblA7ynisC;dorAMlst3Km4rrAth;atch0i8UoG;mHrG;are84laci79;ps3sG;en,on;hirDkah9Mnaka,te,varA;a06ch01eYhUiRmOoMtIuHvGzabo;en9Jobod3N;ar7bot4lliv2zuC;aIeHoG;i7Bj4AyanAB;ele,in2FpheBvens25;l8rm0;kol5lovy5re7Tsa,to,uG;ng,sa;iGy72;rn5tG;!h;l71mHnGrbu;at9cla9Egh;moBo7M;aIeGimizu;hu,vchG;en8Luk;la,r1G;gu9infe5YmGoh,pulveA7rra5P;jGyG;on5;evi6iltz,miHneid0roed0uGwarz;be3Elz;dHtG;!t,z;!t;ar4Th8ito,ka4OlJnGr4saCto,unde19v4;ch7dHtGz;a5Le,os;b53e16;as,ihDm4Po0Y;aVeSiPoJuHyG;a6oo,u;bio,iz,sG;so,u;bKc8Fdrigue67ge10j9YmJosevelt,sItHux,wG;e,li6;a9Ch;enb4Usi;a54e4L;erts15i93;bei4JcHes,vGzzo;as,e9;ci,hards12;ag2es,iHut0yG;es,nol5N;s,t0;dImHnGsmu97v6C;tan1;ir7os;ic,u;aUeOhMiJoHrGut8;asad,if6Zochazk27;lishc2GpGrti72u10we76;e3Aov51;cHe45nG;as,to;as70hl0;aGillips;k,m,n6I;a3Hde3Wete0Bna,rJtG;ersHrovGters54;!a,ic;!en,on;eGic,kiBss3;i9ra,tz,z;h86k,padopoulIrk0tHvG;ic,l4N;el,te39;os;bMconn2Ag2TlJnei6PrHsbor6XweBzG;dem7Rturk;ella4DtGwe6N;ega,iz;iGof7Hs8I;vGyn1R;ei9;aSri1;aPeNiJoGune50ym2;rHvGwak;ak4Qik5otn66;odahl,r4S;cholsZeHkolGls4Jx3;ic,ov84;ls1miG;!n1;ils3mG;co4Xec;gy,kaGray2sh,var38;jiGmu9shiG;ma;a07c04eZiWoMuHyeG;rs;lJnIrGssoli6S;atGp03r7C;i,ov4;oz,te58;d0l0;h2lOnNo0RrHsGza1A;er,s;aKeJiIoz5risHtG;e56on;!on;!n7K;au,i9no,t5J;!lA;r1Btgome59;i3El0;cracFhhail5kkeHlG;l0os64;ls1;hmeJiIj30lHn3Krci0ssiGyer2N;!er;n0Po;er,j0;dDti;cartHlG;aughl8e2;hy;dQe7Egnu68i0jer3TkPmNnMrItHyG;er,r;ei,ic,su21thews;iHkDquAroqu8tinG;ez,s;a5Xc,nG;!o;ci5Vn;a5UmG;ad5;ar5e6Kin1;rig77s1;aVeOiLoJuHyG;!nch;k4nGo;d,gu;mbarGpe3Fvr4we;di;!nGu,yana2B;coln,dG;b21holm,strom;bedEfeKhIitn0kaHn8rGw35;oy;!j;m11tG;in1on1;bvGvG;re;iGmmy,ng,rs2Qu,voie,ws3;ne,t1F;aZeYh2iWlUnez50oNrJuHvar2woG;k,n;cerGmar68znets5;a,o34;aHem0isGyeziu;h23t3O;m0sni4Fus3KvG;ch4O;bay57ch,rh0Usk16vaIwalGzl5;czGsC;yk;cIlG;!cGen4K;huk;!ev4ic,s;e8uiveG;rt;eff0kGl4mu9nnun1;ucF;ll0nnedy;hn,llKminsCne,pIrHstra3Qto,ur,yGzl5;a,s0;j0Rls22;l2oG;or;oe;aPenOha6im14oHuG;ng,r4;e32hInHrge32u6vG;anD;es,ss3;anHnsG;en,on,t3;nesGs1R;en,s1;kiBnings,s1;cJkob4EnGrv0E;kDsG;en,sG;en0Ion;ks3obs2A;brahimDglesi5Nke5Fl0Qno07oneIshikHto,vanoG;u,v54;awa;scu;aVeOiNjaltal8oIrist50uG;!aGb0ghAynh;m2ng;a6dz4fIjgaa3Hk,lHpUrGwe,x3X;ak1Gvat;mAt;er,fm3WmG;ann;ggiBtchcock;iJmingw4BnHrGss;nand7re9;deGriks1;rs3;kkiHnG;on1;la,n1;dz4g1lvoQmOns0ZqNrMsJuIwHyG;asFes;kiB;g1ng;anHhiG;mo14;i,ov0J;di6p0r10t;ue;alaG;in1;rs1;aVeorgUheorghe,iSjonRoLrJuGw3;errGnnar3Co,staf3Ctierr7zm2;a,eG;ro;ayli6ee2Lg4iffithGub0;!s;lIme0UnHodGrbachE;e,m2;calvAzale0S;dGubE;bGs0E;erg;aj,i;bs3l,mGordaO;en7;iev3U;gnMlJmaIndFo,rGsFuthi0;cGdn0za;ia;ge;eaHlG;agh0i,o;no;e,on;aVerQiLjeldsted,lKoIrHuG;chs,entAji41ll0;eem2iedm2;ntaGrt8urni0wl0;na;emi6orA;lipIsHtzgeraG;ld;ch0h0;ovG;!ic;hatDnanIrG;arGei9;a,i;deY;ov4;b0rre1D;dKinsJriksIsGvaB;cob3GpGtra3D;inoza,osiQ;en,s3;te8;er,is3warG;ds;aXePiNjurhuMoKrisco15uHvorakG;!oT;arte,boHmitru,nn,rGt3C;and,ic;is;g2he0Omingu7nErd1ItG;to;us;aGcki2Hmitr2Ossanayake,x3;s,z; JbnaIlHmirGrvisFvi,w2;!ov4;gado,ic;th;bo0groot,jo6lHsilGvriA;va;a cruz,e3uG;ca;hl,mcevsCnIt2WviG;dGes,s;ov,s3;ielsGku22;!en;ki;a0Be06hRiobQlarkPoIrGunningh1H;awfo0RivGuz;elli;h1lKntJoIrGs2Nx;byn,reG;a,ia;ke,p0;i,rer2K;em2liB;ns;!e;anu;aOeMiu,oIristGu6we;eGiaG;ns1;i,ng,p9uHwGy;!dH;dGng;huJ;!n,onGu6;!g;kJnIpm2ttHudhGv7;ry;erjee,o14;!d,g;ma,raboG;rty;bJl0Cng4rG;eghetHnG;a,y;ti;an,ota1C;cerAlder3mpbeLrIstGvadi0B;iGro;llo;doHl0Er,t0uGvalho;so;so,zo;ll;a0Fe01hYiXlUoNrKuIyG;rLtyG;qi;chan2rG;ke,ns;ank5iem,oGyant;oks,wG;ne;gdan5nIruya,su,uchaHyKziG;c,n5;rd;darGik;enG;ko;ov;aGond15;nco,zG;ev4;ancFshw16;a08oGuiy2;umGwmG;ik;ckRethov1gu,ktPnNrG;gJisInG;ascoGds1;ni;ha;er,mG;anG;!n;gtGit7nP;ss3;asF;hi;er,hG;am;b4ch,ez,hRiley,kk0ldw8nMrIshHtAu0;es;ir;bInHtlGua;ett;es,i0;ieYosa;dGik;a9yoG;padhyG;ay;ra;k,ng;ic;bb0Acos09d07g04kht05lZnPrLsl2tJyG;aHd8;in;la;chis3kiG;ns3;aImstro6sl2;an;ng;ujo,ya;dJgelHsaG;ri;ovG;!a;ersJov,reG;aGjEws;ss1;en;en,on,s3;on;eksejEiyEmeiIvG;ar7es;ez;da;ev;arwHuilG;ar;al;ams,l0;er;ta;as",Ordinal:"true¦eBf7nin5s3t0zeroE;enDhir1we0;lfCn7;d,t3;e0ixt8;cond,vent7;et0th;e6ie7;i2o0;r0urt3;tie4;ft1rst;ight0lev1;e0h,ie1;en0;th",Cardinal:"true¦bEeBf5mEnine7one,s4t0zero;en,h2rDw0;e0o;lve,n5;irt6ousands,ree;even2ix2;i3o0;r1ur0;!t2;ty;ft0ve;e2y;ight0lev1;!e0y;en;illions",Multiple:"true¦b3hundred,m3qu2se1t0;housand,r2;pt1xt1;adr0int0;illion",City:"true¦0:74;1:61;2:6G;3:6J;4:5S;a68b53c4Id48e44f3Wg3Hh39i31j2Wk2Fl23m1Mn1Co19p0Wq0Ur0Os05tRuQvLwDxiBy9z5;a7h5i4Muri4O;a5e5ongsh0;ng3H;greb,nzib5G;ang2e5okoha3Sunfu;katerin3Hrev0;a5n0Q;m5Hn;arsBeAi6roclBu5;h0xi,zh5P;c7n5;d5nipeg,terth4;hoek,s1L;hi5Zkl3A;l63xford;aw;a8e6i5ladivost5Molgogr6L;en3lni6S;ni22r5;o3saill4N;lenc4Wncouv3Sr3ughn;lan bat1Crumqi,trecht;aFbilisi,eEheDiBo9r7u5;l21n63r5;in,ku;i5ondh62;es51poli;kyo,m2Zron1Pulo5;n,uS;an5jua3l2Tmisoa6Bra3;j4Tshui; hag62ssaloni2H;gucigal26hr0l av1U;briz,i6llinn,mpe56ng5rtu,shk2R;i3Esh0;an,chu1n0p2Eyu0;aEeDh8kopje,owe1Gt7u5;ra5zh4X;ba0Ht;aten is55ockholm,rasbou67uttga2V;an8e6i5;jiazhua1llo1m5Xy0;f50n5;ya1zh4H;gh3Kt4Q;att45o1Vv44;cramen16int ClBn5o paulo,ppo3Rrajevo; 7aa,t5;a 5o domin3E;a3fe,m1M;antonio,die3Cfrancisco,j5ped3Nsalvad0J;o5u0;se;em,t lake ci5Fz25;lou58peters24;a9e8i6o5;me,t59;ga,o5yadh;! de janei3F;cife,ims,nn3Jykjavik;b4Sip4lei2Inc2Pwalpindi;ingdao,u5;ez2i0Q;aFeEhDiCo9r7u6yong5;ya1;eb59ya1;a5etor3M;g52to;rt5zn0; 5la4Co;au prin0Melizabe24sa03;ls3Prae5Atts26;iladelph3Gnom pe1Aoenix;ki1tah tik3E;dua,lerYnaji,r4Ot5;na,r32;ak44des0Km1Mr6s5ttawa;a3Vlo;an,d06;a7ew5ing2Fovosibir1Jyc; 5cast36;del24orlea44taip14;g8iro4Wn5pl2Wshv33v0;ch6ji1t5;es,o1;a1o1;a6o5p4;ya;no,sa0W;aEeCi9o6u5;mb2Ani26sc3Y;gadishu,nt6s5;c13ul;evideo,pelli1Rre2Z;ami,l6n14s5;kolc,sissauga;an,waukee;cca,d5lbour2Mmph41ndo1Cssi3;an,ell2Xi3;cau,drAkass2Sl9n8r5shh4A;aca6ib5rakesh,se2L;or;i1Sy;a4EchFdal0Zi47;mo;id;aDeAi8o6u5vSy2;anMckn0Odhia3;n5s angel26;d2g bea1N;brev2Be3Lma5nz,sb2verpo28;!ss27; ma39i5;c5pzig;est16; p6g5ho2Wn0Cusan24;os;az,la33;aHharFiClaipeBo9rak0Du7y5;iv,o5;to;ala lump4n5;mi1sh0;hi0Hlka2Xpavog4si5wlo2;ce;da;ev,n5rkuk;gst2sha5;sa;k5toum;iv;bHdu3llakuric0Qmpa3Fn6ohsiu1ra5un1Iwaguc0Q;c0Pj;d5o,p4;ah1Ty;a7e6i5ohannesV;l1Vn0;dd36rusalem;ip4k5;ar2H;bad0mph1OnArkutUs7taXz5;mir,tapala5;pa;fah0l6tanb5;ul;am2Zi2H;che2d5;ianap2Mo20;aAe7o5yder2W; chi mi5ms,nolulu;nh;f6lsin5rakli2;ki;ei;ifa,lifax,mCn5rb1Dva3;g8nov01oi;aFdanEenDhCiPlasgBo9raz,u5;a5jr23;dal6ng5yaquil;zh1J;aja2Oupe;ld coa1Bthen5;bu2S;ow;ent;e0Uoa;sk;lw7n5za;dhi5gt1E;nag0U;ay;aisal29es,o8r6ukuya5;ma;ankfu5esno;rt;rt5sh0; wor6ale5;za;th;d5indhov0Pl paso;in5mont2;bur5;gh;aBe8ha0Xisp4o7resd0Lu5;b5esseldorf,nkirk,rb0shanbe;ai,l0I;ha,nggu0rtmu13;hradSl6nv5troit;er;hi;donghIe6k09l5masc1Zr es sala1KugavpiY;i0lU;gu,je2;aJebu,hAleve0Vo5raio02uriti1Q;lo7n6penhag0Ar5;do1Ok;akKst0V;gUm5;bo;aBen8i6ongqi1ristchur5;ch;ang m7ca5ttago1;go;g6n5;ai;du,zho1;ng5ttogr14;ch8sha,zh07;gliari,i9lga8mayenJn6pe town,r5tanO;acCdiff;ber1Ac5;un;ry;ro;aWeNhKirmingh0WoJr9u5;chareTdapeTenos air7r5s0tu0;g5sa;as;es;a9is6usse5;ls;ba6t5;ol;ne;sil8tisla7zzav5;il5;le;va;ia;goZst2;op6ubaneshw5;ar;al;iCl9ng8r5;g6l5n;in;en;aluru,hazi;fa6grade,o horizon5;te;st;ji1rut;ghd0BkFn9ot8r7s6yan n4;ur;el,r07;celo3i,ranquil09;ou;du1g6ja lu5;ka;alo6k5;ok;re;ng;ers5u;field;a05b02cc01ddis aba00gartaZhmedXizawl,lSmPnHqa00rEsBt7uck5;la5;nd;he7l5;an5;ta;ns;h5unci2;dod,gab5;at;li5;ngt2;on;a8c5kaOtwerp;hora6o3;na;ge;h7p5;ol5;is;eim;aravati,m0s5;terd5;am; 7buquerq6eppo,giers,ma5;ty;ue;basrah al qadim5mawsil al jadid5;ah;ab5;ad;la;ba;ra;idj0u dha5;bi;an;lbo6rh5;us;rg",Region:"true¦0:2O;1:2L;2:2U;3:2F;a2Sb2Fc21d1Wes1Vf1Tg1Oh1Ki1Fj1Bk16l13m0Sn09o07pYqVrSsJtEuBverAw6y4zacatec2W;akut0o0Fu4;cat1k09;a5est 4isconsin,yomi1O;bengal,virgin0;rwick3shington4;! dc;acruz,mont;dmurt0t4;ah,tar4; 2Pa12;a6e5laxca1Vripu21u4;scaEva;langa2nnessee,x2J;bas10m4smQtar29;aulip2Hil nadu;a9elang07i7o5taf16u4ylh1J;ff02rr09s1E;me1Gno1Uuth 4;cZdY;ber0c4kkim,naloa;hu1ily;n5rawak,skatchew1xo4;ny; luis potosi,ta catari2;a4hodeA;j4ngp0C;asth1shahi;ingh29u4;e4intana roo;bec,en6retaro;aAe6rince edward4unjab; i4;sl0G;i,n5r4;ak,nambu0F;a0Rnsylv4;an0;ha0Pra4;!na;axa0Zdisha,h4klaho21ntar4reg7ss0Dx0I;io;aLeEo6u4;evo le4nav0X;on;r4tt18va scot0;f9mandy,th4; 4ampton3;c6d5yo4;rk3;ako1O;aroli2;olk;bras1Nva0Dw4; 6foundland4;! and labrad4;or;brunswick,hamp3jers5mexiTyork4;! state;ey;galPyarit;aAeghala0Mi6o4;nta2r4;dov0elos;ch6dlanDn5ss4zor11;issippi,ouri;as geraPneso18;ig1oac1;dhy12harasht0Gine,lac07ni5r4ssachusetts;anhao,i el,ylG;p4toba;ur;anca3e4incoln3ouisI;e4iR;ds;a6e5h4omi;aka06ul2;dah,lant1ntucky,ra01;bardino,lmyk0ns0Qr4;achay,el0nata0X;alis6har4iangxi;kh4;and;co;daho,llino7n4owa;d5gush4;et0;ia2;is;a6ert5i4un1;dalFm0D;ford3;mp3rya2waii;ansu,eorg0lou7oa,u4;an4izhou,jarat;ajuato,gdo4;ng;cester3;lori4uji1;da;sex;ageUe7o5uran4;go;rs4;et;lawaMrby3;aFeaEh9o4rim08umbr0;ahui7l6nnectic5rsi4ventry;ca;ut;i03orado;la;e5hattisgarh,i4uvash0;apRhuahua;chn5rke4;ss0;ya;ra;lGm4;bridge3peche;a9ihar,r8u4;ck4ryat0;ingham3;shi4;re;emen,itish columb0;h0ja cal8lk7s4v7;hkorto4que;st1;an;ar0;iforn0;ia;dygHguascalientes,lBndhr9r5ss4;am;izo2kans5un4;achal 7;as;na;a 4;pradesh;a6ber5t4;ai;ta;ba5s4;ka;ma;ea",Place:"true¦0:4T;1:4V;2:44;3:4B;4:3I;a4Eb3Gc2Td2Ge26f25g1Vh1Ji1Fk1Cl14m0Vn0No0Jp08r04sTtNuLvJw7y5;a5o0Syz;kut1Bngtze;aDeChitBi9o5upatki,ycom2P;ki26o5;d5l1B;b3Ps5;i4to3Y;c0SllowbroCn5;c2Qgh2;by,chur1P;ed0ntw3Gs22;ke6r3St5;erf1f1; is0Gf3V;auxha3Mirgin is0Jost5;ok;laanbaatar,pto5xb3E;n,wn;a9eotihuac43h7ive49o6ru2Nsarskoe selo,u5;l2Dzigo47;nto,rquay,tt2J;am3e 5orn3E;bronx,hamptons;hiti,j mah0Iu1N;aEcotts bluff,eCfo,herbroQoApring9t7u5yd2F;dbu1Wn5;der03set3B;aff1ock2Nr5;atf1oud;hi37w24;ho,uth5; 1Iam1Zwo3E;a5i2O;f2Tt0;int lawrence riv3Pkhal2D;ayleigh,ed7i5oc1Z;chmo1Eo gran4ver5;be1Dfr09si4; s39cliffe,hi2Y;aCe9h8i5ompeii,utn2;c6ne5tcai2T; 2Pc0G;keri13t0;l,x;k,lh2mbr6n5r2J;n1Hzance;oke;cif38pahanaumokuak30r5;k5then0;si4w1K;ak7r6x5;f1l2X;ange county,d,f1inoco;mTw1G;e8i1Uo5;r5tt2N;th5wi0E; 0Sam19;uschwanste1Pw5; eng6a5h2market,po36;rk;la0P;a8co,e6i5uc;dt1Yll0Z;adow5ko0H;lands;chu picchu,gad2Ridsto1Ql8n7ple6r5;kh2; g1Cw11;hatt2Osf2B;ibu,t0ve1Z;a8e7gw,hr,in5owlOynd02;coln memori5dl2C;al;asi4w3;kefr7mbe1On5s,x;ca2Ig5si05;f1l27t0;ont;azan kreml14e6itchen2Gosrae,rasnoyar5ul;sk;ns0Hs1U;ax,cn,lf1n6ps5st;wiN;d5glew0Lverness;ian27ochina;aDeBi6kg,nd,ov5unti2H;d,enweep;gh6llc5;reL;bu03l5;and5;!s;r5yw0C;ef1tf1;libu24mp6r5stings;f1lem,row;stead,t0;aDodavari,r5uelph;avenAe5imsS;at 8en5; 6f1Fwi5;ch;acr3vall1H;brita0Flak3;hur5;st;ng3y villa0W;airhavHco,ra;aAgli9nf17ppi8u7ver6x5;et1Lf1;glad3t0;rope,st0;ng;nt0;rls1Ls5;t 5;e5si4;nd;aCe9fw,ig8o7ryd6u5xb;mfri3nstab00rh2tt0;en;nca18rcKv19wnt0B;by;n6r5vonpo1D;ry;!h2;nu8r5;l6t5;f1moor;ingt0;be;aLdg,eIgk,hClBo5royd0;l6m5rnwa0B;pt0;c7lingw6osse5;um;ood;he0S;earwat0St;a8el6i5uuk;chen itza,mney ro07natSricahua;m0Zt5;enh2;mor5rlottetPth2;ro;dar 5ntervilA;breaks,faZg5;rove;ld9m8r5versh2;lis6rizo pla5;in;le;bLpbellf1;weQ;aZcn,eNingl01kk,lackLolt0r5uckV;aGiAo5;ckt0ok5wns cany0;lyn,s5;i4to5;ne;de;dge6gh5;am,t0;n6t5;own;or5;th;ceb6m5;lNpt0;rid5;ge;bu5pool,wa8;rn;aconsfEdf1lBr9verly7x5;hi5;ll; hi5;lls;wi5;ck; air,l5;ingh2;am;ie5;ld;ltimore,rnsl6tters5;ea;ey;bLct0driadic,frica,ginJlGmFn9rc8s7tl6yleOzor3;es;!ant8;hcroft,ia; de triomphe,t6;adyr,ca8dov9tarct5;ic5; oce5;an;st5;er;ericas,s;be6dersh5hambra,list0;ot;rt0;cou5;rt;bot7i5;ngd0;on;sf1;ord",Country:"true¦0:38;1:2L;2:3B;a2Xb2Ec22d1Ye1Sf1Mg1Ch1Ai14j12k0Zl0Um0Gn05om2pZqat1KrXsKtCu7v5wal4yemTz3;a25imbabwe;es,lis and futu2Y;a3enezue32ietnam;nuatu,tican city;gTk6nited 4ruXs3zbeE; 2Ca,sr;arab emirat0Kkingdom,states3;! of am2Y;!raiV;a8haCimor les0Co7rinidad 5u3;nis0rk3valu;ey,me2Zs and caic1V;and t3t3;oba1L;go,kel10nga;iw2ji3nz2T;ki2V;aDcotl1eCi9lov8o6pa2Dri lanka,u5w3yr0;az3edAitzerl1;il1;d2riname;lomon1Xmal0uth 3;afr2KkMsud2;ak0en0;erra leoFn3;gapo1Yt maart3;en;negLrb0ychellZ;int 3moa,n marino,udi arab0;hele26luc0mart21;epublic of ir0Eom2Euss0w3;an27;a4eIhilippinUitcairn1Mo3uerto riN;l1rtugF;ki2Dl4nama,pua new0Vra3;gu7;au,esti3;ne;aBe9i7or3;folk1Ith4w3;ay; k3ern mariana1D;or0O;caragua,ger3ue;!ia;p3ther1Aw zeal1;al;mib0u3;ru;a7exi6icro0Bo3yanm06;ldova,n3roc5zambA;a4gol0t3;enegro,serrat;co;cAdagasc01l7r5urit4yot3;te;an0i16;shall0Xtin3;ique;a4div3i,ta;es;wi,ys0;ao,ed02;a6e5i3uxembourg;b3echtenste12thu1G;er0ya;ban0Isotho;os,tv0;azakh1Fe4iriba04o3uwait,yrgyz1F;rXsovo;eling0Knya;a3erG;ma16p2;c7nd6r4s3taly,vory coast;le of m2rael;a3el1;n,q;ia,oJ;el1;aiTon3ungary;dur0Ng kong;aBermany,ha0QibraltAre8u3;a6ern5inea3ya0P;! biss3;au;sey;deloupe,m,tema0Q;e3na0N;ce,nl1;ar;bUmb0;a7i6r3;ance,ench 3;guia0Epoly3;nes0;ji,nl1;lklandUroeU;ast tim7cu6gypt,l salv6ngl1quatorial4ritr5st3thiop0;on0; guin3;ea;ad3;or;enmark,jibou5ominica4r con3;go;!n C;ti;aBentral african Ah8o5roat0u4yprRzech3; 9ia;ba,racao;c4lo3morQngo brazzaville,okGsta r04te de ivoiL;mb0;osE;i3ristmasG;le,na;republic;m3naUpe verde,ymanA;bod0ero3;on;aGeDhut2o9r5u3;lgar0r3;kina faso,ma,undi;azil,itish 3unei;virgin3; is3;lands;liv0nai5snia and herzegoviHtswaHuvet3; isl1;and;re;l3n8rmuG;ar3gium,ize;us;h4ngladesh,rbad3;os;am4ra3;in;as;fghaGlDmBn6r4ustr3zerbaij2;al0ia;genti3men0uba;na;dorra,g5t3;arct7igua and barbu3;da;o3uil3;la;er3;ica;b3ger0;an0;ia;ni3;st2;an",FirstName:"true¦aTblair,cQdOfrancoZgabMhinaLilya,jHkClBm6ni4quinn,re3s0;h0umit,yd;ay,e0iloh;a,lby;g9ne;co,ko0;!s;a1el0ina,org6;!okuhF;ds,naia,r1tt0xiB;i,y;ion,lo;ashawn,eif,uca;a3e1ir0rM;an;lsFn0rry;dall,yat5;i,sD;a0essIie,ude;i1m0;ie,mG;me;ta;rie0y;le;arcy,ev0;an,on;as1h0;arl8eyenne;ey,sidy;drien,kira,l4nd1ubr0vi;ey;i,r0;a,e0;a,y;ex2f1o0;is;ie;ei,is",WeekDay:"true¦fri2mon2s1t0wednesd3;hurs1ues1;aturd1und1;!d0;ay0;!s",Month:"true¦dec0february,july,nov0octo1sept0;em0;ber",Date:"true¦ago,on4som4t1week0yesterd5; end,ends;mr1o0;d2morrow;!w;ed0;ay",Duration:"true¦centurAd8h7m5q4se3w1y0;ear8r8;eek0k7;!end,s;ason,c5;tr,uarter;i0onth3;llisecond2nute2;our1r1;ay0ecade0;!s;ies,y",FemaleName:"true¦0:J7;1:JB;2:IJ;3:IK;4:J1;5:IO;6:JS;7:JO;8:HB;9:JK;A:H4;B:I2;C:IT;D:JH;E:IX;F:BA;G:I4;aGTbFLcDRdD0eBMfB4gADh9Ti9Gj8Dk7Cl5Wm48n3Lo3Hp33qu32r29s15t0Eu0Cv02wVxiTyOzH;aLeIineb,oHsof3;e3Sf3la,ra;h2iKlIna,ynH;ab,ep;da,ma;da,h2iHra;nab;aKeJi0FolB7uIvH;et8onDP;i0na;le0sen3;el,gm3Hn,rGLs8W;aoHme0nyi;m5XyAD;aMendDZhiDGiH;dele9lJnH;if48niHo0;e,f47;a,helmi0lHma;a,ow;ka0nB;aNeKiHusa5;ck84kIl8oleAviH;anFenJ4;ky,toriBK;da,lA8rHs0;a,nHoniH9;a,iFR;leHnesH9;nILrH;i1y;g9rHs6xHA;su5te;aYeUhRiNoLrIuHy2;i,la;acJ3iHu0J;c3na,sH;hFta;nHr0F;iFya;aJffaEOnHs6;a,gtiH;ng;!nFSra;aIeHomasi0;a,l9Oo8Ares1;l3ndolwethu;g9Fo88rIssH;!a,ie;eHi,ri7;sa,za;bOlMmKnIrHs6tia0wa0;a60yn;iHya;a,ka,s6;arFe2iHm77ra;!ka;a,iH;a,t6;at6it6;a0Ecarlett,e0AhWiSkye,neza0oQri,tNuIyH;bIGlvi1;ha,mayIJniAsIzH;an3Net8ie,y;anHi7;!a,e,nH;aCe;aIeH;fan4l5Dphan6E;cI5r5;b3fiAAm0LnHphi1;d2ia,ja,ya;er2lJmon1nIobh8QtH;a,i;dy;lETv3;aMeIirHo0risFDy5;a,lDM;ba,e0i5lJrH;iHr6Jyl;!d8Ifa;ia,lDZ;hd,iMki2nJrIu0w0yH;la,ma,na;i,le9on,ron,yn;aIda,ia,nHon;a,on;!ya;k6mH;!aa;lJrItaye82vH;da,inj;e0ife;en1i0ma;anA9bLd5Oh1SiBkKlJmInd2rHs6vannaC;aCi0;ant6i2;lDOma,ome;ee0in8Tu2;in1ri0;a05eZhXiUoHuthDM;bScRghQl8LnPsJwIxH;anB3ie,y;an,e0;aIeHie,lD;ann7ll1marDGtA;!lHnn1;iHyn;e,nH;a,dF;da,i,na;ayy8G;hel67io;bDRerAyn;a,cIkHmas,nFta,ya;ki,o;h8Xki;ea,iannGMoH;da,n1P;an0bJemFgi0iInHta,y0;a8Bee;han86na;a,eH;cHkaC;a,ca;bi0chIe,i0mo0nHquETy0;di,ia;aERelHiB;!e,le;een4ia0;aPeOhMiLoJrHute6A;iHudenCV;scil3LyamvaB;lHrt3;i0ly;a,paluk;ilome0oebe,ylH;is,lis;ggy,nelope,r5t2;ige,m0VnKo5rvaDMtIulH;a,et8in1;ricHt4T;a,e,ia;do2i07;ctav3dIfD3is6ksa0lHphD3umC5yunbileg;a,ga,iv3;eHvAF;l3t8;aWeUiMoIurHy5;!ay,ul;a,eJor,rIuH;f,r;aCeEma;ll1mi;aNcLhariBQkKlaJna,sHta,vi;anHha;ur;!y;a,iDZki;hoGk9YolH;a,e4P;!mh;hir,lHna,risDEsreE;!a,lBV;asuMdLh3i6Dl5nKomi7rgEVtH;aHhal4;lHs6;i1ya;cy,et8;e9iF0ya;nngu2X;a0Ackenz4e02iMoJrignayani,uriDJyH;a,rH;a,iOlNna,tG;bi0i2llBJnH;a,iH;ca,ka,qD9;a,cUdo4ZkaTlOmi,nMrItzi,yH;ar;aJiIlH;anET;am;!l,nB;dy,eHh,n4;nhGrva;aKdJe0iCUlH;iHy;cent,e;red;!gros;!e5;ae5hH;ae5el3Z;ag5DgNi,lKrH;edi7AiIjem,on,yH;em,l;em,sCG;an4iHliCF;nHsCJ;a,da;!an,han;b09cASd07e,g05ha,i04ja,l02n00rLsoum5YtKuIv84xBKyHz4;bell,ra,soBB;d7rH;a,eE;h8Gild1t4;a,cUgQiKjor4l7Un4s6tJwa,yH;!aHbe6Xja9lAE;m,nBL;a,ha,in1;!aJbCGeIja,lDna,sHt63;!a,ol,sa;!l1D;!h,mInH;!a,e,n1;!awit,i;arJeIie,oHr48ueri8;!t;!ry;et46i3B;el4Xi7Cy;dHon,ue5;akranAy;ak,en,iHlo3S;a,ka,nB;a,re,s4te;daHg4;!l3E;alDd4elHge,isDJon0;ei9in1yn;el,le;a0Ne0CiXoQuLyH;d3la,nH;!a,dIe2OnHsCT;!a,e2N;a,sCR;aD4cJel0Pis1lIna,pHz;e,iA;a,u,wa;iHy;a0Se,ja,l2NnB;is,l1UrItt1LuHvel4;el5is1;aKeIi7na,rH;aADi7;lHn1tA;ei;!in1;aTbb9HdSepa,lNnKsJvIzH;!a,be5Ret8z4;!ia;a,et8;!a,dH;a,sHy;ay,ey,i,y;a,iJja,lH;iHy;aA8e;!aH;!nF;ia,ya;!nH;!a,ne;aPda,e0iNjYla,nMoKsJtHx93y5;iHt4;c3t3;e2PlCO;la,nHra;a,ie,o2;a,or1;a,gh,laH;!ni;!h,nH;a,d2e,n5V;cOdon9DiNkes6mi9Gna,rMtJurIvHxmi,y5;ern1in3;a,e5Aie,yn;as6iIoH;nya,ya;fa,s6;a,isA9;a,la;ey,ie,y;a04eZhXiOlASoNrJyH;lHra;a,ee,ie;istHy6I;a,en,iIyH;!na;!e,n5F;nul,ri,urtnB8;aOerNlB7mJrHzzy;a,stH;en,in;!berlImernH;aq;eHi,y;e,y;a,stE;!na,ra;aHei2ongordzol;dij1w5;el7UiKjsi,lJnIrH;a,i,ri;d2na,za;ey,i,lBLs4y;ra,s6;biAcARdiat7MeBAiSlQmPnyakuma1DrNss6NtKviAyH;!e,lH;a,eH;e,i8T;!a6HeIhHi4TlDri0y;ar8Her8Hie,leErBAy;!lyn8Ori0;a,en,iHl5Xoli0yn;!ma,nFs95;a5il1;ei8Mi,lH;e,ie;a,tl6O;a0AeZiWoOuH;anMdLlHst88;es,iH;a8NeHs8X;!n9tH;!a,te;e5Mi3My;a,iA;!anNcelDdMelGhan7VleLni,sIva0yH;a,ce;eHie;fHlDph7Y;a,in1;en,n1;i7y;!a,e,n45;lHng;!i1DlH;!i1C;anNle0nKrJsH;i8JsH;!e,i8I;i,ri;!a,elGif2CnH;a,et8iHy;!e,f2A;a,eJiInH;a,eIiH;e,n1;!t8;cMda,mi,nIque4YsminFvie2y9zH;min7;a7eIiH;ce,e,n1s;!lHs82t0F;e,le;inIk6HlDquelH;in1yn;da,ta;da,lRmPnOo0rNsIvaHwo0zaro;!a0lu,na;aJiIlaHob89;!n9R;do2;belHdo2;!a,e,l3B;a7Ben1i0ma;di2es,gr72ji;a9elBogH;en1;a,e9iHo0se;a0na;aSeOiJoHus7Kyacin2C;da,ll4rten24snH;a,i9U;lImaH;ri;aIdHlaI;a,egard;ry;ath1BiJlInrietArmi9sH;sa,t1A;en2Uga,mi;di;bi2Fil8MlNnMrJsItHwa,yl8M;i5Tt4;n60ti;iHmo51ri53;etH;!te;aCnaC;a,ey,l4;a02eWiRlPoNrKunJwH;enHyne1R;!dolD;ay,el;acieIetHiselB;a,chE;!la;ld1CogooH;sh;adys,enHor3yn2K;a,da,na;aKgi,lIna,ov8EselHta;a,e,le;da,liH;an;!n0;mLnJorgIrH;ald5Si,m3Etrud7;et8i4X;a,eHna;s29vieve;ma;bIle,mHrnet,yG;al5Si5;iIrielH;a,l1;!ja;aTeQiPlorOoz3rH;anJeIiH;da,eB;da,ja;!cH;esIiHoi0P;n1s66;!ca;a,enc3;en,o0;lIn0rnH;anB;ec3ic3;jr,nArKtHy7;emIiHma,oumaA;ha,ma,n;eh;ah,iBrah,za0;cr4Rd0Re0Qi0Pk0Ol07mXn54rUsOtNuMvHwa;aKelIiH;!e,ta;inFyn;!a;!ngel4V;geni1ni47;h5Yien9ta;mLperanKtH;eIhHrel5;er;l31r7;za;a,eralB;iHma,ne4Lyn;cHka,n;a,ka;aPeNiKmH;aHe21ie,y;!li9nuH;elG;lHn1;e7iHy;a,e,ja;lHrald;da,y;!nue5;aWeUiNlMma,no2oKsJvH;a,iH;na,ra;a,ie;iHuiH;se;a,en,ie,y;a0c3da,e,f,nMsJzaH;!betHveA;e,h;aHe,ka;!beH;th;!a,or;anor,nH;!a,i;!in1na;ate1Rta;leEs6;vi;eIiHna,wi0;e,th;l,n;aYeMh3iLjeneKoH;lor5Vminiq4Ln3FrHtt4;a,eEis,la,othHthy;ea,y;ba;an09naCon9ya;anQbPde,eOiMlJmetr3nHsir5M;a,iH;ce,se;a,iIla,orHphi9;es,is;a,l6F;dHrdH;re;!d5Ena;!b2ForaCraC;a,d2nH;!a,e;hl3i0l0GmNnLphn1rIvi1WyH;le,na;a,by,cIia,lH;a,en1;ey,ie;a,et8iH;!ca,el1Aka,z;arHia;is;a0Re0Nh04i02lUoJristIynH;di,th3;al,i0;lPnMrIurH;tn1D;aJd2OiHn2Ori9;!nH;a,e,n1;!l4;cepci5Cn4sH;tanHuelo;ce,za;eHleE;en,t8;aJeoIotH;il54;!pat2;ir7rJudH;et8iH;a,ne;a,e,iH;ce,sZ;a2er2ndH;i,y;aReNloe,rH;isJyH;stH;al;sy,tH;a1Sen,iHy;an1e,n1;deJlseIrH;!i7yl;a,y;li9;nMrH;isKlImH;ai9;a,eHot8;n1t8;!sa;d2elGtH;al,elG;cIlH;es8i47;el3ilH;e,ia,y;itlYlXmilWndVrMsKtHy5;aIeIhHri0;er1IleErDy;ri0;a38sH;a37ie;a,iOlLmeJolIrH;ie,ol;!e,in1yn;lHn;!a,la;a,eIie,otHy;a,ta;ne,y;na,s1X;a0Ii0I;a,e,l1;isAl4;in,yn;a0Ke02iZlXoUrH;andi7eRiJoIyH;an0nn;nwDoke;an3HdgMgiLtH;n31tH;!aInH;ey,i,y;ny;d,t8;etH;!t7;an0e,nH;da,na;bbi7glarIlo07nH;iAn4;ka;ancHythe;a,he;an1Clja0nHsm3M;iAtH;ou;aWcVlinUniArPssOtJulaCvH;!erlH;ey,y;hJsy,tH;e,iHy7;e,na;!anH;ie,y;!ie;nItHyl;ha,ie;adIiH;ce;et8i9;ay,da;ca,ky;!triH;ce,z;rbJyaH;rmH;aa;a2o2ra;a2Ub2Od25g21i1Sj5l18m0Zn0Boi,r06sWtVuPvOwa,yIzH;ra,u0;aKes6gJlIn,seH;!l;in;un;!nH;a,na;a,i2K;drLguJrIsteH;ja;el3;stH;in1;a,ey,i,y;aahua,he0;hIi2Gja,miAs2DtrH;id;aMlIraqHt21;at;eIi7yH;!n;e,iHy;gh;!nH;ti;iJleIo6piA;ta;en,n1t8;aHelG;!n1J;a01dje5eZgViTjRnKohito,toHya;inet8nH;el5ia;te;!aKeIiHmJ;e,ka;!mHtt7;ar4;!belIliHmU;sa;!l1;a,eliH;ca;ka,sHta;a,sa;elHie;a,iH;a,ca,n1qH;ue;!tH;a,te;!bImHstasiMya;ar3;el;aLberKeliJiHy;e,l3naH;!ta;a,ja;!ly;hGiIl3nB;da;a,ra;le;aWba,ePiMlKthJyH;a,c3sH;a,on,sa;ea;iHys0N;e,s0M;a,cIn1sHza;a,e,ha,on,sa;e,ia,ja;c3is6jaKksaKna,sJxH;aHia;!nd2;ia,saH;nd2;ra;ia;i0nIyH;ah,na;a,is,naCoud;la;c6da,leEmNnLsH;haClH;inHyY;g,n;!h;a,o,slH;ey;ee;en;at6g4nIusH;ti0;es;ie;aWdiTelMrH;eJiH;anMenH;a,e,ne;an0;na;!aLeKiIyH;nn;a,n1;a,e;!ne;!iH;de;e,lDsH;on;yn;!lH;i9yn;ne;aKbIiHrL;!e,gaK;ey,i7y;!e;gaH;il;dKliyJradhIs6;ha;ya;ah;a,ya",Honorific:"true¦director1field marsh2lieutenant1rear0sergeant major,vice0; admir1; gener0;al","Adj|Gerund":"true¦0:3F;1:3H;2:31;3:2X;4:35;5:33;6:3C;7:2Z;8:36;9:29;a33b2Tc2Bd1Te1If19g12h0Zi0Rl0Nm0Gnu0Fo0Ap04rYsKtEuBvAw1Ayiel3;ar6e08;nBpA;l1Rs0B;fol3n1Zsett2;aEeDhrBi4ouc7rAwis0;e0Bif2oub2us0yi1;ea1SiA;l2vi1;l2mp0rr1J;nt1Vxi1;aMcreec7enten2NhLkyrocke0lo0Vmi2oJpHtDuBweA;e0Ul2;pp2ArA;gi1pri5roun3;aBea8iAri2Hun9;mula0r4;gge4rA;t2vi1;ark2eAraw2;e3llb2F;aAot7;ki1ri1;i9oc29;dYtisf6;aEeBive0oAus7;a4l2;assu4defi9fres7ig9juve07mai9s0vAwar3;ea2italiAol1G;si1zi1;gi1ll6mb2vi1;a6eDier23lun1VrAun2C;eBoA;mi5vo1Z;ce3s5vai2;n3rpleA;xi1;ffCpWutBverAwi1;arc7lap04p0Pri3whel8;goi1l6st1J;en3sA;et0;m2Jrtu4;aEeDiCoBuAyst0L;mb2;t1Jvi1;s5tiga0;an1Rl0n3smeri26;dAtu4;de9;aCeaBiAo0U;fesa0Tvi1;di1ni1;c1Fg19s0;llumiGmFnArri0R;cDfurHsCtBviA;go23ti1;e1Oimi21oxica0rig0V;pi4ul0;orpo20r0K;po5;na0;eaBorr02umilA;ia0;li1rtwar8;lFrA;atiDipCoBuelA;i1li1;undbrea10wi1;pi1;f6ng;a4ea8;a3etc7it0lEoCrBulfA;il2;ee1FighXust1L;rAun3;ebo3thco8;aCoA;a0wA;e4i1;mi1tte4;lectrJmHnExA;aCci0hBis0pA;an3lo3;aOila1B;c0spe1A;ab2coura0CdBergi13ga0Clive9ric7s02tA;hral2i0J;ea4u4;barras5er09pA;owe4;if6;aQeIiBrA;if0;sAzz6;aEgDhearCsen0tA;rAur11;ac0es5;te9;us0;ppoin0r8;biliGcDfi9gra3ligh0mBpres5sAvasG;erE;an3ea9orA;ali0L;a6eiBli9rA;ea5;vi1;ta0;maPri1s7un0zz2;aPhMlo5oAripp2ut0;mGnArrespon3;cer9fDspi4tA;inBrA;as0ibu0ol2;ui1;lic0u5;ni1;fDmCpA;eAromi5;l2ti1;an3;or0;aAil2;llenAnAr8;gi1;l8ptAri1;iva0;aff2eGin3lFoDrBuA;d3st2;eathtaAui5;ki1;gg2i2o8ri1unA;ci1;in3;co8wiA;lAtc7;de4;bsorVcOgonMlJmHnno6ppea2rFsA;pi4su4toA;nBun3;di1;is7;hi1;res0;li1;aFu5;si1;ar8lu4;ri1;mi1;iAzi1;zi1;cAhi1;eleDomA;moBpan6;yi1;da0;ra0;ti1;bi1;ng",Comparable:"true¦0:3C;1:3Q;2:3F;a3Tb3Cc33d2Te2Mf2Ag1Wh1Li1Fj1Ek1Bl13m0Xn0So0Rp0Iqu0Gr07sHtCug0vAw4y3za0Q;el10ouN;ary,e6hi5i3ry;ck0Cde,l3n1ry,se;d,y;ny,te;a3i3R;k,ry;a3erda2ulgar;gue,in,st;a6en2Xhi5i4ouZr3;anqu2Cen1ue;dy,g36me0ny;ck,rs28;ll,me,rt,wd3I;aRcaPeOhMiLkin0BlImGoEpDt6u4w3;eet,ift;b3dd0Wperfi21rre28;sta26t21;a8e7iff,r4u3;pUr1;a4ict,o3;ng;ig2Vn0N;a1ep,rn;le,rk,te0;e1Si2Vright0;ci1Yft,l3on,re;emn,id;a3el0;ll,rt;e4i3y;g2Mm0Z;ek,nd2T;ck24l0mp1L;a3iRrill,y;dy,l01rp;ve0Jxy;n1Jr3;ce,y;d,fe,int0l1Hv0V;a8e6i5o3ude;mantic,o19sy,u3;gh;pe,t1P;a3d,mo0A;dy,l;gg4iFndom,p3re,w;id;ed;ai2i3;ck,et;hoAi1Fl9o8r5u3;ny,r3;e,p11;egna2ic4o3;fouSud;ey,k0;liXor;ain,easa2;ny;dd,i0ld,ranL;aive,e5i4o3u14;b0Sisy,rm0Ysy;bb0ce,mb0R;a3r1w;r,t;ad,e5ild,o4u3;nda12te;ist,o1;a4ek,l3;low;s0ty;a8e7i6o3ucky;f0Jn4o15u3ve0w10y0N;d,sy;e0g;ke0l,mp,tt0Eve0;e1Qwd;me,r3te;ge;e4i3;nd;en;ol0ui19;cy,ll,n3;secu6t3;e3ima4;llege2rmedia3;te;re;aAe7i6o5u3;ge,m3ng1C;bYid;me0t;gh,l0;a3fXsita2;dy,rWv3;en0y;nd13ppy,r3;d3sh;!y;aFenEhCiBlAoofy,r3;a8e6i5o3ue0Z;o3ss;vy;m,s0;at,e3y;dy,n;nd,y;ad,ib,ooD;a2d1;a3o3;st0;tDuiS;u1y;aCeebBi9l8o6r5u3;ll,n3r0N;!ny;aCesh,iend0;a3nd,rmD;my;at,ir7;erce,nan3;ci9;le;r,ul3;ty;a6erie,sse4v3xtre0B;il;nti3;al;r4s3;tern,y;ly,th0;appZe9i5ru4u3;mb;nk;r5vi4z3;zy;ne;e,ty;a3ep,n9;d3f,r;!ly;agey,h8l7o5r4u3;dd0r0te;isp,uel;ar3ld,mmon,st0ward0zy;se;evKou1;e3il0;ap,e3;sy;aHiFlCoAr5u3;ff,r0sy;ly;a6i3oad;g4llia2;nt;ht;sh,ve;ld,un3;cy;a4o3ue;nd,o1;ck,nd;g,tt3;er;d,ld,w1;dy;bsu6ng5we3;so3;me;ry;rd",Adverb:"true¦a08b05d00eYfSheQinPjustOkinda,likewiZmMnJoEpCquite,r9s5t2u0very,well;ltima01p0; to,wards5;h1iny bit,o0wiO;o,t6;en,us;eldom,o0uch;!me1rt0; of;how,times,w0C;a1e0;alS;ndomRth05;ar excellenEer0oint blank; Lhaps;f3n0utright;ce0ly;! 0;ag05moX; courGten;ewJo0; longWt 0;onHwithstand9;aybe,eanwhiNore0;!ovT;! aboX;deed,steY;lla,n0;ce;or3u0;ck1l9rther0;!moK;ing; 0evK;exampCgood,suH;n mas0vI;se;e0irect2; 2fini0;te0;ly;juAtrop;ackward,y 0;far,no0; means,w; GbroFd nauseam,gEl7ny5part,s4t 2w0;ay,hi0;le;be7l0mo7wor7;arge,ea6; soon,i4;mo0way;re;l 3mo2ongsi1ready,so,togeth0ways;er;de;st;b1t0;hat;ut;ain;ad;lot,posteriori",Conjunction:"true¦aXbTcReNhowMiEjust00noBo9p8supposing,t5wh0yet;e1il0o3;e,st;n1re0thN; if,by,vM;evL;h0il,o;erefOo0;!uU;lus,rovided th9;r0therwiM;! not; mattEr,w0;! 0;since,th4w7;f4n0; 0asmuch;as mIcaForder t0;h0o;at;! 0;only,t0w0;hen;!ev3;ith2ven0;! 0;if,tB;er;o0uz;s,z;e0ut,y the time;cau1f0;ore;se;lt3nd,s 0;far1if,m0soon1t2;uch0; as;hou0;gh",Currency:"true¦$,aud,bQcOdJeurIfHgbp,hkd,iGjpy,kElDp8r7s3usd,x2y1z0¢,£,¥,ден,лв,руб,฿,₡,₨,€,₭,﷼;lotyQł;en,uanP;af,of;h0t5;e0il5;k0q0;elK;oubleJp,upeeJ;e2ound st0;er0;lingG;n0soF;ceEnies;empi7i7;n,r0wanzaCyatC;!onaBw;ls,nr;ori7ranc9;!os;en3i2kk,o0;b0ll2;ra5;me4n0rham4;ar3;e0ny;nt1;aht,itcoin0;!s",Determiner:"true¦aBboth,d9e6few,le5mu8neiDplenty,s4th2various,wh0;at0ich0;evC;a0e4is,ose;!t;everal,ome;!ast,s;a1l0very;!se;ch;e0u;!s;!n0;!o0y;th0;er","Adj|Present":"true¦a07b04cVdQeNfJhollIidRlEmCnarrIoBp9qua8r7s3t2uttFw0;aKet,ro0;ng,u08;endChin;e2hort,l1mooth,our,pa9tray,u0;re,speU;i2ow;cu6da02leSpaN;eplica01i02;ck;aHerfePr0;eseUime,omV;bscu1pen,wn;atu0e3odeH;re;a2e1ive,ow0;er;an;st,y;ow;a2i1oul,r0;ee,inge;rm;iIke,ncy,st;l1mpty,x0;emHpress;abo4ic7;amp,e2i1oub0ry,ull;le;ffu9re6;fu8libe0;raE;alm,l5o0;mpleCn3ol,rr1unterfe0;it;e0u7;ct;juga8sum7;ea1o0;se;n,r;ankru1lu0;nt;pt;li2pproxi0rticula1;ma0;te;ght","Person|Adj":"true¦b3du2earnest,frank,mi2r0san1woo1;an0ich,u1;dy;sty;ella,rown",Modal:"true¦c5lets,m4ought3sh1w0;ill,o5;a0o4;ll,nt;! to,a;ight,ust;an,o0;uld",Verb:"true¦born,cannot,gonna,has,keep tabs,msg","Person|Verb":"true¦b8ch7dr6foster,gra5ja9lan4ma2ni9ollie,p1rob,s0wade;kip,pike,t5ue;at,eg,ier2;ck,r0;k,shal;ce;ce,nt;ew;ase,u1;iff,l1ob,u0;ck;aze,ossom","Person|Date":"true¦a2j0sep;an0une;!uary;p0ugust,v0;ril"};const hr=36,gr="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",mr=gr.split("").reduce((function(e,t,n){return e[t]=n,e}),{});var pr=function(e){if(void 0!==mr[e])return mr[e];let t=0,n=1,a=hr,r=1;for(;n<e.length;t+=a,n++,a*=hr);for(let n=e.length-1;n>=0;n--,r*=hr){let a=e.charCodeAt(n)-48;a>10&&(a-=7),t+=a*r}return t};const fr=function(e,t,n){const a=pr(t);return a<e.symCount?e.syms[a]:n+a+1-e.symCount},br=function(e){const t={nodes:e.split(";"),syms:[],symCount:0};return e.match(":")&&function(e){const t=new RegExp("([0-9A-Z]+):([0-9A-Z]+)");for(let n=0;n<e.nodes.length;n++){const a=t.exec(e.nodes[n]);if(!a){e.symCount=n;break}e.syms[pr(a[1])]=pr(a[2])}e.nodes=e.nodes.slice(e.symCount,e.nodes.length)}(t),function(e){const t=[],n=(a,r)=>{let o=e.nodes[a];"!"===o[0]&&(t.push(r),o=o.slice(1));const i=o.split(/([A-Z0-9,]+)/g);for(let o=0;o<i.length;o+=2){const s=i[o],l=i[o+1];if(!s)continue;const u=r+s;if(","===l||void 0===l){t.push(u);continue}const c=fr(e,l,a);n(c,u)}};return n(0,""),t}(t)},yr=["Possessive","Pronoun"];const vr={a:[[/(antenn|formul|nebul|vertebr|vit)a$/i,"$1ae"],[/ia$/i,"ia"]],e:[[/(kn|l|w)ife$/i,"$1ives"],[/(hive)$/i,"$1s"],[/([m|l])ouse$/i,"$1ice"],[/([m|l])ice$/i,"$1ice"]],f:[[/^(dwar|handkerchie|hoo|scar|whar)f$/i,"$1ves"],[/^((?:ca|e|ha|(?:our|them|your)?se|she|wo)l|lea|loa|shea|thie)f$/i,"$1ves"]],i:[[/(octop|vir)i$/i,"$1i"]],m:[[/([ti])um$/i,"$1a"]],n:[[/^(oxen)$/i,"$1"]],o:[[/(al|ad|at|er|et|ed)o$/i,"$1oes"]],s:[[/(ax|test)is$/i,"$1es"],[/(alias|status)$/i,"$1es"],[/sis$/i,"ses"],[/(bu)s$/i,"$1ses"],[/(sis)$/i,"ses"],[/^(?!talis|.*hu)(.*)man$/i,"$1men"],[/(octop|vir|radi|nucle|fung|cact|stimul)us$/i,"$1i"]],x:[[/(matr|vert|ind|cort)(ix|ex)$/i,"$1ices"],[/^(ox)$/i,"$1en"]],y:[[/([^aeiouy]|qu)y$/i,"$1ies"]],z:[[/(quiz)$/i,"$1zes"]]},wr=/([xsz]|ch|sh)$/,kr=function(e="",t){let{irregularPlurals:n,uncountable:a}=t.two;if(a.hasOwnProperty(e))return e;if(n.hasOwnProperty(e))return n[e];let r=function(e){let t=e[e.length-1];if(!0===vr.hasOwnProperty(t))for(let n=0;n<vr[t].length;n+=1){let a=vr[t][n][0];if(!0===a.test(e))return e.replace(a,vr[t][n][1])}return null}(e);return null!==r?r:wr.test(e)?e+"es":e+"s"},Pr=/\|/;let Ar={"20th century fox":"Organization","7 eleven":"Organization","motel 6":"Organization",g8:"Organization",vh1:"Organization","76ers":"SportsTeam","49ers":"SportsTeam",q1:"Date",q2:"Date",q3:"Date",q4:"Date",km2:"Unit",m2:"Unit",dm2:"Unit",cm2:"Unit",mm2:"Unit",mile2:"Unit",in2:"Unit",yd2:"Unit",ft2:"Unit",m3:"Unit",dm3:"Unit",cm3:"Unit",in3:"Unit",ft3:"Unit",yd3:"Unit","at&t":"Organization","black & decker":"Organization","h & m":"Organization","johnson & johnson":"Organization","procter & gamble":"Organization","ben & jerry's":"Organization","&":"Conjunction",i:["Pronoun","Singular"],he:["Pronoun","Singular"],she:["Pronoun","Singular"],it:["Pronoun","Singular"],they:["Pronoun","Plural"],we:["Pronoun","Plural"],was:["Copula","PastTense"],is:["Copula","PresentTense"],are:["Copula","PresentTense"],am:["Copula","PresentTense"],were:["Copula","PastTense"],her:yr,his:yr,hers:yr,their:yr,theirs:yr,themselves:yr,your:yr,our:yr,ours:yr,my:yr,its:yr,vs:["Conjunction","Abbreviation"],if:["Condition","Preposition"],closer:"Comparative",closest:"Superlative",much:"Adverb",may:"Modal",babysat:"PastTense",blew:"PastTense",drank:"PastTense",drove:"PastTense",forgave:"PastTense",skiied:"PastTense",spilt:"PastTense",stung:"PastTense",swam:"PastTense",swung:"PastTense",guaranteed:"PastTense",shrunk:"PastTense",nears:"PresentTense",nearing:"Gerund",neared:"PastTense",no:["Negative","Expression"]},Cr={};const jr={two:{irregularPlurals:cr,uncountable:{}}};Object.keys(dr).forEach((e=>{let t=function(e){if(!e)return{};const t=e.split("|").reduce(((e,t)=>{const n=t.split("¦");return e[n[0]]=n[1],e}),{}),n={};return Object.keys(t).forEach((function(e){const a=br(t[e]);"true"===e&&(e=!0);for(let t=0;t<a.length;t++){const r=a[t];!0===n.hasOwnProperty(r)?!1===Array.isArray(n[r])?n[r]=[n[r],e]:n[r].push(e):n[r]=e}})),n}(dr[e]);Pr.test(e)?Object.keys(t).forEach((t=>{if(Cr[t]=e,"Noun|Verb"===e){let e=kr(t,jr);Cr[e]="Plural|Verb"}})):Object.keys(t).forEach((t=>{Ar[t]=e}))})),[":(",":)",":P",":p",":O",";(",";)",";P",";p",";O",":3",":|",":/",":\\",":$",":*",":@",":-(",":-)",":-P",":-p",":-O",":-3",":-|",":-/",":-\\",":-$",":-*",":-@",":^(",":^)",":^P",":^p",":^O",":^3",":^|",":^/",":^\\",":^$",":^*",":^@","):","(:","$:","*:",")-:","(-:","$-:","*-:",")^:","(^:","$^:","*^:","<3","</3","<\\3","=("].forEach((e=>Ar[e]="Emoticon")),delete Ar[""],delete Ar.null,delete Ar[" "];const Nr="Singular";var Ir={beforeTags:{Determiner:Nr,Possessive:Nr,Acronym:Nr,Noun:Nr,Adjective:Nr,PresentTense:Nr,Gerund:Nr,PastTense:Nr,Infinitive:Nr,Date:Nr,Ordinal:Nr,Demonym:Nr},afterTags:{Value:Nr,Modal:Nr,Copula:Nr,PresentTense:Nr,PastTense:Nr,Demonym:Nr,Actor:Nr},beforeWords:{the:Nr,with:Nr,without:Nr,of:Nr,for:Nr,any:Nr,all:Nr,on:Nr,cut:Nr,cuts:Nr,increase:Nr,decrease:Nr,raise:Nr,drop:Nr,save:Nr,saved:Nr,saves:Nr,make:Nr,makes:Nr,made:Nr,minus:Nr,plus:Nr,than:Nr,another:Nr,versus:Nr,neither:Nr,about:Nr,favorite:Nr,best:Nr,daily:Nr,weekly:Nr,linear:Nr,binary:Nr,mobile:Nr,lexical:Nr,technical:Nr,computer:Nr,scientific:Nr,security:Nr,government:Nr,popular:Nr,formal:Nr,no:Nr,more:Nr,one:Nr,let:Nr,her:Nr,his:Nr,their:Nr,our:Nr,us:Nr,sheer:Nr,monthly:Nr,yearly:Nr,current:Nr,previous:Nr,upcoming:Nr,last:Nr,next:Nr,main:Nr,initial:Nr,final:Nr,beginning:Nr,end:Nr,top:Nr,bottom:Nr,future:Nr,past:Nr,major:Nr,minor:Nr,side:Nr,central:Nr,peripheral:Nr,public:Nr,private:Nr},afterWords:{of:Nr,system:Nr,aid:Nr,method:Nr,utility:Nr,tool:Nr,reform:Nr,therapy:Nr,philosophy:Nr,room:Nr,authority:Nr,says:Nr,said:Nr,wants:Nr,wanted:Nr,is:Nr,did:Nr,do:Nr,can:Nr,wise:Nr}};const Dr="Infinitive";var Hr={beforeTags:{Modal:Dr,Adverb:Dr,Negative:Dr,Plural:Dr},afterTags:{Determiner:Dr,Adverb:Dr,Possessive:Dr,Reflexive:Dr,Preposition:Dr,Cardinal:Dr,Comparative:Dr,Superlative:Dr},beforeWords:{i:Dr,we:Dr,you:Dr,they:Dr,to:Dr,please:Dr,will:Dr,have:Dr,had:Dr,would:Dr,could:Dr,should:Dr,do:Dr,did:Dr,does:Dr,can:Dr,must:Dr,us:Dr,me:Dr,let:Dr,even:Dr,when:Dr,help:Dr,he:Dr,she:Dr,it:Dr,being:Dr,bi:Dr,co:Dr,contra:Dr,de:Dr,inter:Dr,intra:Dr,mis:Dr,pre:Dr,out:Dr,counter:Dr,nobody:Dr,somebody:Dr,anybody:Dr,everybody:Dr},afterWords:{the:Dr,me:Dr,you:Dr,him:Dr,us:Dr,her:Dr,his:Dr,them:Dr,they:Dr,it:Dr,himself:Dr,herself:Dr,itself:Dr,myself:Dr,ourselves:Dr,themselves:Dr,something:Dr,anything:Dr,a:Dr,an:Dr,up:Dr,down:Dr,by:Dr,out:Dr,off:Dr,under:Dr,what:Dr,all:Dr,to:Dr,because:Dr,although:Dr,how:Dr,otherwise:Dr,together:Dr,though:Dr,into:Dr,yet:Dr,more:Dr,here:Dr,there:Dr,away:Dr}};const Gr={beforeTags:Object.assign({},Hr.beforeTags,Ir.beforeTags,{}),afterTags:Object.assign({},Hr.afterTags,Ir.afterTags,{}),beforeWords:Object.assign({},Hr.beforeWords,Ir.beforeWords,{}),afterWords:Object.assign({},Hr.afterWords,Ir.afterWords,{})},Tr="Adjective";var xr={beforeTags:{Determiner:Tr,Possessive:Tr,Hyphenated:Tr},afterTags:{Adjective:Tr},beforeWords:{seem:Tr,seemed:Tr,seems:Tr,feel:Tr,feels:Tr,felt:Tr,stay:Tr,appear:Tr,appears:Tr,appeared:Tr,also:Tr,over:Tr,under:Tr,too:Tr,it:Tr,but:Tr,still:Tr,really:Tr,quite:Tr,well:Tr,very:Tr,truly:Tr,how:Tr,deeply:Tr,hella:Tr,profoundly:Tr,extremely:Tr,so:Tr,badly:Tr,mostly:Tr,totally:Tr,awfully:Tr,rather:Tr,nothing:Tr,something:Tr,anything:Tr,not:Tr,me:Tr,is:Tr,face:Tr,faces:Tr,faced:Tr,look:Tr,looks:Tr,looked:Tr,reveal:Tr,reveals:Tr,revealed:Tr,sound:Tr,sounded:Tr,sounds:Tr,remains:Tr,remained:Tr,prove:Tr,proves:Tr,proved:Tr,becomes:Tr,stays:Tr,tastes:Tr,taste:Tr,smells:Tr,smell:Tr,gets:Tr,grows:Tr,as:Tr,rings:Tr,radiates:Tr,conveys:Tr,convey:Tr,conveyed:Tr,of:Tr},afterWords:{too:Tr,also:Tr,or:Tr,enough:Tr,as:Tr}};const Er="Gerund";var Fr={beforeTags:{Adverb:Er,Preposition:Er,Conjunction:Er},afterTags:{Adverb:Er,Possessive:Er,Person:Er,Pronoun:Er,Determiner:Er,Copula:Er,Preposition:Er,Conjunction:Er,Comparative:Er},beforeWords:{been:Er,keep:Er,continue:Er,stop:Er,am:Er,be:Er,me:Er,began:Er,start:Er,starts:Er,started:Er,stops:Er,stopped:Er,help:Er,helps:Er,avoid:Er,avoids:Er,love:Er,loves:Er,loved:Er,hate:Er,hates:Er,hated:Er},afterWords:{you:Er,me:Er,her:Er,him:Er,his:Er,them:Er,their:Er,it:Er,this:Er,there:Er,on:Er,about:Er,for:Er,up:Er,down:Er}};const Or="Gerund",zr="Adjective",Vr={beforeTags:Object.assign({},xr.beforeTags,Fr.beforeTags,{Imperative:Or,Infinitive:zr,Plural:Or}),afterTags:Object.assign({},xr.afterTags,Fr.afterTags,{Noun:zr}),beforeWords:Object.assign({},xr.beforeWords,Fr.beforeWords,{is:zr,are:Or,was:zr,of:zr,suggest:Or,suggests:Or,suggested:Or,recommend:Or,recommends:Or,recommended:Or,imagine:Or,imagines:Or,imagined:Or,consider:Or,considered:Or,considering:Or,resist:Or,resists:Or,resisted:Or,avoid:Or,avoided:Or,avoiding:Or,except:zr,accept:zr,assess:Or,explore:Or,fear:Or,fears:Or,appreciate:Or,question:Or,help:Or,embrace:Or,with:zr}),afterWords:Object.assign({},xr.afterWords,Fr.afterWords,{to:Or,not:Or,the:Or})},Br={beforeTags:{Determiner:void 0,Cardinal:"Noun",PhrasalVerb:"Adjective"},afterTags:{}},Sr={beforeTags:Object.assign({},xr.beforeTags,Ir.beforeTags,Br.beforeTags),afterTags:Object.assign({},xr.afterTags,Ir.afterTags,Br.afterTags),beforeWords:Object.assign({},xr.beforeWords,Ir.beforeWords,{are:"Adjective",is:"Adjective",was:"Adjective",be:"Adjective",off:"Adjective",out:"Adjective"}),afterWords:Object.assign({},xr.afterWords,Ir.afterWords)};let Kr="PastTense",$r="Adjective";const Lr={beforeTags:{Adverb:Kr,Pronoun:Kr,ProperNoun:Kr,Auxiliary:Kr,Noun:Kr},afterTags:{Possessive:Kr,Pronoun:Kr,Determiner:Kr,Adverb:Kr,Comparative:Kr,Date:Kr,Gerund:Kr},beforeWords:{be:Kr,who:Kr,get:$r,had:Kr,has:Kr,have:Kr,been:Kr,it:Kr,as:Kr,for:$r,more:$r,always:$r},afterWords:{by:Kr,back:Kr,out:Kr,in:Kr,up:Kr,down:Kr,before:Kr,after:Kr,for:Kr,the:Kr,with:Kr,as:Kr,on:Kr,at:Kr,between:Kr,to:Kr,into:Kr,us:Kr,them:Kr,his:Kr,her:Kr,their:Kr,our:Kr,me:Kr,about:$r}};var Mr={beforeTags:Object.assign({},xr.beforeTags,Lr.beforeTags),afterTags:Object.assign({},xr.afterTags,Lr.afterTags),beforeWords:Object.assign({},xr.beforeWords,Lr.beforeWords),afterWords:Object.assign({},xr.afterWords,Lr.afterWords)};const Jr={afterTags:{Noun:"Adjective",Conjunction:void 0}},Wr={beforeTags:Object.assign({},xr.beforeTags,Hr.beforeTags,{Adverb:void 0,Negative:void 0}),afterTags:Object.assign({},xr.afterTags,Hr.afterTags,Jr.afterTags),beforeWords:Object.assign({},xr.beforeWords,Hr.beforeWords,{have:void 0,had:void 0,not:void 0,went:"Adjective",goes:"Adjective",got:"Adjective",be:"Adjective"}),afterWords:Object.assign({},xr.afterWords,Hr.afterWords,{to:void 0,as:"Adjective"})},Ur={Copula:"Gerund",PastTense:"Gerund",PresentTense:"Gerund",Infinitive:"Gerund"},qr={Value:"Gerund"},Rr={are:"Gerund",were:"Gerund",be:"Gerund",no:"Gerund",without:"Gerund",you:"Gerund",we:"Gerund",they:"Gerund",he:"Gerund",she:"Gerund",us:"Gerund",them:"Gerund"},Qr={the:"Gerund",this:"Gerund",that:"Gerund",me:"Gerund",us:"Gerund",them:"Gerund"},Zr={beforeTags:Object.assign({},Fr.beforeTags,Ir.beforeTags,Ur),afterTags:Object.assign({},Fr.afterTags,Ir.afterTags,qr),beforeWords:Object.assign({},Fr.beforeWords,Ir.beforeWords,Rr),afterWords:Object.assign({},Fr.afterWords,Ir.afterWords,Qr)},Xr="Singular",_r="Infinitive",Yr={beforeTags:Object.assign({},Hr.beforeTags,Ir.beforeTags,{Adjective:Xr,Particle:Xr}),afterTags:Object.assign({},Hr.afterTags,Ir.afterTags,{ProperNoun:_r,Gerund:_r,Adjective:_r,Copula:Xr}),beforeWords:Object.assign({},Hr.beforeWords,Ir.beforeWords,{is:Xr,was:Xr,of:Xr,have:null}),afterWords:Object.assign({},Hr.afterWords,Ir.afterWords,{instead:_r,about:_r,his:_r,her:_r,to:null,by:null,in:null})},eo="Person";var to={beforeTags:{Honorific:eo,Person:eo},afterTags:{Person:eo,ProperNoun:eo,Verb:eo},ownTags:{ProperNoun:eo},beforeWords:{hi:eo,hey:eo,yo:eo,dear:eo,hello:eo},afterWords:{said:eo,says:eo,told:eo,tells:eo,feels:eo,felt:eo,seems:eo,thinks:eo,thought:eo,spends:eo,spendt:eo,plays:eo,played:eo,sing:eo,sang:eo,learn:eo,learned:eo,wants:eo,wanted:eo}};const no="Month",ao={beforeTags:{Date:no,Value:no},afterTags:{Date:no,Value:no},beforeWords:{by:no,in:no,on:no,during:no,after:no,before:no,between:no,until:no,til:no,sometime:no,of:no,this:no,next:no,last:no,previous:no,following:no,with:"Person"},afterWords:{sometime:no,in:no,of:no,until:no,the:no}};var ro={beforeTags:Object.assign({},to.beforeTags,ao.beforeTags),afterTags:Object.assign({},to.afterTags,ao.afterTags),beforeWords:Object.assign({},to.beforeWords,ao.beforeWords),afterWords:Object.assign({},to.afterWords,ao.afterWords)};const oo="Place",io={beforeTags:{Place:oo},afterTags:{Place:oo,Abbreviation:oo},beforeWords:{in:oo,by:oo,near:oo,from:oo,to:oo},afterWords:{in:oo,by:oo,near:oo,from:oo,to:oo,government:oo,council:oo,region:oo,city:oo}};let so="Unit";const lo={"Actor|Verb":Gr,"Adj|Gerund":Vr,"Adj|Noun":Sr,"Adj|Past":Mr,"Adj|Present":Wr,"Noun|Verb":Yr,"Noun|Gerund":Zr,"Person|Noun":{beforeTags:Object.assign({},Ir.beforeTags,to.beforeTags),afterTags:Object.assign({},Ir.afterTags,to.afterTags),beforeWords:Object.assign({},Ir.beforeWords,to.beforeWords,{i:"Infinitive",we:"Infinitive"}),afterWords:Object.assign({},Ir.afterWords,to.afterWords)},"Person|Date":ro,"Person|Verb":{beforeTags:Object.assign({},Ir.beforeTags,to.beforeTags,Hr.beforeTags),afterTags:Object.assign({},Ir.afterTags,to.afterTags,Hr.afterTags),beforeWords:Object.assign({},Ir.beforeWords,to.beforeWords,Hr.beforeWords),afterWords:Object.assign({},Ir.afterWords,to.afterWords,Hr.afterWords)},"Person|Place":{beforeTags:Object.assign({},io.beforeTags,to.beforeTags),afterTags:Object.assign({},io.afterTags,to.afterTags),beforeWords:Object.assign({},io.beforeWords,to.beforeWords),afterWords:Object.assign({},io.afterWords,to.afterWords)},"Person|Adj":{beforeTags:Object.assign({},to.beforeTags,xr.beforeTags),afterTags:Object.assign({},to.afterTags,xr.afterTags),beforeWords:Object.assign({},to.beforeWords,xr.beforeWords),afterWords:Object.assign({},to.afterWords,xr.afterWords)},"Unit|Noun":{beforeTags:{Value:so},afterTags:{},beforeWords:{per:so,every:so,each:so,square:so,cubic:so,sq:so,metric:so},afterWords:{per:so,squared:so,cubed:so,long:so}}},uo=(e,t)=>{let n=Object.keys(e).reduce(((t,n)=>(t[n]="Infinitive"===e[n]?"PresentTense":"Plural",t)),{});return Object.assign(n,t)};lo["Plural|Verb"]={beforeWords:uo(lo["Noun|Verb"].beforeWords,{had:"Plural",have:"Plural"}),afterWords:uo(lo["Noun|Verb"].afterWords,{his:"PresentTense",her:"PresentTense",its:"PresentTense",in:null,to:null,is:"PresentTense",by:"PresentTense"}),beforeTags:uo(lo["Noun|Verb"].beforeTags,{Conjunction:"PresentTense",Noun:void 0,ProperNoun:"PresentTense"}),afterTags:uo(lo["Noun|Verb"].afterTags,{Gerund:"Plural",Noun:"PresentTense",Value:"PresentTense"})};const co="Adjective",ho="Infinitive",go="PresentTense",mo="Singular",po="PastTense",fo="Adverb",bo="Plural",yo="Actor",vo="Verb",wo="Noun",ko="LastName",Po="Modal",Ao="Place",Co="Participle";var jo=[null,null,{ea:mo,ia:wo,ic:co,ly:fo,"'n":vo,"'t":vo},{oed:po,ued:po,xed:po," so":fo,"'ll":Po,"'re":"Copula",azy:co,eer:wo,end:vo,ped:po,ffy:co,ify:ho,ing:"Gerund",ize:ho,ibe:ho,lar:co,mum:co,nes:go,nny:co,ous:co,que:co,ger:wo,ber:wo,rol:mo,sis:mo,ogy:mo,oid:mo,ian:mo,zes:go,eld:po,ken:Co,ven:Co,ten:Co,ect:ho,ict:ho,ign:ho,oze:ho,ful:co,bal:co,ton:wo,pur:Ao},{amed:po,aped:po,ched:po,lked:po,rked:po,reed:po,nded:po,mned:co,cted:po,dged:po,ield:mo,akis:ko,cede:ho,chuk:ko,czyk:ko,ects:go,iend:mo,ends:vo,enko:ko,ette:mo,iary:mo,wner:mo,fies:go,fore:fo,gate:ho,gone:co,ices:bo,ints:bo,ruct:ho,ines:bo,ions:bo,ners:bo,pers:bo,lers:bo,less:co,llen:co,made:co,nsen:ko,oses:go,ould:Po,some:co,sson:ko,ians:bo,tion:mo,tage:wo,ique:mo,tive:co,tors:wo,vice:mo,lier:mo,fier:mo,wned:po,gent:mo,tist:yo,pist:yo,rist:yo,mist:yo,yist:yo,vist:yo,ists:yo,lite:mo,site:mo,rite:mo,mite:mo,bite:mo,mate:mo,date:mo,ndal:mo,vent:mo,uist:yo,gist:yo,note:mo,cide:mo,ence:mo,wide:co,vide:ho,ract:ho,duce:ho,pose:ho,eive:ho,lyze:ho,lyse:ho,iant:co,nary:co,ghty:co,uent:co,erer:yo,bury:Ao,dorf:wo,esty:wo,wych:Ao,dale:Ao,folk:Ao,vale:Ao,abad:Ao,sham:Ao,wick:Ao,view:Ao},{elist:yo,holic:mo,phite:mo,tized:po,urned:po,eased:po,ances:bo,bound:co,ettes:bo,fully:fo,ishes:go,ities:bo,marek:ko,nssen:ko,ology:wo,osome:mo,tment:mo,ports:bo,rough:co,tches:go,tieth:"Ordinal",tures:bo,wards:fo,where:fo,archy:wo,pathy:wo,opoly:wo,embly:wo,phate:wo,ndent:mo,scent:mo,onist:yo,anist:yo,alist:yo,olist:yo,icist:yo,ounce:ho,iable:co,borne:co,gnant:co,inant:co,igent:co,atory:co,rient:mo,dient:mo,maker:yo,burgh:Ao,mouth:Ao,ceter:Ao,ville:Ao,hurst:Ao,stead:Ao,endon:Ao,brook:Ao,shire:Ao,worth:wo,field:"ProperNoun",ridge:Ao},{auskas:ko,parent:mo,cedent:mo,ionary:mo,cklist:mo,brooke:Ao,keeper:yo,logist:yo,teenth:"Value",worker:yo,master:yo,writer:yo,brough:Ao,cester:Ao,ington:Ao,cliffe:Ao,ingham:Ao},{chester:Ao,logists:yo,opoulos:ko,borough:Ao,sdottir:ko}];const No="Adjective",Io="Noun",Do="Verb";var Ho=[null,null,{},{neo:Io,bio:Io,"de-":Do,"re-":Do,"un-":Do,"ex-":Io},{anti:Io,auto:Io,faux:No,hexa:Io,kilo:Io,mono:Io,nano:Io,octa:Io,poly:Io,semi:No,tele:Io,"pro-":No,"mis-":Do,"dis-":Do,"pre-":No},{anglo:Io,centi:Io,ethno:Io,ferro:Io,grand:Io,hepta:Io,hydro:Io,intro:Io,macro:Io,micro:Io,milli:Io,nitro:Io,penta:Io,quasi:No,radio:Io,tetra:Io,"omni-":No,"post-":No},{pseudo:No,"extra-":No,"hyper-":No,"inter-":No,"intra-":No,"deca-":No},{electro:Io}];const Go="Adjective",To="Infinitive",xo="PresentTense",Eo="Singular",Fo="PastTense",Oo="Adverb",zo="Expression",Vo="Actor",Bo="Verb",So="Noun",Ko="LastName";var $o={a:[[/.[aeiou]na$/,So,"tuna"],[/.[oau][wvl]ska$/,Ko],[/.[^aeiou]ica$/,Eo,"harmonica"],[/^([hyj]a+)+$/,zo,"haha"]],c:[[/.[^aeiou]ic$/,Go]],d:[[/[aeiou](pp|ll|ss|ff|gg|tt|rr|bb|nn|mm)ed$/,Fo,"popped"],[/.[aeo]{2}[bdgmnprvz]ed$/,Fo,"rammed"],[/.[aeiou][sg]hed$/,Fo,"gushed"],[/.[aeiou]red$/,Fo,"hired"],[/.[aeiou]r?ried$/,Fo,"hurried"],[/[^aeiou]ard$/,Eo,"steward"],[/[aeiou][^aeiou]id$/,Go,""],[/.[vrl]id$/,Go,"livid"],[/..led$/,Fo,"hurled"],[/.[iao]sed$/,Fo,""],[/[aeiou]n?[cs]ed$/,Fo,""],[/[aeiou][rl]?[mnf]ed$/,Fo,""],[/[aeiou][ns]?c?ked$/,Fo,"bunked"],[/[aeiou]gned$/,Fo],[/[aeiou][nl]?ged$/,Fo],[/.[tdbwxyz]ed$/,Fo],[/[^aeiou][aeiou][tvx]ed$/,Fo],[/.[cdflmnprstv]ied$/,Fo,"emptied"]],e:[[/.[lnr]ize$/,To,"antagonize"],[/.[^aeiou]ise$/,To,"antagonise"],[/.[aeiou]te$/,To,"bite"],[/.[^aeiou][ai]ble$/,Go,"fixable"],[/.[^aeiou]eable$/,Go,"maleable"],[/.[ts]ive$/,Go,"festive"],[/[a-z]-like$/,Go,"woman-like"]],h:[[/.[^aeiouf]ish$/,Go,"cornish"],[/.v[iy]ch$/,Ko,"..ovich"],[/^ug?h+$/,zo,"ughh"],[/^uh[ -]?oh$/,zo,"uhoh"],[/[a-z]-ish$/,Go,"cartoon-ish"]],i:[[/.[oau][wvl]ski$/,Ko,"polish-male"]],k:[[/^(k){2}$/,zo,"kkkk"]],l:[[/.[gl]ial$/,Go,"familial"],[/.[^aeiou]ful$/,Go,"fitful"],[/.[nrtumcd]al$/,Go,"natal"],[/.[^aeiou][ei]al$/,Go,"familial"]],m:[[/.[^aeiou]ium$/,Eo,"magnesium"],[/[^aeiou]ism$/,Eo,"schism"],[/^[hu]m+$/,zo,"hmm"],[/^\d+ ?[ap]m$/,"Date","3am"]],n:[[/.[lsrnpb]ian$/,Go,"republican"],[/[^aeiou]ician$/,Vo,"musician"],[/[aeiou][ktrp]in'$/,"Gerund","cookin'"]],o:[[/^no+$/,zo,"noooo"],[/^(yo)+$/,zo,"yoo"],[/^wo{2,}[pt]?$/,zo,"woop"]],r:[[/.[bdfklmst]ler$/,"Noun"],[/[aeiou][pns]er$/,Eo],[/[^i]fer$/,To],[/.[^aeiou][ao]pher$/,Vo],[/.[lk]er$/,"Noun"],[/.ier$/,"Comparative"]],t:[[/.[di]est$/,"Superlative"],[/.[icldtgrv]ent$/,Go],[/[aeiou].*ist$/,Go],[/^[a-z]et$/,Bo]],s:[[/.[^aeiou]ises$/,xo],[/.[rln]ates$/,xo],[/.[^z]ens$/,Bo],[/.[lstrn]us$/,Eo],[/.[aeiou]sks$/,xo],[/.[aeiou]kes$/,xo],[/[aeiou][^aeiou]is$/,Eo],[/[a-z]'s$/,So],[/^yes+$/,zo]],v:[[/.[^aeiou][ai][kln]ov$/,Ko]],y:[[/.[cts]hy$/,Go],[/.[st]ty$/,Go],[/.[tnl]ary$/,Go],[/.[oe]ry$/,Eo],[/[rdntkbhs]ly$/,Oo],[/.(gg|bb|zz)ly$/,Go],[/...lly$/,Oo],[/.[gk]y$/,Go],[/[bszmp]{2}y$/,Go],[/.[ai]my$/,Go],[/[ea]{2}zy$/,Go],[/.[^aeiou]ity$/,Eo]]};const Lo="Verb",Mo="Noun";var Jo={leftTags:[["Adjective",Mo],["Possessive",Mo],["Determiner",Mo],["Adverb",Lo],["Pronoun",Lo],["Value",Mo],["Ordinal",Mo],["Modal",Lo],["Superlative",Mo],["Demonym",Mo],["Honorific","Person"]],leftWords:[["i",Lo],["first",Mo],["it",Lo],["there",Lo],["not",Lo],["because",Mo],["if",Mo],["but",Mo],["who",Lo],["this",Mo],["his",Mo],["when",Mo],["you",Lo],["very","Adjective"],["old",Mo],["never",Lo],["before",Mo],["a",Mo],["the",Mo],["been",Lo]],rightTags:[["Copula",Mo],["PastTense",Mo],["Conjunction",Mo],["Modal",Mo]],rightWords:[["there",Lo],["me",Lo],["man","Adjective"],["him",Lo],["it",Lo],["were",Mo],["took",Mo],["himself",Lo],["went",Mo],["who",Mo],["jr","Person"]]},Wo={fwd:"3:ser,ier¦1er:h,t,f,l,n¦1r:e¦2er:ss,or,om",both:"3er:ver,ear,alm¦3ner:hin¦3ter:lat¦2mer:im¦2er:ng,rm,mb¦2ber:ib¦2ger:ig¦1er:w,p,k,d¦ier:y",rev:"1:tter,yer¦2:uer,ver,ffer,oner,eler,ller,iler,ster,cer,uler,sher,ener,gher,aner,adder,nter,eter,rter,hter,rner,fter¦3:oser,ooler,eafer,user,airer,bler,maler,tler,eater,uger,rger,ainer,urer,ealer,icher,pler,emner,icter,nser,iser¦4:arser,viner,ucher,rosser,somer,ndomer,moter,oother,uarer,hiter¦5:nuiner,esser,emier¦ar:urther",ex:"worse:bad¦better:good¦4er:fair,gray,poor¦1urther:far¦3ter:fat,hot,wet¦3der:mad,sad¦3er:shy,fun¦4der:glad¦:¦4r:cute,dire,fake,fine,free,lame,late,pale,rare,ripe,rude,safe,sore,tame,wide¦5r:eerie,stale"},Uo={fwd:"1:nning,tting,rring,pping,eing,mming,gging,dding,bbing,kking¦2:eking,oling,eling,eming¦3:velling,siting,uiting,fiting,loting,geting,ialing,celling¦4:graming",both:"1:aing,iing,fing,xing,ying,oing,hing,wing¦2:tzing,rping,izzing,bting,mning,sping,wling,rling,wding,rbing,uping,lming,wning,mping,oning,lting,mbing,lking,fting,hting,sking,gning,pting,cking,ening,nking,iling,eping,ering,rting,rming,cting,lping,ssing,nting,nding,lding,sting,rning,rding,rking¦3:belling,siping,toming,yaking,uaking,oaning,auling,ooping,aiding,naping,euring,tolling,uzzing,ganing,haning,ualing,halling,iasing,auding,ieting,ceting,ouling,voring,ralling,garing,joring,oaming,oaking,roring,nelling,ooring,uelling,eaming,ooding,eaping,eeting,ooting,ooming,xiting,keting,ooking,ulling,airing,oaring,biting,outing,oiting,earing,naling,oading,eeding,ouring,eaking,aiming,illing,oining,eaning,onging,ealing,aining,eading¦4:thoming,melling,aboring,ivoting,weating,dfilling,onoring,eriting,imiting,tialling,rgining,otoring,linging,winging,lleting,louding,spelling,mpelling,heating,feating,opelling,choring,welling,ymaking,ctoring,calling,peating,iloring,laiting,utoring,uditing,mmaking,loating,iciting,waiting,mbating,voiding,otalling,nsoring,nselling,ocusing,itoring,eloping¦5:rselling,umpeting,atrolling,treating,tselling,rpreting,pringing,ummeting,ossoming,elmaking,eselling,rediting,totyping,onmaking,rfeiting,ntrolling¦5e:chmaking,dkeeping,severing,erouting,ecreting,ephoning,uthoring,ravening,reathing,pediting,erfering,eotyping,fringing,entoring,ombining,ompeting¦4e:emaking,eething,twining,rruling,chuting,xciting,rseding,scoping,edoring,pinging,lunging,agining,craping,pleting,eleting,nciting,nfining,ncoding,tponing,ecoding,writing,esaling,nvening,gnoring,evoting,mpeding,rvening,dhering,mpiling,storing,nviting,ploring¦3e:tining,nuring,saking,miring,haling,ceding,xuding,rining,nuting,laring,caring,miling,riding,hoking,piring,lading,curing,uading,noting,taping,futing,paring,hading,loding,siring,guring,vading,voking,during,niting,laning,caping,luting,muting,ruding,ciding,juring,laming,caling,hining,uoting,liding,ciling,duling,tuting,puting,cuting,coring,uiding,tiring,turing,siding,rading,enging,haping,buting,lining,taking,anging,haring,uiring,coming,mining,moting,suring,viding,luding¦2e:tring,zling,uging,oging,gling,iging,vring,fling,lging,obing,psing,pling,ubing,cling,dling,wsing,iking,rsing,dging,kling,ysing,tling,rging,eging,nsing,uning,osing,uming,using,ibing,bling,aging,ising,asing,ating¦2ie:rlying¦1e:zing,uing,cing,ving",rev:"ying:ie¦1ing:se,ke,te,we,ne,re,de,pe,me,le,c,he¦2ing:ll,ng,dd,ee,ye,oe,rg,us¦2ning:un¦2ging:og,ag,ug,ig,eg¦2ming:um¦2bing:ub,ab,eb,ob¦3ning:lan,can,hin,pin,win¦3ring:cur,lur,tir,tar,pur,car¦3ing:ait,del,eel,fin,eat,oat,eem,lel,ool,ein,uin¦3ping:rop,rap,top,uip,wap,hip,hop,lap,rip,cap¦3ming:tem,wim,rim,kim,lim¦3ting:mat,cut,pot,lit,lot,hat,set,pit,put¦3ding:hed,bed,bid¦3king:rek¦3ling:cil,pel¦3bing:rib¦4ning:egin¦4ing:isit,ruit,ilot,nsit,dget,rkel,ival,rcel¦4ring:efer,nfer¦4ting:rmit,mmit,ysit,dmit,emit,bmit,tfit,gret¦4ling:evel,xcel,ivel¦4ding:hred¦5ing:arget,posit,rofit¦5ring:nsfer¦5ting:nsmit,orget,cquit¦5ling:ancel,istil",ex:"3:adding,eating,aiming,aiding,airing,outing,gassing,setting,getting,putting,cutting,winning,sitting,betting,mapping,tapping,letting,bidding,hitting,tanning,netting,popping,fitting,capping,lapping,barring,banning,vetting,topping,rotting,tipping,potting,wetting,pitting,dipping,budding,hemming,pinning,jetting,kidding,padding,podding,sipping,wedding,bedding,donning,warring,penning,gutting,cueing,wadding,petting,ripping,napping,matting,tinning,binning,dimming,hopping,mopping,nodding,panning,rapping,ridding,sinning¦4:selling,falling,calling,waiting,editing,telling,rolling,heating,boating,hanging,beating,coating,singing,tolling,felling,polling,discing,seating,voiding,gelling,yelling,baiting,reining,ruining,seeking,spanning,stepping,knitting,emitting,slipping,quitting,dialing,omitting,clipping,shutting,skinning,abutting,flipping,trotting,cramming,fretting,suiting¦5:bringing,treating,spelling,stalling,trolling,expelling,rivaling,wringing,deterring,singeing,befitting,refitting¦6:enrolling,distilling,scrolling,strolling,caucusing,travelling¦7:installing,redefining,stencilling,recharging,overeating,benefiting,unraveling,programing¦9:reprogramming¦is:being¦2e:using,aging,owing¦3e:making,taking,coming,noting,hiring,filing,coding,citing,doping,baking,coping,hoping,lading,caring,naming,voting,riding,mining,curing,lining,ruling,typing,boring,dining,firing,hiding,piling,taping,waning,baling,boning,faring,honing,wiping,luring,timing,wading,piping,fading,biting,zoning,daring,waking,gaming,raking,ceding,tiring,coking,wining,joking,paring,gaping,poking,pining,coring,liming,toting,roping,wiring,aching¦4e:writing,storing,eroding,framing,smoking,tasting,wasting,phoning,shaking,abiding,braking,flaking,pasting,priming,shoring,sloping,withing,hinging¦5e:defining,refining,renaming,swathing,fringing,reciting¦1ie:dying,tying,lying,vying¦7e:sunbathing"},qo={fwd:"1:mt¦2:llen¦3:iven,aken¦:ne¦y:in",both:"1:wn¦2:me,aten¦3:seen,bidden,isen¦4:roven,asten¦3l:pilt¦3d:uilt¦2e:itten¦1im:wum¦1eak:poken¦1ine:hone¦1ose:osen¦1in:gun¦1ake:woken¦ear:orn¦eal:olen¦eeze:ozen¦et:otten¦ink:unk¦ing:ung",rev:"2:un¦oken:eak¦ought:eek¦oven:eave¦1ne:o¦1own:ly¦1den:de¦1in:ay¦2t:am¦2n:ee¦3en:all¦4n:rive,sake,take¦5n:rgive",ex:"2:been¦3:seen,run¦4:given,taken¦5:shaken¦2eak:broken¦1ive:dove¦2y:flown¦3e:hidden,ridden¦1eek:sought¦1ake:woken¦1eave:woven"},Ro={fwd:"1:oes¦1ve:as",both:"1:xes¦2:zzes,ches,shes,sses¦3:iases¦2y:llies,plies¦1y:cies,bies,ties,vies,nies,pies,dies,ries,fies¦:s",rev:"1ies:ly¦2es:us,go,do¦3es:cho,eto",ex:"2:does,goes¦3:gasses¦5:focuses¦is:are¦3y:relies¦2y:flies¦2ve:has"},Qo={fwd:"1st:e¦1est:l,m,f,s¦1iest:cey¦2est:or,ir¦3est:ver",both:"4:east¦5:hwest¦5lest:erful¦4est:weet,lgar,tter,oung¦4most:uter¦3est:ger,der,rey,iet,ong,ear¦3test:lat¦3most:ner¦2est:pt,ft,nt,ct,rt,ht¦2test:it¦2gest:ig¦1est:b,k,n,p,h,d,w¦iest:y",rev:"1:ttest,nnest,yest¦2:sest,stest,rmest,cest,vest,lmest,olest,ilest,ulest,ssest,imest,uest¦3:rgest,eatest,oorest,plest,allest,urest,iefest,uelest,blest,ugest,amest,yalest,ealest,illest,tlest,itest¦4:cerest,eriest,somest,rmalest,ndomest,motest,uarest,tiffest¦5:leverest,rangest¦ar:urthest¦3ey:riciest",ex:"best:good¦worst:bad¦5est:great¦4est:fast,full,fair,dull¦3test:hot,wet,fat¦4nest:thin¦1urthest:far¦3est:gay,shy,ill¦4test:neat¦4st:late,wide,fine,safe,cute,fake,pale,rare,rude,sore,ripe,dire¦6st:severe"},Zo={fwd:"1:tistic,eable,lful,sful,ting,tty¦2:onate,rtable,geous,ced,seful,ctful¦3:ortive,ented¦arity:ear¦y:etic¦fulness:begone¦1ity:re¦1y:tiful,gic¦2ity:ile,imous,ilous,ime¦2ion:ated¦2eness:iving¦2y:trious¦2ation:iring¦2tion:vant¦3ion:ect¦3ce:mant,mantic¦3tion:irable¦3y:est,estic¦3m:mistic,listic¦3ess:ning¦4n:utious¦4on:rative,native,vative,ective¦4ce:erant",both:"1:king,wing¦2:alous,ltuous,oyful,rdous¦3:gorous,ectable,werful,amatic¦4:oised,usical,agical,raceful,ocused,lined,ightful¦5ness:stful,lding,itous,nuous,ulous,otous,nable,gious,ayful,rvous,ntous,lsive,peful,entle,ciful,osive,leful,isive,ncise,reful,mious¦5ty:ivacious¦5ties:ubtle¦5ce:ilient,adiant,atient¦5cy:icient¦5sm:gmatic¦5on:sessive,dictive¦5ity:pular,sonal,eative,entic¦5sity:uminous¦5ism:conic¦5nce:mperate¦5ility:mitable¦5ment:xcited¦5n:bitious¦4cy:brant,etent,curate¦4ility:erable,acable,icable,ptable¦4ty:nacious,aive,oyal,dacious¦4n:icious¦4ce:vient,erent,stent,ndent,dient,quent,ident¦4ness:adic,ound,hing,pant,sant,oing,oist,tute¦4icity:imple¦4ment:fined,mused¦4ism:otic¦4ry:dantic¦4ity:tund,eral¦4edness:hand¦4on:uitive¦4lity:pitable¦4sm:eroic,namic¦4sity:nerous¦3th:arm¦3ility:pable,bable,dable,iable¦3cy:hant,nant,icate¦3ness:red,hin,nse,ict,iet,ite,oud,ind,ied,rce¦3ion:lute¦3ity:ual,gal,volous,ial¦3ce:sent,fensive,lant,gant,gent,lent,dant¦3on:asive¦3m:fist,sistic,iastic¦3y:terious,xurious,ronic,tastic¦3ur:amorous¦3e:tunate¦3ation:mined¦3sy:rteous¦3ty:ain¦3ry:ave¦3ment:azed¦2ness:de,on,ue,rn,ur,ft,rp,pe,om,ge,rd,od,ay,ss,er,ll,oy,ap,ht,ld,ad,rt¦2inousness:umous¦2ity:neous,ene,id,ane¦2cy:bate,late¦2ation:ized¦2ility:oble,ible¦2y:odic¦2e:oving,aring¦2s:ost¦2itude:pt¦2dom:ee¦2ance:uring¦2tion:reet¦2ion:oted¦2sion:ending¦2liness:an¦2or:rdent¦1th:ung¦1e:uable¦1ness:w,h,k,f¦1ility:mble¦1or:vent¦1ement:ging¦1tiquity:ncient¦1ment:hed¦verty:or¦ength:ong¦eat:ot¦pth:ep¦iness:y",rev:"",ex:"5:forceful,humorous¦8:charismatic¦13:understanding¦5ity:active¦11ness:adventurous,inquisitive,resourceful¦8on:aggressive,automatic,perceptive¦7ness:amorous,fatuous,furtive,ominous,serious¦5ness:ample,sweet¦12ness:apprehensive,cantankerous,contemptuous,ostentatious¦13ness:argumentative,conscientious¦9ness:assertive,facetious,imperious,inventive,oblivious,rapacious,receptive,seditious,whimsical¦10ness:attractive,expressive,impressive,loquacious,salubrious,thoughtful¦3edom:boring¦4ness:calm,fast,keen,tame¦8ness:cheerful,gracious,specious,spurious,timorous,unctuous¦5sity:curious¦9ion:deliberate¦8ion:desperate¦6e:expensive¦7ce:fragrant¦3y:furious¦9ility:ineluctable¦6ism:mystical¦8ity:physical,proactive,sensitive,vertical¦5cy:pliant¦7ity:positive¦9ity:practical¦12ism:professional¦6ce:prudent¦3ness:red¦6cy:vagrant¦3dom:wise"};const Xo=function(e="",t={}){let n=function(e,t={}){return t.hasOwnProperty(e)?t[e]:null}(e,t.ex);return n=n||function(e,t=[]){for(let n=0;n<t.length;n+=1)if(e.endsWith(t[n]))return e;return null}(e,t.same),n=n||function(e,t,n={}){t=t||{};for(let a=e.length-1;a>=1;a-=1){let r=e.length-a,o=e.substring(r,e.length);if(!0===t.hasOwnProperty(o))return e.slice(0,r)+t[o];if(!0===n.hasOwnProperty(o))return e.slice(0,r)+n[o]}return t.hasOwnProperty("")?e+t[""]:n.hasOwnProperty("")?e+n[""]:null}(e,t.fwd,t.both),n=n||e,n},_o=function(e){return Object.entries(e).reduce(((e,t)=>(e[t[1]]=t[0],e)),{})},Yo=function(e={}){return{reversed:!0,both:_o(e.both),ex:_o(e.ex),fwd:e.rev||{}}},ei=/^([0-9]+)/,ti=function(e){let t=function(e){let t={};return e.split("¦").forEach((e=>{let[n,a]=e.split(":");a=(a||"").split(","),a.forEach((e=>{t[e]=n}))})),t}(e);return Object.keys(t).reduce(((e,n)=>(e[n]=function(e="",t=""){let n=(t=String(t)).match(ei);if(null===n)return t;let a=Number(n[1])||0;return e.substring(0,a)+t.replace(ei,"")}(n,t[n]),e)),{})},ni=function(e={}){return"string"==typeof e&&(e=JSON.parse(e)),e.fwd=ti(e.fwd||""),e.both=ti(e.both||""),e.rev=ti(e.rev||""),e.ex=ti(e.ex||""),e},ai=ni({fwd:"1:tted,wed,gged,nned,een,rred,pped,yed,bbed,oed,dded,rd,wn,mmed¦2:eed,nded,et,hted,st,oled,ut,emed,eled,lded,ken,rt,nked,apt,ant,eped,eked¦3:eared,eat,eaded,nelled,ealt,eeded,ooted,eaked,eaned,eeted,mited,bid,uit,ead,uited,ealed,geted,velled,ialed,belled¦4:ebuted,hined,comed¦y:ied¦ome:ame¦ear:ore¦ind:ound¦ing:ung,ang¦ep:pt¦ink:ank,unk¦ig:ug¦all:ell¦ee:aw¦ive:ave¦eeze:oze¦old:eld¦ave:ft¦ake:ook¦ell:old¦ite:ote¦ide:ode¦ine:one¦in:un,on¦eal:ole¦im:am¦ie:ay¦and:ood¦1ise:rose¦1eak:roke¦1ing:rought¦1ive:rove¦1el:elt¦1id:bade¦1et:got¦1y:aid¦1it:sat¦3e:lid¦3d:pent",both:"1:aed,fed,xed,hed¦2:sged,xted,wled,rped,lked,kied,lmed,lped,uped,bted,rbed,rked,wned,rled,mped,fted,mned,mbed,zzed,omed,ened,cked,gned,lted,sked,ued,zed,nted,ered,rted,rmed,ced,sted,rned,ssed,rded,pted,ved,cted¦3:cled,eined,siped,ooned,uked,ymed,jored,ouded,ioted,oaned,lged,asped,iged,mured,oided,eiled,yped,taled,moned,yled,lit,kled,oaked,gled,naled,fled,uined,oared,valled,koned,soned,aided,obed,ibed,meted,nicked,rored,micked,keted,vred,ooped,oaded,rited,aired,auled,filled,ouled,ooded,ceted,tolled,oited,bited,aped,tled,vored,dled,eamed,nsed,rsed,sited,owded,pled,sored,rged,osed,pelled,oured,psed,oated,loned,aimed,illed,eured,tred,ioned,celled,bled,wsed,ooked,oiled,itzed,iked,iased,onged,ased,ailed,uned,umed,ained,auded,nulled,ysed,eged,ised,aged,oined,ated,used,dged,doned¦4:ntied,efited,uaked,caded,fired,roped,halled,roked,himed,culed,tared,lared,tuted,uared,routed,pited,naked,miled,houted,helled,hared,cored,caled,tired,peated,futed,ciled,called,tined,moted,filed,sided,poned,iloted,honed,lleted,huted,ruled,cured,named,preted,vaded,sured,talled,haled,peded,gined,nited,uided,ramed,feited,laked,gured,ctored,unged,pired,cuted,voked,eloped,ralled,rined,coded,icited,vided,uaded,voted,mined,sired,noted,lined,nselled,luted,jured,fided,puted,piled,pared,olored,cided,hoked,enged,tured,geoned,cotted,lamed,uiled,waited,udited,anged,luded,mired,uired,raded¦5:modelled,izzled,eleted,umpeted,ailored,rseded,treated,eduled,ecited,rammed,eceded,atrolled,nitored,basted,twined,itialled,ncited,gnored,ploded,xcited,nrolled,namelled,plored,efeated,redited,ntrolled,nfined,pleted,llided,lcined,eathed,ibuted,lloted,dhered,cceded¦3ad:sled¦2aw:drew¦2ot:hot¦2ke:made¦2ow:hrew,grew¦2ose:hose¦2d:ilt¦2in:egan¦1un:ran¦1ink:hought¦1ick:tuck¦1ike:ruck¦1eak:poke,nuck¦1it:pat¦1o:did¦1ow:new¦1ake:woke¦go:went",rev:"3:rst,hed,hut,cut,set¦4:tbid¦5:dcast,eread,pread,erbid¦ought:uy,eek¦1ied:ny,ly,dy,ry,fy,py,vy,by,ty,cy¦1ung:ling,ting,wing¦1pt:eep¦1ank:rink¦1ore:bear,wear¦1ave:give¦1oze:reeze¦1ound:rind,wind¦1ook:take,hake¦1aw:see¦1old:sell¦1ote:rite¦1ole:teal¦1unk:tink¦1am:wim¦1ay:lie¦1ood:tand¦1eld:hold¦2d:he,ge,re,le,leed,ne,reed,be,ye,lee,pe,we¦2ed:dd,oy,or,ey,gg,rr,us,ew,to¦2ame:ecome,rcome¦2ped:ap¦2ged:ag,og,ug,eg¦2bed:ub,ab,ib,ob¦2lt:neel¦2id:pay¦2ang:pring¦2ove:trive¦2med:um¦2ode:rride¦2at:ysit¦3ted:mit,hat,mat,lat,pot,rot,bat¦3ed:low,end,tow,und,ond,eem,lay,cho,dow,xit,eld,ald,uld,law,lel,eat,oll,ray,ank,fin,oam,out,how,iek,tay,haw,ait,vet,say,cay,bow¦3d:ste,ede,ode,ete,ree,ude,ame,oke,ote,ime,ute,ade¦3red:lur,cur,pur,car¦3ped:hop,rop,uip,rip,lip,tep,top¦3ded:bed,rod,kid¦3ade:orbid¦3led:uel¦3ned:lan,can,kin,pan,tun¦3med:rim,lim¦4ted:quit,llot¦4ed:pear,rrow,rand,lean,mand,anel,pand,reet,link,abel,evel,imit,ceed,ruit,mind,peal,veal,hool,head,pell,well,mell,uell,band,hear,weak¦4led:nnel,qual,ebel,ivel¦4red:nfer,efer,sfer¦4n:sake,trew¦4d:ntee¦4ded:hred¦4ned:rpin¦5ed:light,nceal,right,ndear,arget,hread,eight,rtial,eboot¦5d:edite,nvite¦5ted:egret¦5led:ravel",ex:"2:been,upped¦3:added,aged,aided,aimed,aired,bid,died,dyed,egged,erred,eyed,fit,gassed,hit,lied,owed,pent,pied,tied,used,vied,oiled,outed,banned,barred,bet,canned,cut,dipped,donned,ended,feed,inked,jarred,let,manned,mowed,netted,padded,panned,pitted,popped,potted,put,set,sewn,sowed,tanned,tipped,topped,vowed,weed,bowed,jammed,binned,dimmed,hopped,mopped,nodded,pinned,rigged,sinned,towed,vetted¦4:ached,baked,baled,boned,bored,called,caned,cared,ceded,cited,coded,cored,cubed,cured,dared,dined,edited,exited,faked,fared,filed,fined,fired,fuelled,gamed,gelled,hired,hoped,joked,lined,mined,named,noted,piled,poked,polled,pored,pulled,reaped,roamed,rolled,ruled,seated,shed,sided,timed,tolled,toned,voted,waited,walled,waned,winged,wiped,wired,zoned,yelled,tamed,lubed,roped,faded,mired,caked,honed,banged,culled,heated,raked,welled,banded,beat,cast,cooled,cost,dealt,feared,folded,footed,handed,headed,heard,hurt,knitted,landed,leaked,leapt,linked,meant,minded,molded,neared,needed,peaked,plodded,plotted,pooled,quit,read,rooted,sealed,seeded,seeped,shipped,shunned,skimmed,slammed,sparred,stemmed,stirred,suited,thinned,twinned,swayed,winked,dialed,abutted,blotted,fretted,healed,heeded,peeled,reeled¦5:basted,cheated,equalled,eroded,exiled,focused,opined,pleated,primed,quoted,scouted,shored,sloped,smoked,sniped,spelled,spouted,routed,staked,stored,swelled,tasted,treated,wasted,smelled,dwelled,honored,prided,quelled,eloped,scared,coveted,sweated,breaded,cleared,debuted,deterred,freaked,modeled,pleaded,rebutted,speeded¦6:anchored,defined,endured,impaled,invited,refined,revered,strolled,cringed,recast,thrust,unfolded¦7:authored,combined,competed,conceded,convened,excreted,extruded,redefined,restored,secreted,rescinded,welcomed¦8:expedited,infringed¦9:interfered,intervened,persevered¦10:contravened¦eat:ate¦is:was¦go:went¦are:were¦3d:bent,lent,rent,sent¦3e:bit,fled,hid,lost¦3ed:bled,bred¦2ow:blew,grew¦1uy:bought¦2tch:caught¦1o:did¦1ive:dove,gave¦2aw:drew¦2ed:fed¦2y:flew,laid,paid,said¦1ight:fought¦1et:got¦2ve:had¦1ang:hung¦2ad:led¦2ght:lit¦2ke:made¦2et:met¦1un:ran¦1ise:rose¦1it:sat¦1eek:sought¦1each:taught¦1ake:woke,took¦1eave:wove¦2ise:arose¦1ear:bore,tore,wore¦1ind:bound,found,wound¦2eak:broke¦2ing:brought,wrung¦1ome:came¦2ive:drove¦1ig:dug¦1all:fell¦2el:felt¦4et:forgot¦1old:held¦2ave:left¦1ing:rang,sang¦1ide:rode¦1ink:sank¦1ee:saw¦2ine:shone¦4e:slid¦1ell:sold,told¦4d:spent¦2in:spun¦1in:won"}),ri=ni(Ro),oi=ni(Uo),ii=ni(qo),si=Yo(ai),li=Yo(ri),ui=Yo(oi),ci=Yo(ii),di=ni(Wo),hi=ni(Qo);var gi={fromPast:ai,fromPresent:ri,fromGerund:oi,fromParticiple:ii,toPast:si,toPresent:li,toGerund:ui,toParticiple:ci,toComparative:di,toSuperlative:hi,fromComparative:Yo(di),fromSuperlative:Yo(hi),adjToNoun:ni(Zo)},mi=["academy","administration","agence","agences","agencies","agency","airlines","airways","army","assoc","associates","association","assurance","authority","autorite","aviation","bank","banque","board","boys","brands","brewery","brotherhood","brothers","bureau","cafe","co","caisse","capital","care","cathedral","center","centre","chemicals","choir","chronicle","church","circus","clinic","clinique","club","co","coalition","coffee","collective","college","commission","committee","communications","community","company","comprehensive","computers","confederation","conference","conseil","consulting","containers","corporation","corps","corp","council","crew","data","departement","department","departments","design","development","directorate","division","drilling","education","eglise","electric","electricity","energy","ensemble","enterprise","enterprises","entertainment","estate","etat","faculty","faction","federation","financial","fm","foundation","fund","gas","gazette","girls","government","group","guild","herald","holdings","hospital","hotel","hotels","inc","industries","institut","institute","institutes","insurance","international","interstate","investment","investments","investors","journal","laboratory","labs","llc","ltd","limited","machines","magazine","management","marine","marketing","markets","media","memorial","ministere","ministry","military","mobile","motor","motors","musee","museum","news","observatory","office","oil","optical","orchestra","organization","partners","partnership","petrol","petroleum","pharmacare","pharmaceutical","pharmaceuticals","pizza","plc","police","politburo","polytechnic","post","power","press","productions","quartet","radio","reserve","resources","restaurant","restaurants","savings","school","securities","service","services","societe","subsidiary","society","sons","subcommittee","syndicat","systems","telecommunications","telegraph","television","times","tribunal","tv","union","university","utilities","workers"].reduce(((e,t)=>(e[t]=!0,e)),{}),pi=["atoll","basin","bay","beach","bluff","bog","camp","canyon","canyons","cape","cave","caves","cliffs","coast","cove","coves","crater","crossing","creek","desert","dune","dunes","downs","estates","escarpment","estuary","falls","fjord","fjords","forest","forests","glacier","gorge","gorges","grove","gulf","gully","highland","heights","hollow","hill","hills","inlet","island","islands","isthmus","junction","knoll","lagoon","lake","lakeshore","marsh","marshes","mount","mountain","mountains","narrows","peninsula","plains","plateau","pond","rapids","ravine","reef","reefs","ridge","river","rivers","sandhill","shoal","shore","shoreline","shores","strait","straits","springs","stream","swamp","tombolo","trail","trails","trench","valley","vallies","village","volcano","waterfall","watershed","wetland","woods","acres","burough","county","district","municipality","prefecture","province","region","reservation","state","territory","borough","metropolis","downtown","uptown","midtown","city","town","township","hamlet","country","kingdom","enclave","neighbourhood","neighborhood","kingdom","ward","zone","airport","amphitheater","arch","arena","auditorium","bar","barn","basilica","battlefield","bridge","building","castle","centre","coliseum","cineplex","complex","dam","farm","field","fort","garden","gardens","gymnasium","hall","house","levee","library","manor","memorial","monument","museum","gallery","palace","pillar","pits","plantation","playhouse","quarry","sportsfield","sportsplex","stadium","terrace","terraces","theater","tower","park","parks","site","ranch","raceway","sportsplex","ave","st","street","rd","road","lane","landing","crescent","cr","way","tr","terrace","avenue"].reduce(((e,t)=>(e[t]=!0,e)),{}),fi=[[/([^v])ies$/i,"$1y"],[/(ise)s$/i,"$1"],[/(kn|[^o]l|w)ives$/i,"$1ife"],[/^((?:ca|e|ha|(?:our|them|your)?se|she|wo)l|lea|loa|shea|thie)ves$/i,"$1f"],[/^(dwar|handkerchie|hoo|scar|whar)ves$/i,"$1f"],[/(antenn|formul|nebul|vertebr|vit)ae$/i,"$1a"],[/(octop|vir|radi|nucle|fung|cact|stimul)(i)$/i,"$1us"],[/(buffal|tomat|tornad)(oes)$/i,"$1o"],[/(ause)s$/i,"$1"],[/(ease)s$/i,"$1"],[/(ious)es$/i,"$1"],[/(ouse)s$/i,"$1"],[/(ose)s$/i,"$1"],[/(..ase)s$/i,"$1"],[/(..[aeiu]s)es$/i,"$1"],[/(vert|ind|cort)(ices)$/i,"$1ex"],[/(matr|append)(ices)$/i,"$1ix"],[/([xo]|ch|ss|sh)es$/i,"$1"],[/men$/i,"man"],[/(n)ews$/i,"$1ews"],[/([ti])a$/i,"$1um"],[/([^aeiouy]|qu)ies$/i,"$1y"],[/(s)eries$/i,"$1eries"],[/(m)ovies$/i,"$1ovie"],[/(cris|ax|test)es$/i,"$1is"],[/(alias|status)es$/i,"$1"],[/(ss)$/i,"$1"],[/(ic)s$/i,"$1"],[/s$/i,""]];const bi=function(e,t){const{irregularPlurals:n}=t.two;let a=(r=n,Object.keys(r).reduce(((e,t)=>(e[r[t]]=t,e)),{}));var r;if(a.hasOwnProperty(e))return a[e];for(let t=0;t<fi.length;t++)if(!0===fi[t][0].test(e))return e=e.replace(fi[t][0],fi[t][1]);return e};var yi={toPlural:kr,toSingular:bi,all:function(e,t){let n=[e],a=kr(e,t);a!==e&&n.push(a);let r=bi(e,t);return r!==e&&n.push(r),n}};let vi={Gerund:["ing"],Actor:["erer"],Infinitive:["ate","ize","tion","rify","then","ress","ify","age","nce","ect","ise","ine","ish","ace","ash","ure","tch","end","ack","and","ute","ade","ock","ite","ase","ose","use","ive","int","nge","lay","est","ain","ant","ent","eed","er","le","unk","ung","upt","en"],PastTense:["ept","ed","lt","nt","ew","ld"],PresentTense:["rks","cks","nks","ngs","mps","tes","zes","ers","les","acks","ends","ands","ocks","lays","eads","lls","els","ils","ows","nds","ays","ams","ars","ops","ffs","als","urs","lds","ews","ips","es","ts","ns"],Participle:["ken","wn"]};vi=Object.keys(vi).reduce(((e,t)=>(vi[t].forEach((n=>e[n]=t)),e)),{});const wi=function(e){let t=e.substring(e.length-3);if(!0===vi.hasOwnProperty(t))return vi[t];let n=e.substring(e.length-2);return!0===vi.hasOwnProperty(n)?vi[n]:"s"===e.substring(e.length-1)?"PresentTense":null},ki={are:"be",were:"be",been:"be",is:"be",am:"be",was:"be",be:"be",being:"be"},Pi=function(e,t,n){const{fromPast:a,fromPresent:r,fromGerund:o,fromParticiple:i}=t.two.models;let{prefix:s,verb:l,particle:u}=function(e,t){let n="",a={};t.one&&t.one.prefixes&&(a=t.one.prefixes);let[r,o]=e.split(/ /);return o&&!0===a[r]&&(n=r,r=o,o=""),{prefix:n,verb:r,particle:o}}(e,t),c="";if(n||(n=wi(e)),ki.hasOwnProperty(e))c=ki[e];else if("Participle"===n)c=Xo(l,i);else if("PastTense"===n)c=Xo(l,a);else if("PresentTense"===n)c=Xo(l,r);else{if("Gerund"!==n)return e;c=Xo(l,o)}return u&&(c+=" "+u),s&&(c=s+" "+c),c},Ai=function(e,t){const{toPast:n,toPresent:a,toGerund:r,toParticiple:o}=t.two.models;if("be"===e)return{Infinitive:e,Gerund:"being",PastTense:"was",PresentTense:"is"};let[i,s]=(e=>/ /.test(e)?e.split(/ /):[e,""])(e),l={Infinitive:i,PastTense:Xo(i,n),PresentTense:Xo(i,a),Gerund:Xo(i,r),FutureTense:"will "+i},u=Xo(i,o);if(u!==e&&u!==l.PastTense){let n=t.one.lexicon||{};"Participle"!==n[u]&&"Adjective"!==n[u]||("play"===e&&(u="played"),l.Participle=u)}return s&&Object.keys(l).forEach((e=>{l[e]+=" "+s})),l};var Ci={toInfinitive:Pi,conjugate:Ai,all:function(e,t){let n=Ai(e,t);return delete n.FutureTense,Object.values(n).filter((e=>e))}};const ji=function(e,t){const n=t.two.models.toSuperlative;return Xo(e,n)},Ni=function(e,t){const n=t.two.models.toComparative;return Xo(e,n)},Ii=function(e="",t=[]){const n=e.length;for(let a=n<=6?n-1:6;a>=1;a-=1){let r=e.substring(n-a,e.length);if(!0===t[r.length].hasOwnProperty(r)){return e.slice(0,n-a)+t[r.length][r]}}return null},Di="ically",Hi=new Set(["analyt"+Di,"chem"+Di,"class"+Di,"clin"+Di,"crit"+Di,"ecolog"+Di,"electr"+Di,"empir"+Di,"frant"+Di,"grammat"+Di,"ident"+Di,"ideolog"+Di,"log"+Di,"mag"+Di,"mathemat"+Di,"mechan"+Di,"med"+Di,"method"+Di,"method"+Di,"mus"+Di,"phys"+Di,"phys"+Di,"polit"+Di,"pract"+Di,"rad"+Di,"satir"+Di,"statist"+Di,"techn"+Di,"technolog"+Di,"theoret"+Di,"typ"+Di,"vert"+Di,"whims"+Di]),Gi=[null,{},{ly:""},{ily:"y",bly:"ble",ply:"ple"},{ally:"al",rply:"rp"},{ually:"ual",ially:"ial",cally:"cal",eally:"eal",rally:"ral",nally:"nal",mally:"mal",eeply:"eep",eaply:"eap"},{ically:"ic"}],Ti=new Set(["early","only","hourly","daily","weekly","monthly","yearly","mostly","duly","unduly","especially","undoubtedly","conversely","namely","exceedingly","presumably","accordingly","overly","best","latter","little","long","low"]),xi={wholly:"whole",fully:"full",truly:"true",gently:"gentle",singly:"single",customarily:"customary",idly:"idle",publically:"public",quickly:"quick",superbly:"superb",cynically:"cynical",well:"good"},Ei=[null,{y:"ily"},{ly:"ly",ic:"ically"},{ial:"ially",ual:"ually",tle:"tly",ble:"bly",ple:"ply",ary:"arily"},{},{},{}],Fi={cool:"cooly",whole:"wholly",full:"fully",good:"well",idle:"idly",public:"publicly",single:"singly",special:"especially"},Oi=function(e){if(Fi.hasOwnProperty(e))return Fi[e];let t=Ii(e,Ei);return t||(t=e+"ly"),t};var zi={toSuperlative:ji,toComparative:Ni,toAdverb:Oi,toNoun:function(e,t){const n=t.two.models.adjToNoun;return Xo(e,n)},fromAdverb:function(e){return e.endsWith("ly")?Hi.has(e)?e.replace(/ically/,"ical"):Ti.has(e)?null:xi.hasOwnProperty(e)?xi[e]:Ii(e,Gi)||e:null},fromSuperlative:function(e,t){const n=t.two.models.fromSuperlative;return Xo(e,n)},fromComparative:function(e,t){const n=t.two.models.fromComparative;return Xo(e,n)},all:function(e,t){let n=[e];return n.push(ji(e,t)),n.push(Ni(e,t)),n.push(Oi(e)),n=n.filter((e=>e)),n=new Set(n),Array.from(n)}},Vi={noun:yi,verb:Ci,adjective:zi},Bi={Singular:(e,t,n,a)=>{let r=a.one.lexicon,o=n.two.transform.noun.toPlural(e,a);r[o]||(t[o]=t[o]||"Plural")},Actor:(e,t,n,a)=>{let r=a.one.lexicon,o=n.two.transform.noun.toPlural(e,a);r[o]||(t[o]=t[o]||["Plural","Actor"])},Comparable:(e,t,n,a)=>{let r=a.one.lexicon,{toSuperlative:o,toComparative:i}=n.two.transform.adjective,s=o(e,a);r[s]||(t[s]=t[s]||"Superlative");let l=i(e,a);r[l]||(t[l]=t[l]||"Comparative"),t[e]="Adjective"},Demonym:(e,t,n,a)=>{let r=n.two.transform.noun.toPlural(e,a);t[r]=t[r]||["Demonym","Plural"]},Infinitive:(e,t,n,a)=>{let r=a.one.lexicon,o=n.two.transform.verb.conjugate(e,a);Object.entries(o).forEach((e=>{r[e[1]]||t[e[1]]||"FutureTense"===e[0]||(t[e[1]]=e[0])}))},PhrasalVerb:(e,t,n,a)=>{let r=a.one.lexicon;t[e]=["PhrasalVerb","Infinitive"];let o=a.one._multiCache,[i,s]=e.split(" ");r[i]||(t[i]=t[i]||"Infinitive");let l=n.two.transform.verb.conjugate(i,a);delete l.FutureTense,Object.entries(l).forEach((e=>{if("Actor"===e[0]||""===e[1])return;t[e[1]]||r[e[1]]||(t[e[1]]=e[0]),o[e[1]]=2;let n=e[1]+" "+s;t[n]=t[n]||[e[0],"PhrasalVerb"]}))},Multiple:(e,t)=>{t[e]=["Multiple","Cardinal"],t[e+"th"]=["Multiple","Ordinal"],t[e+"ths"]=["Multiple","Fraction"]},Cardinal:(e,t)=>{t[e]=["TextValue","Cardinal"]},Ordinal:(e,t)=>{t[e]=["TextValue","Ordinal"],t[e+"s"]=["TextValue","Fraction"]},Place:(e,t)=>{t[e]=["Place","ProperNoun"]},Region:(e,t)=>{t[e]=["Region","ProperNoun"]}};const Si={e:["mice","louse","antennae","formulae","nebulae","vertebrae","vitae"],i:["tia","octopi","viri","radii","nuclei","fungi","cacti","stimuli"],n:["men"],t:["feet"]},Ki=new Set(["israelis","menus","logos"]),$i=["bus","mas","was","ias","xas","vas","cis","lis","nis","ois","ris","sis","tis","xis","aus","cus","eus","fus","gus","ius","lus","nus","das","ous","pus","rus","sus","tus","xus","aos","igos","ados","ogos","'s","ss"],Li=function(e){if(!e||e.length<=3)return!1;if(Ki.has(e))return!0;let t=e[e.length-1];return Si.hasOwnProperty(t)?Si[t].find((t=>e.endsWith(t))):"s"===t&&!$i.find((t=>e.endsWith(t)))};var Mi={two:{quickSplit:function(e){const t=/[,:;]/;let n=[];return e.forEach((e=>{let a=0;e.forEach(((r,o)=>{t.test(r.post)&&function(e,t){const n=/^[0-9]+$/;let a=e[t];if(!a)return!1;const r=new Set(["may","april","august","jan"]);if("like"===a.normal||r.has(a.normal))return!1;if(a.tags.has("Place")||a.tags.has("Date"))return!1;if(e[t-1]){let n=e[t-1];if(n.tags.has("Date")||r.has(n.normal))return!1;if(n.tags.has("Adjective")||a.tags.has("Adjective"))return!1}let o=a.normal;return 1!==o.length&&2!==o.length&&4!==o.length||!n.test(o)}(e,o+1)&&(n.push(e.slice(a,o+1)),a=o+1)})),a<e.length&&n.push(e.slice(a,e.length))})),n},expandLexicon:function(e,t){const{methods:n,model:a}=t;let r={},o={};return Object.keys(e).forEach((t=>{let i=e[t],s=(t=(t=t.toLowerCase().trim()).replace(/'s\b/,"")).split(/ /);s.length>1&&(void 0===o[s[0]]||s.length>o[s[0]])&&(o[s[0]]=s.length),!0===Bi.hasOwnProperty(i)&&Bi[i](t,r,n,a),r[t]=r[t]||i})),delete r[""],delete r.null,delete r[" "],{lex:r,_multi:o}},transform:Vi,looksPlural:Li}};let Ji={one:{lexicon:{}},two:{models:gi}};const Wi={"Actor|Verb":"Actor","Adj|Gerund":"Adjective","Adj|Noun":"Adjective","Adj|Past":"Adjective","Adj|Present":"Adjective","Noun|Verb":"Singular","Noun|Gerund":"Gerund","Person|Noun":"Noun","Person|Date":"Month","Person|Verb":"FirstName","Person|Place":"Person","Person|Adj":"Comparative","Plural|Verb":"Plural","Unit|Noun":"Noun"},Ui=function(e,t){const n={model:t,methods:Mi};let{lex:a,_multi:r}=Mi.two.expandLexicon(e,n);return Object.assign(t.one.lexicon,a),Object.assign(t.one._multiCache,r),t},qi=function(e,t,n){let a=Ai(e,Ji);t[a.PastTense]=t[a.PastTense]||"PastTense",t[a.Gerund]=t[a.Gerund]||"Gerund",!0===n&&(t[a.PresentTense]=t[a.PresentTense]||"PresentTense")},Ri=function(e,t,n){let a=ji(e,n);t[a]=t[a]||"Superlative";let r=Ni(e,n);t[r]=t[r]||"Comparative"},Qi=function(e,t){let n={};const a=t.one.lexicon;return Object.keys(e).forEach((r=>{const o=e[r];if(n[r]=Wi[o],"Noun|Verb"!==o&&"Person|Verb"!==o&&"Actor|Verb"!==o||qi(r,a,!1),"Adj|Present"===o&&(qi(r,a,!0),Ri(r,a,t)),"Person|Adj"===o&&Ri(r,a,t),"Adj|Gerund"===o||"Noun|Gerund"===o){let e=Pi(r,Ji,"Gerund");a[e]||(n[e]="Infinitive")}if("Noun|Gerund"!==o&&"Adj|Noun"!==o&&"Person|Noun"!==o||function(e,t,n){let a=kr(e,n);t[a]=t[a]||"Plural"}(r,a,t),"Adj|Past"===o){let e=Pi(r,Ji,"PastTense");a[e]||(n[e]="Infinitive")}})),t=Ui(n,t)};let Zi={one:{_multiCache:{},lexicon:Ar,frozenLex:{"20th century fox":"Organization","7 eleven":"Organization","motel 6":"Organization","excuse me":"Expression","financial times":"Organization","guns n roses":"Organization","la z boy":"Organization","labour party":"Organization","new kids on the block":"Organization","new york times":"Organization","the guess who":"Organization","thin lizzy":"Organization","prime minister":"Actor","free market":"Singular","lay up":"Singular","living room":"Singular","living rooms":"Plural","spin off":"Singular","appeal court":"Uncountable","cold war":"Uncountable","gene pool":"Uncountable","machine learning":"Uncountable","nail polish":"Uncountable","time off":"Uncountable","take part":"Infinitive","bill gates":"Person","doctor who":"Person","dr who":"Person","he man":"Person","iron man":"Person","kid cudi":"Person","run dmc":"Person","rush limbaugh":"Person","snow white":"Person","tiger woods":"Person","brand new":"Adjective","en route":"Adjective","left wing":"Adjective","off guard":"Adjective","on board":"Adjective","part time":"Adjective","right wing":"Adjective","so called":"Adjective","spot on":"Adjective","straight forward":"Adjective","super duper":"Adjective","tip top":"Adjective","top notch":"Adjective","up to date":"Adjective","win win":"Adjective","brooklyn nets":"SportsTeam","chicago bears":"SportsTeam","houston astros":"SportsTeam","houston dynamo":"SportsTeam","houston rockets":"SportsTeam","houston texans":"SportsTeam","minnesota twins":"SportsTeam","orlando magic":"SportsTeam","san antonio spurs":"SportsTeam","san diego chargers":"SportsTeam","san diego padres":"SportsTeam","iron maiden":"ProperNoun","isle of man":"Country","united states":"Country","united states of america":"Country","prince edward island":"Region","cedar breaks":"Place","cedar falls":"Place","point blank":"Adverb","tiny bit":"Adverb","by the time":"Conjunction","no matter":"Conjunction","civil wars":"Plural","credit cards":"Plural","default rates":"Plural","free markets":"Plural","head starts":"Plural","home runs":"Plural","lay ups":"Plural","phone calls":"Plural","press releases":"Plural","record labels":"Plural","soft serves":"Plural","student loans":"Plural","tax returns":"Plural","tv shows":"Plural","video games":"Plural","took part":"PastTense","takes part":"PresentTense","taking part":"Gerund","taken part":"Participle","light bulb":"Noun","rush hour":"Noun","fluid ounce":"Unit","the rolling stones":"Organization"}},two:{irregularPlurals:cr,models:gi,suffixPatterns:jo,prefixPatterns:Ho,endsWith:$o,neighbours:Jo,regexNormal:[[/^[\w.]+@[\w.]+\.[a-z]{2,3}$/,"Email"],[/^(https?:\/\/|www\.)+\w+\.[a-z]{2,3}/,"Url","http.."],[/^[a-z0-9./].+\.(com|net|gov|org|ly|edu|info|biz|dev|ru|jp|de|in|uk|br|io|ai)/,"Url",".com"],[/^[PMCE]ST$/,"Timezone","EST"],[/^ma?c'[a-z]{3}/,"LastName","mc'neil"],[/^o'[a-z]{3}/,"LastName","o'connor"],[/^ma?cd[aeiou][a-z]{3}/,"LastName","mcdonald"],[/^(lol)+[sz]$/,"Expression","lol"],[/^wo{2,}a*h?$/,"Expression","wooah"],[/^(hee?){2,}h?$/,"Expression","hehe"],[/^(un|de|re)\\-[a-z\u00C0-\u00FF]{2}/,"Verb","un-vite"],[/^(m|k|cm|km)\/(s|h|hr)$/,"Unit","5 k/m"],[/^(ug|ng|mg)\/(l|m3|ft3)$/,"Unit","ug/L"],[/[^:/]\/\p{Letter}/u,"SlashedTerm","love/hate"]],regexText:[[/^#[\p{Number}_]*\p{Letter}/u,"HashTag"],[/^@\w{2,}$/,"AtMention"],[/^([A-Z]\.){2}[A-Z]?/i,["Acronym","Noun"],"F.B.I"],[/.{3}[lkmnp]in['‘’‛‵′`´]$/,"Gerund","chillin'"],[/.{4}s['‘’‛‵′`´]$/,"Possessive","flanders'"],[/^[\p{Emoji_Presentation}\p{Extended_Pictographic}]/u,"Emoji","emoji-class"]],regexNumbers:[[/^@1?[0-9](am|pm)$/i,"Time","3pm"],[/^@1?[0-9]:[0-9]{2}(am|pm)?$/i,"Time","3:30pm"],[/^'[0-9]{2}$/,"Year"],[/^[012]?[0-9](:[0-5][0-9])(:[0-5][0-9])$/,"Time","3:12:31"],[/^[012]?[0-9](:[0-5][0-9])?(:[0-5][0-9])? ?(am|pm)$/i,"Time","1:12pm"],[/^[012]?[0-9](:[0-5][0-9])(:[0-5][0-9])? ?(am|pm)?$/i,"Time","1:12:31pm"],[/^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}/i,"Date","iso-date"],[/^[0-9]{1,4}-[0-9]{1,2}-[0-9]{1,4}$/,"Date","iso-dash"],[/^[0-9]{1,4}\/[0-9]{1,2}\/([0-9]{4}|[0-9]{2})$/,"Date","iso-slash"],[/^[0-9]{1,4}\.[0-9]{1,2}\.[0-9]{1,4}$/,"Date","iso-dot"],[/^[0-9]{1,4}-[a-z]{2,9}-[0-9]{1,4}$/i,"Date","12-dec-2019"],[/^utc ?[+-]?[0-9]+$/,"Timezone","utc-9"],[/^(gmt|utc)[+-][0-9]{1,2}$/i,"Timezone","gmt-3"],[/^[0-9]{3}-[0-9]{4}$/,"PhoneNumber","421-0029"],[/^(\+?[0-9][ -])?[0-9]{3}[ -]?[0-9]{3}-[0-9]{4}$/,"PhoneNumber","1-800-"],[/^[-+]?\p{Currency_Symbol}[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?([kmb]|bn)?\+?$/u,["Money","Value"],"$5.30"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?\p{Currency_Symbol}\+?$/u,["Money","Value"],"5.30£"],[/^[-+]?[$£]?[0-9]([0-9,.])+(usd|eur|jpy|gbp|cad|aud|chf|cny|hkd|nzd|kr|rub)$/i,["Money","Value"],"$400usd"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?\+?$/,["Cardinal","NumericValue"],"5,999"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?(st|nd|rd|r?th)$/,["Ordinal","NumericValue"],"53rd"],[/^\.[0-9]+\+?$/,["Cardinal","NumericValue"],".73th"],[/^[-+]?[0-9]+(,[0-9]{3})*(\.[0-9]+)?%\+?$/,["Percent","Cardinal","NumericValue"],"-4%"],[/^\.[0-9]+%$/,["Percent","Cardinal","NumericValue"],".3%"],[/^[0-9]{1,4}\/[0-9]{1,4}(st|nd|rd|th)?s?$/,["Fraction","NumericValue"],"2/3rds"],[/^[0-9.]{1,3}[a-z]{0,2}[-–—][0-9]{1,3}[a-z]{0,2}$/,["Value","NumberRange"],"3-4"],[/^[0-9]{1,2}(:[0-9][0-9])?(am|pm)? ?[-–—] ?[0-9]{1,2}(:[0-9][0-9])?(am|pm)$/,["Time","NumberRange"],"3-4pm"],[/^[0-9.]+([a-z°]{1,4})$/,"NumericValue","9km"]],switches:Cr,clues:lo,uncountable:{},orgWords:mi,placeWords:pi}};Zi=function(e){return e=function(e,t){return Object.keys(e).forEach((n=>{"Uncountable"===e[n]&&(t.two.uncountable[n]=!0,e[n]="Uncountable")})),t}((e=Ui(e.one.lexicon,e)).one.lexicon,e),e=function(e){const{irregularPlurals:t}=e.two,{lexicon:n}=e.one;return Object.entries(t).forEach((e=>{n[e[0]]=n[e[0]]||"Singular",n[e[1]]=n[e[1]]||"Plural"})),e}(e=Qi(e.two.switches,e)),e}(Zi);const Xi=function(e,t,n,a){const r=a.methods.one.setTag;"-"===e[t].post&&e[t+1]&&r([e[t],e[t+1]],"Hyphenated",a,null,"1-punct-hyphen''")},_i=/^(under|over|mis|re|un|dis|semi)-?/,Yi=function(e,t,n){const a=n.two.switches;let r=e[t];if(a.hasOwnProperty(r.normal))r.switch=a[r.normal];else if(_i.test(r.normal)){let e=r.normal.replace(_i,"");e.length>3&&a.hasOwnProperty(e)&&(r.switch=a[e])}},es=function(e,t,n){if(!t||0===t.length)return;if(!0===e.frozen)return;const a="undefined"!=typeof process&&process.env?process.env:self.env||{};a&&a.DEBUG_TAGS&&((e,t,n="")=>{let a=e.text||"["+e.implicit+"]";var r;"string"!=typeof t&&t.length>2&&(t=t.slice(0,2).join(", #")+" +"),t="string"!=typeof t?t.join(", #"):t,console.log(` ${(r=a,"[33m[3m"+r+"[0m").padEnd(24)} [32m→[0m #${t.padEnd(22)}  ${(e=>"[3m"+e+"[0m")(n)}`)})(e,t,n),e.tags=e.tags||new Set,"string"==typeof t?e.tags.add(t):t.forEach((t=>e.tags.add(t)))},ts=["Acronym","Abbreviation","ProperNoun","Uncountable","Possessive","Pronoun","Activity","Honorific","Month"],ns=function(e,t,n){let a=e[t],r=Array.from(a.tags);for(let e=0;e<r.length;e+=1)if(n.one.tagSet[r[e]]){let t=n.one.tagSet[r[e]].parents;es(a,t,` -inferred by #${r[e]}`)}!function(e){!e.tags.has("Noun")||e.tags.has("Plural")||e.tags.has("Singular")||ts.find((t=>e.tags.has(t)))||(Li(e.normal)?es(e,"Plural","3-plural-guess"):es(e,"Singular","3-singular-guess"))}(a),function(e){let t=e.tags;if(t.has("Verb")&&1===t.size){let t=wi(e.normal);t&&es(e,t,"3-verb-tense-guess")}}(a)},as=/^\p{Lu}[\p{Ll}'’]/u,rs=/[0-9]/,os=["Date","Month","WeekDay","Unit","Expression"],is=/[IVX]/,ss=/^[IVXLCDM]{2,}$/,ls=/^M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$/,us={li:!0,dc:!0,md:!0,dm:!0,ml:!0},cs=function(e,t,n){let a=e[t];a.index=a.index||[0,0];let r=a.index[1],o=a.text||"";return 0!==r&&!0===as.test(o)&&!1===rs.test(o)?os.find((e=>a.tags.has(e)))||a.pre.match(/["']$/)||"the"===a.normal?null:(ns(e,t,n),a.tags.has("Noun")||a.frozen||a.tags.clear(),es(a,"ProperNoun","2-titlecase"),!0):o.length>=2&&ss.test(o)&&is.test(o)&&ls.test(o)&&!us[a.normal]?(es(a,"RomanNumeral","2-xvii"),!0):null},ds=function(e="",t=[]){const n=e.length;let a=7;n<=a&&(a=n-1);for(let r=a;r>1;r-=1){let a=e.substring(n-r,n);if(!0===t[a.length].hasOwnProperty(a)){return t[a.length][a]}}return null},hs=function(e,t,n){let a=e[t];if(0===a.tags.size){let e=ds(a.normal,n.two.suffixPatterns);if(null!==e)return es(a,e,"2-suffix"),a.confidence=.7,!0;if(a.implicit&&(e=ds(a.implicit,n.two.suffixPatterns),null!==e))return es(a,e,"2-implicit-suffix"),a.confidence=.7,!0}return null},gs=/['‘’‛‵′`´]/,ms=function(e,t){for(let n=0;n<t.length;n+=1)if(!0===t[n][0].test(e))return t[n];return null},ps=function(e,t,n,a){const r=a.methods.one.setTag;let{regexText:o,regexNormal:i,regexNumbers:s,endsWith:l}=n.two,u=e[t],c=u.machine||u.normal,d=u.text;gs.test(u.post)&&!gs.test(u.pre)&&(d+=u.post.trim());let h=ms(d,o)||ms(c,i);return!h&&/[0-9]/.test(c)&&(h=ms(c,s)),h||0!==u.tags.size||(h=function(e="",t){let n=e[e.length-1];if(!0===t.hasOwnProperty(n)){let a=t[n]||[];for(let t=0;t<a.length;t+=1)if(!0===a[t][0].test(e))return a[t]}return null}(c,l)),h?(r([u],h[1],a,null,`2-regex-'${h[2]||h[0]}'`),u.confidence=.6,!0):null},fs=function(e,t,n){let a=e[t];if(0===a.tags.size){let e=function(e="",t=[]){const n=e.length;let a=7;a>n-3&&(a=n-3);for(let n=a;n>2;n-=1){let a=e.substring(0,n);if(!0===t[a.length].hasOwnProperty(a))return t[a.length][a]}return null}(a.normal,n.two.prefixPatterns);if(null!==e)return es(a,e,"2-prefix"),a.confidence=.5,!0}return null},bs=new Set(["in","on","by","until","for","to","during","throughout","through","within","before","after","of","this","next","last","circa","around","post","pre","budget","classic","plan","may"]),ys=function(e){if(!e)return!1;let t=e.normal||e.implicit;return!!bs.has(t)||(!!(e.tags.has("Date")||e.tags.has("Month")||e.tags.has("WeekDay")||e.tags.has("Year"))||!!e.tags.has("ProperNoun"))},vs=function(e){return!!e&&(!!e.tags.has("Ordinal")||(!!(e.tags.has("Cardinal")&&e.normal.length<3)||("is"===e.normal||"was"===e.normal)))},ws=function(e){return e&&(e.tags.has("Date")||e.tags.has("Month")||e.tags.has("WeekDay")||e.tags.has("Year"))},ks=function(e,t){const n=e[t];if(n.tags.has("NumericValue")&&n.tags.has("Cardinal")&&4===n.normal.length){let a=Number(n.normal);if(a&&!isNaN(a)&&a>1400&&a<2100){let r=e[t-1],o=e[t+1];if(ys(r)||ys(o))return es(n,"Year","2-tagYear");if(a>=1920&&a<2025){if(vs(r)||vs(o))return es(n,"Year","2-tagYear-close");if(ws(e[t-2])||ws(e[t+2]))return es(n,"Year","2-tagYear-far");if(r&&(r.tags.has("Determiner")||r.tags.has("Possessive"))&&o&&o.tags.has("Noun")&&!o.tags.has("Plural"))return es(n,"Year","2-tagYear-noun")}}}return null},Ps=function(e,t,n,a){const r=a.methods.one.setTag,o=e[t],i=["PastTense","PresentTense","Auxiliary","Modal","Particle"];if(o.tags.has("Verb")){i.find((e=>o.tags.has(e)))||r([o],"Infinitive",a,null,"2-verb-type''")}},As=/^[A-Z]('s|,)?$/,Cs=/^[A-Z-]+$/,js=/^[A-Z]+s$/,Ns=/([A-Z]\.)+[A-Z]?,?$/,Is=/[A-Z]{2,}('s|,)?$/,Ds=/([a-z]\.)+[a-z]\.?$/,Hs={I:!0,A:!0},Gs={la:!0,ny:!0,us:!0,dc:!0,gb:!0},Ts=function(e,t,n){let a=e[t];return a.tags.has("RomanNumeral")||a.tags.has("Acronym")||a.frozen?null:function(e,t){let n=e.text;if(!1===Cs.test(n)){if(!(n.length>3&&!0===js.test(n)))return!1;n=n.replace(/s$/,"")}return!(n.length>5||Hs.hasOwnProperty(n)||t.one.lexicon.hasOwnProperty(e.normal)||!0!==Ns.test(n)&&!0!==Ds.test(n)&&!0!==As.test(n)&&!0!==Is.test(n))}(a,n)?(a.tags.clear(),es(a,["Acronym","Noun"],"3-no-period-acronym"),!0===Gs[a.normal]&&es(a,"Place","3-place-acronym"),!0===js.test(a.text)&&es(a,"Plural","3-plural-acronym"),!0):!Hs.hasOwnProperty(a.text)&&As.test(a.text)?(a.tags.clear(),es(a,["Acronym","Noun"],"3-one-letter-acronym"),!0):a.tags.has("Organization")&&a.text.length<=3?(es(a,"Acronym","3-org-acronym"),!0):a.tags.has("Organization")&&Cs.test(a.text)&&a.text.length<=6?(es(a,"Acronym","3-titlecase-acronym"),!0):null},xs=function(e,t){if(!e)return null;let n=t.find((t=>e.normal===t[0]));return n?n[1]:null},Es=function(e,t){if(!e)return null;let n=t.find((t=>e.tags.has(t[0])));return n?n[1]:null},Fs=function(e,t,n){const{leftTags:a,leftWords:r,rightWords:o,rightTags:i}=n.two.neighbours;let s=e[t];if(0===s.tags.size){let l=null;if(l=l||xs(e[t-1],r),l=l||xs(e[t+1],o),l=l||Es(e[t-1],a),l=l||Es(e[t+1],i),l)return es(s,l,"3-[neighbour]"),ns(e,t,n),e[t].confidence=.2,!0}return null},Os=function(e,t,n){return!!e&&(!e.tags.has("FirstName")&&!e.tags.has("Place")&&(!!(e.tags.has("ProperNoun")||e.tags.has("Organization")||e.tags.has("Acronym"))||!(n||(a=e.text,!/^\p{Lu}[\p{Ll}'’]/u.test(a)))&&(0!==t||e.tags.has("Singular"))));var a},zs=function(e,t,n,a){const r=n.model.two.orgWords,o=n.methods.one.setTag;let i=e[t];if(!0===r[i.machine||i.normal]&&Os(e[t-1],t-1,a)){o([e[t]],"Organization",n,null,"3-[org-word]");for(let r=t;r>=0&&Os(e[r],r,a);r-=1)o([e[r]],"Organization",n,null,"3-[org-word]")}return null},Vs=/'s$/,Bs=new Set(["athletic","city","community","eastern","federal","financial","great","historic","historical","local","memorial","municipal","national","northern","provincial","southern","state","western","spring","pine","sunset","view","oak","maple","spruce","cedar","willow"]),Ss=new Set(["center","centre","way","range","bar","bridge","field","pit"]),Ks=function(e,t,n){if(!e)return!1;let a=e.tags;return!(a.has("Organization")||a.has("Possessive")||Vs.test(e.normal))&&(!(!a.has("ProperNoun")&&!a.has("Place"))||!(n||(r=e.text,!/^\p{Lu}[\p{Ll}'’]/u.test(r)))&&(0!==t||a.has("Singular")));var r},$s=function(e,t,n,a){const r=n.model.two.placeWords,o=n.methods.one.setTag;let i=e[t],s=i.machine||i.normal;if(!0===r[s]){for(let r=t-1;r>=0;r-=1)if(!Bs.has(e[r].normal)){if(!Ks(e[r],r,a))break;o(e.slice(r,t+1),"Place",n,null,"3-[place-of-foo]")}if(Ss.has(s))return!1;for(let r=t+1;r<e.length;r+=1){if(Ks(e[r],r,a))return o(e.slice(t,r+1),"Place",n,null,"3-[foo-place]"),!0;if("of"!==e[r].normal&&!Bs.has(e[r].normal))break}}return null},Ls=function(e,t,n){let a=!1,r=e[t].tags;(0===r.size||1===r.size&&(r.has("Hyphenated")||r.has("HashTag")||r.has("Prefix")||r.has("SlashedTerm")))&&(a=!0),a&&(es(e[t],"Noun","3-[fallback]"),ns(e,t,n),e[t].confidence=.1)},Ms=/^[A-Z][a-z]/,Js=(e,t)=>e[t].tags.has("ProperNoun")&&Ms.test(e[t].text)?"Noun":null,Ws=(e,t,n)=>0!==t||e[1]?null:n,Us={"Adj|Gerund":(e,t)=>Js(e,t),"Adj|Noun":(e,t)=>Js(e,t)||function(e,t){return!e[t+1]&&e[t-1]&&e[t-1].tags.has("Determiner")?"Noun":null}(e,t),"Actor|Verb":(e,t)=>Js(e,t),"Adj|Past":(e,t)=>Js(e,t),"Adj|Present":(e,t)=>Js(e,t),"Noun|Gerund":(e,t)=>Js(e,t),"Noun|Verb":(e,t)=>t>0&&Js(e,t)||Ws(e,t,"Infinitive"),"Plural|Verb":(e,t)=>Js(e,t)||Ws(e,t,"PresentTense")||function(e,t,n){return 0===t&&e.length>3?n:null}(e,t,"Plural"),"Person|Noun":(e,t)=>Js(e,t),"Person|Verb":(e,t)=>0!==t?Js(e,t):null,"Person|Adj":(e,t)=>0===t&&e.length>1||Js(e,t)?"Person":null},qs="undefined"!=typeof process&&process.env?process.env:self.env||{},Rs=/^(under|over|mis|re|un|dis|semi)-?/,Qs=(e,t)=>{if(!e||!t)return null;let n=e.normal||e.implicit,a=null;return t.hasOwnProperty(n)&&(a=t[n]),a&&qs.DEBUG_TAGS&&console.log(`\n  [2m[3m     ↓ - '${n}' [0m`),a},Zs=(e,t={},n)=>{if(!e||!t)return null;let a=Array.from(e.tags).sort(((e,t)=>(n[e]?n[e].parents.length:0)>(n[t]?n[t].parents.length:0)?-1:1)),r=a.find((e=>t[e]));return r&&qs.DEBUG_TAGS&&console.log(`  [2m[3m      ↓ - '${e.normal||e.implicit}' (#${r})  [0m`),r=t[r],r},Xs=function(e,t,n){const a=n.model,r=n.methods.one.setTag,{switches:o,clues:i}=a.two,s=e[t];let l=s.normal||s.implicit||"";if(Rs.test(l)&&!o[l]&&(l=l.replace(Rs,"")),s.switch){let o=s.switch;if(s.tags.has("Acronym")||s.tags.has("PhrasalVerb"))return;let u=function(e,t,n,a){if(!n)return null;const r="also"!==e[t-1]?.text?t-1:Math.max(0,t-2),o=a.one.tagSet;let i=Qs(e[t+1],n.afterWords);return i=i||Qs(e[r],n.beforeWords),i=i||Zs(e[r],n.beforeTags,o),i=i||Zs(e[t+1],n.afterTags,o),i}(e,t,i[o],a);Us[o]&&(u=Us[o](e,t)||u),u?(r([s],u,n,null,`3-[switch] (${o})`),ns(e,t,a)):qs.DEBUG_TAGS&&console.log(`\n -> X  - '${l}'  : (${o})  `)}},_s={there:!0,this:!0,it:!0,him:!0,her:!0,us:!0},Ys=function(e){if(e.filter((e=>!e.tags.has("ProperNoun"))).length<=3)return!1;const t=/^[a-z]/;return e.every((e=>!t.test(e.text)))},el=function(e,t,n,a){for(let r=0;r<e.length;r+=1)!0!==e[r].frozen&&(Yi(e,r,t),!1===a&&cs(e,r,t),hs(e,r,t),ps(e,r,t,n),fs(e,r,t),ks(e,r))},tl=function(e,t,n,a){for(let n=0;n<e.length;n+=1){let a=Ts(e,n,t);ns(e,n,t),a=a||Fs(e,n,t),a=a||Ls(e,n,t)}for(let t=0;t<e.length;t+=1)!0!==e[t].frozen&&(zs(e,t,n,a),$s(e,t,n,a),Xs(e,t,n),Ps(e,t,0,n),Xi(e,t,0,n));!function(e,t){const n=t.methods.one.setTag,a=t.model.one._multiCache||{};let r=e[0];if(("Noun|Verb"===r.switch||r.tags.has("Infinitive"))&&e.length>=2){if(e.length<4&&!_s[e[1].normal])return;if(!r.tags.has("PhrasalVerb")&&a.hasOwnProperty(r.normal))return;(e[1].tags.has("Noun")||e[1].tags.has("Determiner"))&&(e.slice(1,3).some((e=>e.tags.has("Verb")))&&!r.tags.has("#PhrasalVerb")||n([r],"Imperative",t,null,"3-[imperative]"))}}(e,n)},nl={Possessive:e=>{let t=e.machine||e.normal||e.text;return t=t.replace(/'s$/,""),t},Plural:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.noun.toSingular(n,t.model)},Copula:()=>"is",PastTense:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.verb.toInfinitive(n,t.model,"PastTense")},Gerund:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.verb.toInfinitive(n,t.model,"Gerund")},PresentTense:(e,t)=>{let n=e.machine||e.normal||e.text;return e.tags.has("Infinitive")?n:t.methods.two.transform.verb.toInfinitive(n,t.model,"PresentTense")},Comparative:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.adjective.fromComparative(n,t.model)},Superlative:(e,t)=>{let n=e.machine||e.normal||e.text;return t.methods.two.transform.adjective.fromSuperlative(n,t.model)},Adverb:(e,t)=>{const{fromAdverb:n}=t.methods.two.transform.adjective;return n(e.machine||e.normal||e.text)}},al={Adverb:"RB",Comparative:"JJR",Superlative:"JJS",Adjective:"JJ",TO:"Conjunction",Modal:"MD",Auxiliary:"MD",Gerund:"VBG",PastTense:"VBD",Participle:"VBN",PresentTense:"VBZ",Infinitive:"VB",Particle:"RP",Verb:"VB",Pronoun:"PRP",Cardinal:"CD",Conjunction:"CC",Determiner:"DT",Preposition:"IN",QuestionWord:"WP",Expression:"UH",Possessive:"POS",ProperNoun:"NNP",Person:"NNP",Place:"NNP",Organization:"NNP",Singular:"NN",Plural:"NNS",Noun:"NN",There:"EX"};var rl={preTagger:function(e){const{methods:t,model:n,world:a}=e;let r=e.docs;!function(e,t,n){e.forEach((e=>{!function(e,t,n,a){const r=a.methods.one.setTag;if(e.length>=3){const t=/:/;if(e[0].post.match(t)){let t=e[1];if(t.tags.has("Value")||t.tags.has("Email")||t.tags.has("PhoneNumber"))return;r([e[0]],"Expression",a,null,"2-punct-colon''")}}}(e,0,0,n)}))}(r,0,a);let o=t.two.quickSplit(r);for(let e=0;e<o.length;e+=1){let t=o[e];const r=Ys(t);el(t,n,a,r),tl(t,n,a,r)}return o},root:function(e){const t=e.world,n=Object.keys(nl);e.docs.forEach((e=>{for(let a=0;a<e.length;a+=1){const r=e[a];for(let e=0;e<n.length;e+=1)if(r.tags.has(n[e])){let a=(0,nl[n[e]])(r,t);r.normal!==a&&(r.root=a);break}}}))},penn:function(e){e.compute("tagRank"),e.docs.forEach((e=>{e.forEach((e=>{e.penn=function(e){if(e.tags.has("ProperNoun")&&e.tags.has("Plural"))return"NNPS";if(e.tags.has("Possessive")&&e.tags.has("Pronoun"))return"PRP$";if("there"===e.normal)return"EX";if("to"===e.normal)return"TO";let t=e.tagRank||[];for(let e=0;e<t.length;e+=1)if(al.hasOwnProperty(t[e]))return al[t[e]];return null}(e)}))}))}};const ol=["Person","Place","Organization"];var il={Noun:{not:["Verb","Adjective","Adverb","Value","Determiner"]},Singular:{is:"Noun",not:["Plural","Uncountable"]},ProperNoun:{is:"Noun"},Person:{is:"Singular",also:["ProperNoun"],not:["Place","Organization","Date"]},FirstName:{is:"Person"},MaleName:{is:"FirstName",not:["FemaleName","LastName"]},FemaleName:{is:"FirstName",not:["MaleName","LastName"]},LastName:{is:"Person",not:["FirstName"]},Honorific:{is:"Person",not:["FirstName","LastName","Value"]},Place:{is:"Singular",not:["Person","Organization"]},Country:{is:"Place",also:["ProperNoun"],not:["City"]},City:{is:"Place",also:["ProperNoun"],not:["Country"]},Region:{is:"Place",also:["ProperNoun"]},Address:{},Organization:{is:"ProperNoun",not:["Person","Place"]},SportsTeam:{is:"Organization"},School:{is:"Organization"},Company:{is:"Organization"},Plural:{is:"Noun",not:["Singular","Uncountable"]},Uncountable:{is:"Noun"},Pronoun:{is:"Noun",not:ol},Actor:{is:"Noun",not:["Place","Organization"]},Activity:{is:"Noun",not:["Person","Place"]},Unit:{is:"Noun",not:ol},Demonym:{is:"Noun",also:["ProperNoun"],not:ol},Possessive:{is:"Noun"},Reflexive:{is:"Pronoun"}};var sl={Adjective:{not:["Noun","Verb","Adverb","Value"]},Comparable:{is:"Adjective"},Comparative:{is:"Adjective"},Superlative:{is:"Adjective",not:["Comparative"]},NumberRange:{},Adverb:{not:["Noun","Verb","Adjective","Value"]},Determiner:{not:["Noun","Verb","Adjective","Adverb","QuestionWord","Conjunction"]},Conjunction:{not:["Noun","Verb","Adjective","Adverb","Value","QuestionWord"]},Preposition:{not:["Noun","Verb","Adjective","Adverb","QuestionWord","Determiner"]},QuestionWord:{not:["Determiner"]},Currency:{is:"Noun"},Expression:{not:["Noun","Adjective","Verb","Adverb"]},Abbreviation:{},Url:{not:["HashTag","PhoneNumber","Verb","Adjective","Value","AtMention","Email","SlashedTerm"]},PhoneNumber:{not:["HashTag","Verb","Adjective","Value","AtMention","Email"]},HashTag:{},AtMention:{is:"Noun",not:["HashTag","Email"]},Emoji:{not:["HashTag","Verb","Adjective","Value","AtMention"]},Emoticon:{not:["HashTag","Verb","Adjective","Value","AtMention","SlashedTerm"]},SlashedTerm:{not:["Emoticon","Url","Value"]},Email:{not:["HashTag","Verb","Adjective","Value","AtMention"]},Acronym:{not:["Plural","RomanNumeral","Pronoun","Date"]},Negative:{not:["Noun","Adjective","Value","Expression"]},Condition:{not:["Verb","Adjective","Noun","Value"]},There:{not:["Verb","Adjective","Noun","Value","Conjunction","Preposition"]},Prefix:{not:["Abbreviation","Acronym","ProperNoun"]},Hyphenated:{}};let ll=Object.assign({},il,{Verb:{not:["Noun","Adjective","Adverb","Value","Expression"]},PresentTense:{is:"Verb",not:["PastTense","FutureTense"]},Infinitive:{is:"PresentTense",not:["Gerund"]},Imperative:{is:"Verb",not:["PastTense","Gerund","Copula"]},Gerund:{is:"PresentTense",not:["Copula"]},PastTense:{is:"Verb",not:["PresentTense","Gerund","FutureTense"]},FutureTense:{is:"Verb",not:["PresentTense","PastTense"]},Copula:{is:"Verb"},Modal:{is:"Verb",not:["Infinitive"]},Participle:{is:"PastTense"},Auxiliary:{is:"Verb",not:["PastTense","PresentTense","Gerund","Conjunction"]},PhrasalVerb:{is:"Verb"},Particle:{is:"PhrasalVerb",not:["PastTense","PresentTense","Copula","Gerund"]},Passive:{is:"Verb"}},{Value:{not:["Verb","Adjective","Adverb"]},Ordinal:{is:"Value",not:["Cardinal"]},Cardinal:{is:"Value",not:["Ordinal"]},Fraction:{is:"Value",not:["Noun"]},Multiple:{is:"TextValue"},RomanNumeral:{is:"Cardinal",not:["TextValue"]},TextValue:{is:"Value",not:["NumericValue"]},NumericValue:{is:"Value",not:["TextValue"]},Money:{is:"Cardinal"},Percent:{is:"Value"}},{Date:{not:["Verb","Adverb","Adjective"]},Month:{is:"Date",also:["Noun"],not:["Year","WeekDay","Time"]},WeekDay:{is:"Date",also:["Noun"]},Year:{is:"Date",not:["RomanNumeral"]},FinancialQuarter:{is:"Date",not:"Fraction"},Holiday:{is:"Date",also:["Noun"]},Season:{is:"Date"},Timezone:{is:"Date",also:["Noun"],not:["ProperNoun"]},Time:{is:"Date",not:["AtMention"]},Duration:{is:"Date",also:["Noun"]}},sl);var ul={compute:rl,methods:Mi,model:Zi,tags:ll,hooks:["preTagger"]};const cl=/[,)"';:\-–—.…]/,dl=function(e,t){if(!e.found)return;let n=e.termList();for(let e=0;e<n.length-1;e++){const t=n[e];if(cl.test(t.post))return}n[0].implicit=n[0].normal,n[0].text+=t,n[0].normal+=t,n.slice(1).forEach((e=>{e.implicit=e.normal,e.text="",e.normal=""}));for(let e=0;e<n.length-1;e++)n[e].post=n[e].post.replace(/ /,"")},hl=function(){let e=this.not("@hasContraction"),t=e.match("(we|they|you) are");return dl(t,"'re"),t=e.match("(he|she|they|it|we|you) will"),dl(t,"'ll"),t=e.match("(he|she|they|it|we) is"),dl(t,"'s"),t=e.match("#Person is"),dl(t,"'s"),t=e.match("#Person would"),dl(t,"'d"),t=e.match("(is|was|had|would|should|could|do|does|have|has|can) not"),dl(t,"n't"),t=e.match("(i|we|they) have"),dl(t,"'ve"),t=e.match("(would|should|could) have"),dl(t,"'ve"),t=e.match("i am"),dl(t,"'m"),t=e.match("going to"),this},gl=/^\p{Lu}[\p{Ll}'’]/u,ml=function(e,t,n){let[a,r]=t;n&&0!==n.length&&(n=n.map(((e,t)=>(e.implicit=e.text,e.machine=e.text,e.pre="",e.post="",e.text="",e.normal="",e.index=[a,r+t],e))),n[0]&&(n[0].pre=e[a][r].pre,n[n.length-1].post=e[a][r].post,n[0].text=e[a][r].text,n[0].normal=e[a][r].normal),e[a].splice(r,1,...n))},pl=/'/,fl=new Set(["been","become"]),bl=new Set(["what","how","when","if","too"]);let yl=new Set(["too","also","enough"]);const vl=function(e,t){let n=e[t].normal.split(pl)[0];if("let"===n)return[n,"us"];if("there"===n){let a=e[t+1];if(a&&a.tags.has("Plural"))return[n,"are"]}return"has"===((e,t)=>{for(let n=t+1;n<e.length;n+=1){let t=e[n];if(fl.has(t.normal))return"has";if(bl.has(t.normal))return"is";if(t.tags.has("Gerund"))return"is";if(t.tags.has("Determiner"))return"is";if(t.tags.has("Adjective"))return"is";if("Adj|Past"===t.switch&&e[n+1]){if(yl.has(e[n+1].normal))return"is";if(e[n+1].tags.has("Preposition"))return"is"}if(t.tags.has("PastTense"))return e[n+1]&&"for"===e[n+1].normal?"is":"has"}return"is"})(e,t)?[n,"has"]:[n,"is"]},wl=/'/,kl=new Set(["better","done","before","it","had"]),Pl=new Set(["have","be"]),Al=function(e,t){let n=e[t].normal.split(wl)[0];return"how"===n||"what"===n?[n,"did"]:"had"===((e,t)=>{for(let n=t+1;n<e.length;n+=1){let t=e[n];if(kl.has(t.normal))return"had";if(Pl.has(t.normal))return"would";if(t.tags.has("PastTense")||"Adj|Past"===t.switch)return"had";if(t.tags.has("PresentTense")||t.tags.has("Infinitive"))return"would";if(t.tags.has("#Determiner"))return"had";if(t.tags.has("Adjective"))return"would"}return!1})(e,t)?[n,"had"]:[n,"would"]},Cl={that:!0,there:!0,let:!0,here:!0,everywhere:!0},jl={in:!0,by:!0,for:!0};let Nl=new Set(["too","also","enough","about"]),Il=new Set(["is","are","did","were","could","should","must","had","have"]);const Dl=/'/,Hl=function(e,t,n,a){let r=t.update();r.document=[e];let o=n+a;n>0&&(n-=1),e[o]&&(o+=1),r.ptrs=[[0,n,o]],r.compute(["freeze","lexicon","preTagger","unfreeze"]),function(e){e.forEach(((e,t)=>{e.index&&(e.index[1]=t)}))}(e)},Gl={d:(e,t)=>Al(e,t),t:(e,t)=>function(e,t){if("ain't"===e[t].normal||"aint"===e[t].normal){if(e[t+1]&&"never"===e[t+1].normal)return["have"];let n=function(e,t){for(let n=t-1;n>=0;n-=1)if(e[n].tags.has("Noun")||e[n].tags.has("Pronoun")||e[n].tags.has("Plural")||e[n].tags.has("Singular"))return e[n];return null}(e,t);if(n){if("we"===n.normal||"they"===n.normal)return["are","not"];if("i"===n.normal)return["am","not"];if(n.tags&&n.tags.has("Plural"))return["are","not"]}return["is","not"]}return[e[t].normal.replace(/n't/,""),"not"]}(e,t),s:(e,t,n)=>((e,t)=>{let n=e[t];if(Cl.hasOwnProperty(n.machine||n.normal))return!1;if(n.tags.has("Possessive"))return!0;if(n.tags.has("QuestionWord"))return!1;if("he's"===n.normal||"she's"===n.normal)return!1;let a=e[t+1];if(!a)return!0;if("it's"===n.normal)return!!a.tags.has("#Noun");if("Noun|Gerund"==a.switch){let a=e[t+2];return a?!!a.tags.has("Copula")||("on"===a.normal||a.normal,!1):!(!n.tags.has("Actor")&&!n.tags.has("ProperNoun"))}if(a.tags.has("Verb"))return!!a.tags.has("Infinitive")||!a.tags.has("Gerund")&&!!a.tags.has("PresentTense");if("Adj|Noun"===a.switch){let n=e[t+2];if(!n)return!1;if(Il.has(n.normal))return!0;if(Nl.has(n.normal))return!1}if(a.tags.has("Noun")){let e=a.machine||a.normal;return!("here"===e||"there"===e||"everywhere"===e||a.tags.has("Possessive")||a.tags.has("ProperNoun")&&!n.tags.has("ProperNoun"))}if(e[t-1]&&!0===jl[e[t-1].normal])return!0;if(a.tags.has("Adjective")){let n=e[t+2];if(!n)return!1;if(n.tags.has("Noun")&&!n.tags.has("Pronoun")){let e=a.normal;return"above"!==e&&"below"!==e&&"behind"!==e}return"Noun|Verb"===n.switch}return!!a.tags.has("Value")})(e,t)?n.methods.one.setTag([e[t]],"Possessive",n,null,"2-contraction"):vl(e,t)},Tl=function(e,t){let n=t.fromText(e.join(" "));return n.compute("id"),n.docs[0]};var xl={contractionTwo:e=>{let{world:t,document:n}=e;n.forEach(((a,r)=>{for(let o=a.length-1;o>=0;o-=1){if(a[o].implicit)continue;let i=null;!0===Dl.test(a[o].normal)&&(i=a[o].normal.split(Dl)[1]);let s=null;Gl.hasOwnProperty(i)&&(s=Gl[i](a,o,t)),s&&(s=Tl(s,e),ml(n,[r,o],s),Hl(n[r],e,o,s.length))}}))}},El={compute:xl,api:function(e){class Contractions extends e{constructor(e,t,n){super(e,t,n),this.viewType="Contraction"}expand(){return this.docs.forEach((e=>{let t=gl.test(e[0].text);e.forEach(((t,n)=>{t.text=t.implicit||"",delete t.implicit,n<e.length-1&&""===t.post&&(t.post+=" "),t.dirty=!0})),t&&(e[0].text=function(e=""){return e.replace(/^ *[a-z\u00C0-\u00FF]/,(e=>e.toUpperCase()))}(e[0].text))})),this.compute("normal"),this}}e.prototype.contractions=function(){let e=this.match("@hasContraction+");return new Contractions(this.document,e.pointer)},e.prototype.contract=hl},hooks:["contractionTwo"]};const Fl="(hard|fast|late|early|high|right|deep|close|direct)";const Ol="(i|we|they)";let zl=[].concat([{match:"(got|were|was|is|are|am) (#PastTense|#Participle)",tag:"Passive",reason:"got-walked"},{match:"(was|were|is|are|am) being (#PastTense|#Participle)",tag:"Passive",reason:"was-being"},{match:"(had|have|has) been (#PastTense|#Participle)",tag:"Passive",reason:"had-been"},{match:"will be being? (#PastTense|#Participle)",tag:"Passive",reason:"will-be-cleaned"},{match:"#Noun [(#PastTense|#Participle)] by (the|a) #Noun",group:0,tag:"Passive",reason:"suffered-by"}],[{match:"[(all|both)] #Determiner #Noun",group:0,tag:"Noun",reason:"all-noun"},{match:"#Copula [(just|alone)]$",group:0,tag:"Adjective",reason:"not-adverb"},{match:"#Singular is #Adverb? [#PastTense$]",group:0,tag:"Adjective",reason:"is-filled"},{match:"[#PastTense] #Singular is",group:0,tag:"Adjective",reason:"smoked-poutine"},{match:"[#PastTense] #Plural are",group:0,tag:"Adjective",reason:"baked-onions"},{match:"well [#PastTense]",group:0,tag:"Adjective",reason:"well-made"},{match:"#Copula [fucked up?]",group:0,tag:"Adjective",reason:"swears-adjective"},{match:"#Singular (seems|appears) #Adverb? [#PastTense$]",group:0,tag:"Adjective",reason:"seems-filled"},{match:"#Copula #Adjective? [(out|in|through)]$",group:0,tag:"Adjective",reason:"still-out"},{match:"^[#Adjective] (the|your) #Noun",group:0,notIf:"(all|even)",tag:"Infinitive",reason:"shut-the"},{match:"the [said] #Noun",group:0,tag:"Adjective",reason:"the-said-card"},{match:"[#Hyphenated (#Hyphenated && #PastTense)] (#Noun|#Conjunction)",group:0,tag:"Adjective",notIf:"#Adverb",reason:"faith-based"},{match:"[#Hyphenated (#Hyphenated && #Gerund)] (#Noun|#Conjunction)",group:0,tag:"Adjective",notIf:"#Adverb",reason:"self-driving"},{match:"[#PastTense (#Hyphenated && #PhrasalVerb)] (#Noun|#Conjunction)",group:0,tag:"Adjective",reason:"dammed-up"},{match:"(#Hyphenated && #Value) fold",tag:"Adjective",reason:"two-fold"},{match:"must (#Hyphenated && #Infinitive)",tag:"Adjective",reason:"must-win"},{match:"(#Hyphenated && #Infinitive) #Hyphenated",tag:"Adjective",notIf:"#PhrasalVerb",reason:"vacuum-sealed"},{match:"too much",tag:"Adverb Adjective",reason:"bit-4"},{match:"a bit much",tag:"Determiner Adverb Adjective",reason:"bit-3"},{match:"[(un|contra|extra|inter|intra|macro|micro|mid|mis|mono|multi|pre|sub|tri|ex)] #Adjective",group:0,tag:["Adjective","Prefix"],reason:"un-skilled"}],[{match:"#Adverb [#Adverb] (and|or|then)",group:0,tag:"Adjective",reason:"kinda-sparkly-and"},{match:"[(dark|bright|flat|light|soft|pale|dead|dim|faux|little|wee|sheer|most|near|good|extra|all)] #Adjective",group:0,tag:"Adverb",reason:"dark-green"},{match:"#Copula [far too] #Adjective",group:0,tag:"Adverb",reason:"far-too"},{match:"#Copula [still] (in|#Gerund|#Adjective)",group:0,tag:"Adverb",reason:"was-still-walking"},{match:`#Plural ${Fl}`,tag:"#PresentTense #Adverb",reason:"studies-hard"},{match:`#Verb [${Fl}] !#Noun?`,group:0,notIf:"(#Copula|get|got|getting|become|became|becoming|feel|feels|feeling|#Determiner|#Preposition)",tag:"Adverb",reason:"shops-direct"},{match:"[#Plural] a lot",tag:"PresentTense",reason:"studies-a-lot"}],[{match:"as [#Gerund] as",group:0,tag:"Adjective",reason:"as-gerund-as"},{match:"more [#Gerund] than",group:0,tag:"Adjective",reason:"more-gerund-than"},{match:"(so|very|extremely) [#Gerund]",group:0,tag:"Adjective",reason:"so-gerund"},{match:"(found|found) it #Adverb? [#Gerund]",group:0,tag:"Adjective",reason:"found-it-gerund"},{match:"a (little|bit|wee) bit? [#Gerund]",group:0,tag:"Adjective",reason:"a-bit-gerund"},{match:"#Gerund [#Gerund]",group:0,tag:"Adjective",notIf:"(impersonating|practicing|considering|assuming)",reason:"looking-annoying"},{match:"(looked|look|looks) #Adverb? [%Adj|Gerund%]",group:0,tag:"Adjective",notIf:"(impersonating|practicing|considering|assuming)",reason:"looked-amazing"},{match:"[%Adj|Gerund%] #Determiner",group:0,tag:"Gerund",reason:"developing-a"},{match:"#Possessive [%Adj|Gerund%] #Noun",group:0,tag:"Adjective",reason:"leading-manufacturer"},{match:"%Noun|Gerund% %Adj|Gerund%",tag:"Gerund #Adjective",reason:"meaning-alluring"},{match:"(face|embrace|reveal|stop|start|resume) %Adj|Gerund%",tag:"#PresentTense #Adjective",reason:"face-shocking"},{match:"(are|were) [%Adj|Gerund%] #Plural",group:0,tag:"Adjective",reason:"are-enduring-symbols"}],[{match:"#Determiner [#Adjective] #Copula",group:0,tag:"Noun",reason:"the-adj-is"},{match:"#Adjective [#Adjective] #Copula",group:0,tag:"Noun",reason:"adj-adj-is"},{match:"(his|its) [%Adj|Noun%]",group:0,tag:"Noun",notIf:"#Hyphenated",reason:"his-fine"},{match:"#Copula #Adverb? [all]",group:0,tag:"Noun",reason:"is-all"},{match:"(have|had) [#Adjective] #Preposition .",group:0,tag:"Noun",reason:"have-fun"},{match:"#Gerund (giant|capital|center|zone|application)",tag:"Noun",reason:"brewing-giant"},{match:"#Preposition (a|an) [#Adjective]$",group:0,tag:"Noun",reason:"an-instant"},{match:"no [#Adjective] #Modal",group:0,tag:"Noun",reason:"no-golden"},{match:"[brand #Gerund?] new",group:0,tag:"Adverb",reason:"brand-new"},{match:"(#Determiner|#Comparative|new|different) [kind]",group:0,tag:"Noun",reason:"some-kind"},{match:"#Possessive [%Adj|Noun%] #Noun",group:0,tag:"Adjective",reason:"her-favourite"},{match:"must && #Hyphenated .",tag:"Adjective",reason:"must-win"},{match:"#Determiner [#Adjective]$",tag:"Noun",notIf:"(this|that|#Comparative|#Superlative)",reason:"the-south"},{match:"(#Noun && #Hyphenated) (#Adjective && #Hyphenated)",tag:"Adjective",notIf:"(this|that|#Comparative|#Superlative)",reason:"company-wide"},{match:"#Determiner [#Adjective] (#Copula|#Determiner)",notIf:"(#Comparative|#Superlative)",group:0,tag:"Noun",reason:"the-poor"},{match:"[%Adj|Noun%] #Noun",notIf:"(#Pronoun|#ProperNoun)",group:0,tag:"Adjective",reason:"stable-foundations"}],[{match:"[still] #Adjective",group:0,tag:"Adverb",reason:"still-advb"},{match:"[still] #Verb",group:0,tag:"Adverb",reason:"still-verb"},{match:"[so] #Adjective",group:0,tag:"Adverb",reason:"so-adv"},{match:"[way] #Comparative",group:0,tag:"Adverb",reason:"way-adj"},{match:"[way] #Adverb #Adjective",group:0,tag:"Adverb",reason:"way-too-adj"},{match:"[all] #Verb",group:0,tag:"Adverb",reason:"all-verb"},{match:"#Verb  [like]",group:0,notIf:"(#Modal|#PhrasalVerb)",tag:"Adverb",reason:"verb-like"},{match:"(barely|hardly) even",tag:"Adverb",reason:"barely-even"},{match:"[even] #Verb",group:0,tag:"Adverb",reason:"even-walk"},{match:"[even] #Comparative",group:0,tag:"Adverb",reason:"even-worse"},{match:"[even] (#Determiner|#Possessive)",group:0,tag:"#Adverb",reason:"even-the"},{match:"even left",tag:"#Adverb #Verb",reason:"even-left"},{match:"[way] #Adjective",group:0,tag:"#Adverb",reason:"way-over"},{match:"#PresentTense [(hard|quick|bright|slow|fast|backwards|forwards)]",notIf:"#Copula",group:0,tag:"Adverb",reason:"lazy-ly"},{match:"[much] #Adjective",group:0,tag:"Adverb",reason:"bit-1"},{match:"#Copula [#Adverb]$",group:0,tag:"Adjective",reason:"is-well"},{match:"a [(little|bit|wee) bit?] #Adjective",group:0,tag:"Adverb",reason:"a-bit-cold"},{match:"[(super|pretty)] #Adjective",group:0,tag:"Adverb",reason:"super-strong"},{match:"(become|fall|grow) #Adverb? [#PastTense]",group:0,tag:"Adjective",reason:"overly-weakened"},{match:"(a|an) #Adverb [#Participle] #Noun",group:0,tag:"Adjective",reason:"completely-beaten"},{match:"#Determiner #Adverb? [close]",group:0,tag:"Adjective",reason:"a-close"},{match:"#Gerund #Adverb? [close]",group:0,tag:"Adverb",notIf:"(getting|becoming|feeling)",reason:"being-close"},{match:"(the|those|these|a|an) [#Participle] #Noun",group:0,tag:"Adjective",reason:"blown-motor"},{match:"(#PresentTense|#PastTense) [back]",group:0,tag:"Adverb",notIf:"(#PhrasalVerb|#Copula)",reason:"charge-back"},{match:"#Verb [around]",group:0,tag:"Adverb",notIf:"#PhrasalVerb",reason:"send-around"},{match:"[later] #PresentTense",group:0,tag:"Adverb",reason:"later-say"},{match:"#Determiner [well] !#PastTense?",group:0,tag:"Noun",reason:"the-well"},{match:"#Adjective [enough]",group:0,tag:"Adverb",reason:"high-enough"}],[{match:"[sun] the #Ordinal",tag:"WeekDay",reason:"sun-the-5th"},{match:"[sun] #Date",group:0,tag:"WeekDay",reason:"sun-feb"},{match:"#Date (on|this|next|last|during)? [sun]",group:0,tag:"WeekDay",reason:"1pm-sun"},{match:"(in|by|before|during|on|until|after|of|within|all) [sat]",group:0,tag:"WeekDay",reason:"sat"},{match:"(in|by|before|during|on|until|after|of|within|all) [wed]",group:0,tag:"WeekDay",reason:"wed"},{match:"(in|by|before|during|on|until|after|of|within|all) [march]",group:0,tag:"Month",reason:"march"},{match:"[sat] #Date",group:0,tag:"WeekDay",reason:"sat-feb"},{match:"#Preposition [(march|may)]",group:0,tag:"Month",reason:"in-month"},{match:"(this|next|last) (march|may) !#Infinitive?",tag:"#Date #Month",reason:"this-month"},{match:"(march|may) the? #Value",tag:"#Month #Date #Date",reason:"march-5th"},{match:"#Value of? (march|may)",tag:"#Date #Date #Month",reason:"5th-of-march"},{match:"[(march|may)] .? #Date",group:0,tag:"Month",reason:"march-and-feb"},{match:"#Date .? [(march|may)]",group:0,tag:"Month",reason:"feb-and-march"},{match:"#Adverb [(march|may)]",group:0,tag:"Verb",reason:"quickly-march"},{match:"[(march|may)] #Adverb",group:0,tag:"Verb",reason:"march-quickly"},{match:"#Value (am|pm)",tag:"Time",reason:"2-am"}],[{match:"#Holiday (day|eve)",tag:"Holiday",reason:"holiday-day"},{match:"#Value of #Month",tag:"Date",reason:"value-of-month"},{match:"#Cardinal #Month",tag:"Date",reason:"cardinal-month"},{match:"#Month #Value to #Value",tag:"Date",reason:"value-to-value"},{match:"#Month the #Value",tag:"Date",reason:"month-the-value"},{match:"(#WeekDay|#Month) #Value",tag:"Date",reason:"date-value"},{match:"#Value (#WeekDay|#Month)",tag:"Date",reason:"value-date"},{match:"(#TextValue && #Date) #TextValue",tag:"Date",reason:"textvalue-date"},{match:"#Month #NumberRange",tag:"Date",reason:"aug 20-21"},{match:"#WeekDay #Month #Ordinal",tag:"Date",reason:"week mm-dd"},{match:"#Month #Ordinal #Cardinal",tag:"Date",reason:"mm-dd-yyy"},{match:"(#Place|#Demonmym|#Time) (standard|daylight|central|mountain)? time",tag:"Timezone",reason:"std-time"},{match:"(eastern|mountain|pacific|central|atlantic) (standard|daylight|summer)? time",tag:"Timezone",reason:"eastern-time"},{match:"#Time [(eastern|mountain|pacific|central|est|pst|gmt)]",group:0,tag:"Timezone",reason:"5pm-central"},{match:"(central|western|eastern) european time",tag:"Timezone",reason:"cet"}],[{match:"(the|any) [more]",group:0,tag:"Singular",reason:"more-noun"},{match:"[more] #Noun",group:0,tag:"Adjective",reason:"more-noun"},{match:"(right|rights) of .",tag:"Noun",reason:"right-of"},{match:"a [bit]",group:0,tag:"Singular",reason:"bit-2"},{match:"a [must]",group:0,tag:"Singular",reason:"must-2"},{match:"(we|us) [all]",group:0,tag:"Noun",reason:"we all"},{match:"due to [#Verb]",group:0,tag:"Noun",reason:"due-to"},{match:"some [#Verb] #Plural",group:0,tag:"Noun",reason:"determiner6"},{match:"#Possessive #Ordinal [#PastTense]",group:0,tag:"Noun",reason:"first-thought"},{match:"(the|this|those|these) #Adjective [%Verb|Noun%]",group:0,tag:"Noun",notIf:"#Copula",reason:"the-adj-verb"},{match:"(the|this|those|these) #Adverb #Adjective [#Verb]",group:0,tag:"Noun",reason:"determiner4"},{match:"the [#Verb] #Preposition .",group:0,tag:"Noun",reason:"determiner1"},{match:"(a|an|the) [#Verb] of",group:0,tag:"Noun",reason:"the-verb-of"},{match:"#Determiner #Noun of [#Verb]",group:0,tag:"Noun",notIf:"#Gerund",reason:"noun-of-noun"},{match:"#PastTense #Preposition [#PresentTense]",group:0,notIf:"#Gerund",tag:"Noun",reason:"ended-in-ruins"},{match:"#Conjunction [u]",group:0,tag:"Pronoun",reason:"u-pronoun-2"},{match:"[u] #Verb",group:0,tag:"Pronoun",reason:"u-pronoun-1"},{match:"#Determiner [(western|eastern|northern|southern|central)] #Noun",group:0,tag:"Noun",reason:"western-line"},{match:"(#Singular && @hasHyphen) #PresentTense",tag:"Noun",reason:"hyphen-verb"},{match:"is no [#Verb]",group:0,tag:"Noun",reason:"is-no-verb"},{match:"do [so]",group:0,tag:"Noun",reason:"so-noun"},{match:"#Determiner [(shit|damn|hell)]",group:0,tag:"Noun",reason:"swears-noun"},{match:"to [(shit|hell)]",group:0,tag:"Noun",reason:"to-swears"},{match:"(the|these) [#Singular] (were|are)",group:0,tag:"Plural",reason:"singular-were"},{match:"a #Noun+ or #Adverb+? [#Verb]",group:0,tag:"Noun",reason:"noun-or-noun"},{match:"(the|those|these|a|an) #Adjective? [#PresentTense #Particle?]",group:0,tag:"Noun",notIf:"(seem|appear|include|#Gerund|#Copula)",reason:"det-inf"},{match:"#Noun #Actor",tag:"Actor",notIf:"(#Person|#Pronoun)",reason:"thing-doer"},{match:"#Gerund #Actor",tag:"Actor",reason:"gerund-doer"},{match:"co #Singular",tag:"Actor",reason:"co-noun"},{match:"[#Noun+] #Actor",group:0,tag:"Actor",notIf:"(#Honorific|#Pronoun|#Possessive)",reason:"air-traffic-controller"},{match:"(urban|cardiac|cardiovascular|respiratory|medical|clinical|visual|graphic|creative|dental|exotic|fine|certified|registered|technical|virtual|professional|amateur|junior|senior|special|pharmaceutical|theoretical)+ #Noun? #Actor",tag:"Actor",reason:"fine-artist"},{match:"#Noun+ (coach|chef|king|engineer|fellow|personality|boy|girl|man|woman|master)",tag:"Actor",reason:"dance-coach"},{match:"chief . officer",tag:"Actor",reason:"chief-x-officer"},{match:"chief of #Noun+",tag:"Actor",reason:"chief-of-police"},{match:"senior? vice? president of #Noun+",tag:"Actor",reason:"president-of"},{match:"#Determiner [sun]",group:0,tag:"Singular",reason:"the-sun"},{match:"#Verb (a|an) [#Value]$",group:0,tag:"Singular",reason:"did-a-value"},{match:"the [(can|will|may)]",group:0,tag:"Singular",reason:"the can"},{match:"#FirstName #Acronym? (#Possessive && #LastName)",tag:"Possessive",reason:"name-poss"},{match:"#Organization+ #Possessive",tag:"Possessive",reason:"org-possessive"},{match:"#Place+ #Possessive",tag:"Possessive",reason:"place-possessive"},{match:"#Possessive #PresentTense #Particle?",notIf:"(#Gerund|her)",tag:"Noun",reason:"possessive-verb"},{match:"(my|our|their|her|his|its) [(#Plural && #Actor)] #Noun",tag:"Possessive",reason:"my-dads"},{match:"#Value of a [second]",group:0,unTag:"Value",tag:"Singular",reason:"10th-of-a-second"},{match:"#Value [seconds]",group:0,unTag:"Value",tag:"Plural",reason:"10-seconds"},{match:"in [#Infinitive]",group:0,tag:"Singular",reason:"in-age"},{match:"a [#Adjective] #Preposition",group:0,tag:"Noun",reason:"a-minor-in"},{match:"#Determiner [#Singular] said",group:0,tag:"Actor",reason:"the-actor-said"},{match:"#Determiner #Noun [(feel|sense|process|rush|side|bomb|bully|challenge|cover|crush|dump|exchange|flow|function|issue|lecture|limit|march|process)] !(#Preposition|to|#Adverb)?",group:0,tag:"Noun",reason:"the-noun-sense"},{match:"[#PresentTense] (of|by|for) (a|an|the) #Noun #Copula",group:0,tag:"Plural",reason:"photographs-of"},{match:"#Infinitive and [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"fight and win"},{match:"#Noun and [#Verb] and #Noun",group:0,tag:"Noun",reason:"peace-and-flowers"},{match:"the #Cardinal [%Adj|Noun%]",group:0,tag:"Noun",reason:"the-1992-classic"},{match:"#Copula the [%Adj|Noun%] #Noun",group:0,tag:"Adjective",reason:"the-premier-university"},{match:"i #Verb [me] #Noun",group:0,tag:"Possessive",reason:"scottish-me"},{match:"[#PresentTense] (music|class|lesson|night|party|festival|league|ceremony)",group:0,tag:"Noun",reason:"dance-music"},{match:"[wit] (me|it)",group:0,tag:"Presposition",reason:"wit-me"},{match:"#PastTense #Possessive [#Verb]",group:0,tag:"Noun",notIf:"(saw|made)",reason:"left-her-boots"},{match:"#Value [%Plural|Verb%]",group:0,tag:"Plural",notIf:"(one|1|a|an)",reason:"35-signs"},{match:"had [#PresentTense]",group:0,tag:"Noun",notIf:"(#Gerund|come|become)",reason:"had-time"},{match:"%Adj|Noun% %Noun|Verb%",tag:"#Adjective #Noun",notIf:"#ProperNoun #Noun",reason:"instant-access"},{match:"#Determiner [%Adj|Noun%] #Conjunction",group:0,tag:"Noun",reason:"a-rep-to"},{match:"#Adjective #Noun [%Plural|Verb%]$",group:0,tag:"Plural",notIf:"#Pronoun",reason:"near-death-experiences"},{match:"#Possessive #Noun [%Plural|Verb%]$",group:0,tag:"Plural",reason:"your-guild-colors"}],[{match:"(this|that|the|a|an) [#Gerund #Infinitive]",group:0,tag:"Singular",reason:"the-planning-process"},{match:"(that|the) [#Gerund #PresentTense]",group:0,ifNo:"#Copula",tag:"Plural",reason:"the-paving-stones"},{match:"#Determiner [#Gerund] #Noun",group:0,tag:"Adjective",reason:"the-gerund-noun"},{match:"#Pronoun #Infinitive [#Gerund] #PresentTense",group:0,tag:"Noun",reason:"tipping-sucks"},{match:"#Adjective [#Gerund]",group:0,tag:"Noun",notIf:"(still|even|just)",reason:"early-warning"},{match:"[#Gerund] #Adverb? not? #Copula",group:0,tag:"Activity",reason:"gerund-copula"},{match:"#Copula [(#Gerund|#Activity)] #Copula",group:0,tag:"Gerund",reason:"are-doing-is"},{match:"[#Gerund] #Modal",group:0,tag:"Activity",reason:"gerund-modal"},{match:"#Singular for [%Noun|Gerund%]",group:0,tag:"Gerund",reason:"noun-for-gerund"},{match:"#Comparative (for|at) [%Noun|Gerund%]",group:0,tag:"Gerund",reason:"better-for-gerund"},{match:"#PresentTense the [#Gerund]",group:0,tag:"Noun",reason:"keep-the-touching"}],[{match:"#Infinitive (this|that|the) [#Infinitive]",group:0,tag:"Noun",reason:"do-this-dance"},{match:"#Gerund #Determiner [#Infinitive]",group:0,tag:"Noun",reason:"running-a-show"},{match:"#Determiner (only|further|just|more|backward) [#Infinitive]",group:0,tag:"Noun",reason:"the-only-reason"},{match:"(the|this|a|an) [#Infinitive] #Adverb? #Verb",group:0,tag:"Noun",reason:"determiner5"},{match:"#Determiner #Adjective #Adjective? [#Infinitive]",group:0,tag:"Noun",reason:"a-nice-inf"},{match:"#Determiner #Demonym [#PresentTense]",group:0,tag:"Noun",reason:"mexican-train"},{match:"#Adjective #Noun+ [#Infinitive] #Copula",group:0,tag:"Noun",reason:"career-move"},{match:"at some [#Infinitive]",group:0,tag:"Noun",reason:"at-some-inf"},{match:"(go|goes|went) to [#Infinitive]",group:0,tag:"Noun",reason:"goes-to-verb"},{match:"(a|an) #Adjective? #Noun [#Infinitive] (#Preposition|#Noun)",group:0,notIf:"from",tag:"Noun",reason:"a-noun-inf"},{match:"(a|an) #Noun [#Infinitive]$",group:0,tag:"Noun",reason:"a-noun-inf2"},{match:"#Gerund #Adjective? for [#Infinitive]",group:0,tag:"Noun",reason:"running-for"},{match:"about [#Infinitive]",group:0,tag:"Singular",reason:"about-love"},{match:"#Plural on [#Infinitive]",group:0,tag:"Noun",reason:"on-stage"},{match:"any [#Infinitive]",group:0,tag:"Noun",reason:"any-charge"},{match:"no [#Infinitive]",group:0,tag:"Noun",reason:"no-doubt"},{match:"number of [#PresentTense]",group:0,tag:"Noun",reason:"number-of-x"},{match:"(taught|teaches|learns|learned) [#PresentTense]",group:0,tag:"Noun",reason:"teaches-x"},{match:"(try|use|attempt|build|make) [#Verb #Particle?]",notIf:"(#Copula|#Noun|sure|fun|up)",group:0,tag:"Noun",reason:"do-verb"},{match:"^[#Infinitive] (is|was)",group:0,tag:"Noun",reason:"checkmate-is"},{match:"#Infinitive much [#Infinitive]",group:0,tag:"Noun",reason:"get-much"},{match:"[cause] #Pronoun #Verb",group:0,tag:"Conjunction",reason:"cause-cuz"},{match:"the #Singular [#Infinitive] #Noun",group:0,tag:"Noun",notIf:"#Pronoun",reason:"cardio-dance"},{match:"#Determiner #Modal [#Noun]",group:0,tag:"PresentTense",reason:"should-smoke"},{match:"this [#Plural]",group:0,tag:"PresentTense",notIf:"(#Preposition|#Date)",reason:"this-verbs"},{match:"#Noun that [#Plural]",group:0,tag:"PresentTense",notIf:"(#Preposition|#Pronoun|way)",reason:"voice-that-rocks"},{match:"that [#Plural] to",group:0,tag:"PresentTense",notIf:"#Preposition",reason:"that-leads-to"},{match:"(let|make|made) (him|her|it|#Person|#Place|#Organization)+ [#Singular] (a|an|the|it)",group:0,tag:"Infinitive",reason:"let-him-glue"},{match:"#Verb (all|every|each|most|some|no) [#PresentTense]",notIf:"#Modal",group:0,tag:"Noun",reason:"all-presentTense"},{match:"(had|have|#PastTense) #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"better",reason:"adj-presentTense"},{match:"#Value #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"one-big-reason"},{match:"#PastTense #Adjective+ [#PresentTense]",group:0,tag:"Noun",notIf:"(#Copula|better)",reason:"won-wide-support"},{match:"(many|few|several|couple) [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"many-poses"},{match:"#Determiner #Adverb #Adjective [%Noun|Verb%]",group:0,tag:"Noun",notIf:"#Copula",reason:"very-big-dream"},{match:"from #Noun to [%Noun|Verb%]",group:0,tag:"Noun",reason:"start-to-finish"},{match:"(for|with|of) #Noun (and|or|not) [%Noun|Verb%]",group:0,tag:"Noun",notIf:"#Pronoun",reason:"for-food-and-gas"},{match:"#Adjective #Adjective [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"adorable-little-store"},{match:"#Gerund #Adverb? #Comparative [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"higher-costs"},{match:"(#Noun && @hasComma) #Noun (and|or) [#PresentTense]",group:0,tag:"Noun",notIf:"#Copula",reason:"noun-list"},{match:"(many|any|some|several) [#PresentTense] for",group:0,tag:"Noun",reason:"any-verbs-for"},{match:"to #PresentTense #Noun [#PresentTense] #Preposition",group:0,tag:"Noun",reason:"gas-exchange"},{match:"#PastTense (until|as|through|without) [#PresentTense]",group:0,tag:"Noun",reason:"waited-until-release"},{match:"#Gerund like #Adjective? [#PresentTense]",group:0,tag:"Plural",reason:"like-hot-cakes"},{match:"some #Adjective [#PresentTense]",group:0,tag:"Noun",reason:"some-reason"},{match:"for some [#PresentTense]",group:0,tag:"Noun",reason:"for-some-reason"},{match:"(same|some|the|that|a) kind of [#PresentTense]",group:0,tag:"Noun",reason:"some-kind-of"},{match:"(same|some|the|that|a) type of [#PresentTense]",group:0,tag:"Noun",reason:"some-type-of"},{match:"#Gerund #Adjective #Preposition [#PresentTense]",group:0,tag:"Noun",reason:"doing-better-for-x"},{match:"(get|got|have) #Comparative [#PresentTense]",group:0,tag:"Noun",reason:"got-better-aim"},{match:"whose [#PresentTense] #Copula",group:0,tag:"Noun",reason:"whos-name-was"},{match:"#PhrasalVerb #Particle #Preposition [#PresentTense]",group:0,tag:"Noun",reason:"given-up-on-x"},{match:"there (are|were) #Adjective? [#PresentTense]",group:0,tag:"Plural",reason:"there-are"},{match:"#Value [#PresentTense] of",group:0,notIf:"(one|1|#Copula|#Infinitive)",tag:"Plural",reason:"2-trains"},{match:"[#PresentTense] (are|were) #Adjective",group:0,tag:"Plural",reason:"compromises-are-possible"},{match:"^[(hope|guess|thought|think)] #Pronoun #Verb",group:0,tag:"Infinitive",reason:"suppose-i"},{match:"#Possessive #Adjective [#Verb]",group:0,tag:"Noun",notIf:"#Copula",reason:"our-full-support"},{match:"[(tastes|smells)] #Adverb? #Adjective",group:0,tag:"PresentTense",reason:"tastes-good"},{match:"#Copula #Gerund [#PresentTense] !by?",group:0,tag:"Noun",notIf:"going",reason:"ignoring-commute"},{match:"#Determiner #Adjective? [(shed|thought|rose|bid|saw|spelt)]",group:0,tag:"Noun",reason:"noun-past"},{match:"how to [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"how-to-noun"},{match:"which [%Noun|Verb%] #Noun",group:0,tag:"Infinitive",reason:"which-boost-it"},{match:"#Gerund [%Plural|Verb%]",group:0,tag:"Plural",reason:"asking-questions"},{match:"(ready|available|difficult|hard|easy|made|attempt|try) to [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"ready-to-noun"},{match:"(bring|went|go|drive|run|bike) to [%Noun|Verb%]",group:0,tag:"Noun",reason:"bring-to-noun"},{match:"#Modal #Noun [%Noun|Verb%]",group:0,tag:"Infinitive",reason:"would-you-look"},{match:"#Copula just [#Infinitive]",group:0,tag:"Noun",reason:"is-just-spam"},{match:"^%Noun|Verb% %Plural|Verb%",tag:"Imperative #Plural",reason:"request-copies"},{match:"#Adjective #Plural and [%Plural|Verb%]",group:0,tag:"#Plural",reason:"pickles-and-drinks"},{match:"#Determiner #Year [#Verb]",group:0,tag:"Noun",reason:"the-1968-film"},{match:"#Determiner [#PhrasalVerb #Particle]",group:0,tag:"Noun",reason:"the-break-up"},{match:"#Determiner [%Adj|Noun%] #Noun",group:0,tag:"Adjective",notIf:"(#Pronoun|#Possessive|#ProperNoun)",reason:"the-individual-goals"},{match:"[%Noun|Verb%] or #Infinitive",group:0,tag:"Infinitive",reason:"work-or-prepare"},{match:"to #Infinitive [#PresentTense]",group:0,tag:"Noun",notIf:"(#Gerund|#Copula|help)",reason:"to-give-thanks"},{match:"[#Noun] me",group:0,tag:"Verb",reason:"kills-me"},{match:"%Plural|Verb% %Plural|Verb%",tag:"#PresentTense #Plural",reason:"removes-wrinkles"}],[{match:"#Money and #Money #Currency?",tag:"Money",reason:"money-and-money"},{match:"#Value #Currency [and] #Value (cents|ore|centavos|sens)",group:0,tag:"money",reason:"and-5-cents"},{match:"#Value (mark|rand|won|rub|ore)",tag:"#Money #Currency",reason:"4-mark"},{match:"a pound",tag:"#Money #Unit",reason:"a-pound"},{match:"#Value (pound|pounds)",tag:"#Money #Unit",reason:"4-pounds"}],[{match:"[(half|quarter)] of? (a|an)",group:0,tag:"Fraction",reason:"millionth"},{match:"#Adverb [half]",group:0,tag:"Fraction",reason:"nearly-half"},{match:"[half] the",group:0,tag:"Fraction",reason:"half-the"},{match:"#Cardinal and a half",tag:"Fraction",reason:"and-a-half"},{match:"#Value (halves|halfs|quarters)",tag:"Fraction",reason:"two-halves"},{match:"a #Ordinal",tag:"Fraction",reason:"a-quarter"},{match:"[#Cardinal+] (#Fraction && /s$/)",tag:"Fraction",reason:"seven-fifths"},{match:"[#Cardinal+ #Ordinal] of .",group:0,tag:"Fraction",reason:"ordinal-of"},{match:"[(#NumericValue && #Ordinal)] of .",group:0,tag:"Fraction",reason:"num-ordinal-of"},{match:"(a|one) #Cardinal?+ #Ordinal",tag:"Fraction",reason:"a-ordinal"},{match:"#Cardinal+ out? of every? #Cardinal",tag:"Fraction",reason:"out-of"}],[{match:"#Cardinal [second]",tag:"Unit",reason:"one-second"},{match:"!once? [(a|an)] (#Duration|hundred|thousand|million|billion|trillion)",group:0,tag:"Value",reason:"a-is-one"},{match:"1 #Value #PhoneNumber",tag:"PhoneNumber",reason:"1-800-Value"},{match:"#NumericValue #PhoneNumber",tag:"PhoneNumber",reason:"(800) PhoneNumber"},{match:"#Demonym #Currency",tag:"Currency",reason:"demonym-currency"},{match:"#Value [(buck|bucks|grand)]",group:0,tag:"Currency",reason:"value-bucks"},{match:"[#Value+] #Currency",group:0,tag:"Money",reason:"15 usd"},{match:"[second] #Noun",group:0,tag:"Ordinal",reason:"second-noun"},{match:"#Value+ [#Currency]",group:0,tag:"Unit",reason:"5-yan"},{match:"#Value [(foot|feet)]",group:0,tag:"Unit",reason:"foot-unit"},{match:"#Value [#Abbreviation]",group:0,tag:"Unit",reason:"value-abbr"},{match:"#Value [k]",group:0,tag:"Unit",reason:"value-k"},{match:"#Unit an hour",tag:"Unit",reason:"unit-an-hour"},{match:"(minus|negative) #Value",tag:"Value",reason:"minus-value"},{match:"#Value (point|decimal) #Value",tag:"Value",reason:"value-point-value"},{match:"#Determiner [(half|quarter)] #Ordinal",group:0,tag:"Value",reason:"half-ordinal"},{match:"#Multiple+ and #Value",tag:"Value",reason:"magnitude-and-value"},{match:"#Value #Unit [(per|an) (hr|hour|sec|second|min|minute)]",group:0,tag:"Unit",reason:"12-miles-per-second"},{match:"#Value [(square|cubic)] #Unit",group:0,tag:"Unit",reason:"square-miles"}],[{match:"#Copula [(#Noun|#PresentTense)] #LastName",group:0,tag:"FirstName",reason:"copula-noun-lastname"},{match:"(sister|pope|brother|father|aunt|uncle|grandpa|grandfather|grandma) #ProperNoun",tag:"Person",reason:"lady-titlecase",safe:!0},{match:"#FirstName [#Determiner #Noun] #LastName",group:0,tag:"Person",reason:"first-noun-last"},{match:"#ProperNoun (b|c|d|e|f|g|h|j|k|l|m|n|o|p|q|r|s|t|u|v|w|x|y|z) #ProperNoun",tag:"Person",reason:"titlecase-acronym-titlecase",safe:!0},{match:"#Acronym #LastName",tag:"Person",reason:"acronym-lastname",safe:!0},{match:"#Person (jr|sr|md)",tag:"Person",reason:"person-honorific"},{match:"#Honorific #Acronym",tag:"Person",reason:"Honorific-TitleCase"},{match:"#Person #Person the? #RomanNumeral",tag:"Person",reason:"roman-numeral"},{match:"#FirstName [/^[^aiurck]$/]",group:0,tag:["Acronym","Person"],reason:"john-e"},{match:"#Noun van der? #Noun",tag:"Person",reason:"van der noun",safe:!0},{match:"(king|queen|prince|saint|lady) of #Noun",tag:"Person",reason:"king-of-noun",safe:!0},{match:"(prince|lady) #Place",tag:"Person",reason:"lady-place"},{match:"(king|queen|prince|saint) #ProperNoun",tag:"Person",notIf:"#Place",reason:"saint-foo"},{match:"al (#Person|#ProperNoun)",tag:"Person",reason:"al-borlen",safe:!0},{match:"#FirstName de #Noun",tag:"Person",reason:"bill-de-noun"},{match:"#FirstName (bin|al) #Noun",tag:"Person",reason:"bill-al-noun"},{match:"#FirstName #Acronym #ProperNoun",tag:"Person",reason:"bill-acronym-title"},{match:"#FirstName #FirstName #ProperNoun",tag:"Person",reason:"bill-firstname-title"},{match:"#Honorific #FirstName? #ProperNoun",tag:"Person",reason:"dr-john-Title"},{match:"#FirstName the #Adjective",tag:"Person",reason:"name-the-great"},{match:"#ProperNoun (van|al|bin) #ProperNoun",tag:"Person",reason:"title-van-title",safe:!0},{match:"#ProperNoun (de|du) la? #ProperNoun",tag:"Person",notIf:"#Place",reason:"title-de-title"},{match:"#Singular #Acronym #LastName",tag:"#FirstName #Person .",reason:"title-acro-noun",safe:!0},{match:"[#ProperNoun] #Person",group:0,tag:"Person",reason:"proper-person",safe:!0},{match:"#Person [#ProperNoun #ProperNoun]",group:0,tag:"Person",notIf:"#Possessive",reason:"three-name-person",safe:!0},{match:"#FirstName #Acronym? [#ProperNoun]",group:0,tag:"LastName",notIf:"#Possessive",reason:"firstname-titlecase"},{match:"#FirstName [#FirstName]",group:0,tag:"LastName",reason:"firstname-firstname"},{match:"#FirstName #Acronym #Noun",tag:"Person",reason:"n-acro-noun",safe:!0},{match:"#FirstName [(de|di|du|van|von)] #Person",group:0,tag:"LastName",reason:"de-firstname"},{match:"[(lieutenant|corporal|sergeant|captain|qeen|king|admiral|major|colonel|marshal|president|queen|king)+] #ProperNoun",group:0,tag:"Honorific",reason:"seargeant-john"},{match:"[(private|general|major|rear|prime|field|count|miss)] #Honorific? #Person",group:0,tag:["Honorific","Person"],reason:"ambg-honorifics"},{match:"#Honorific #FirstName [#Singular]",group:0,tag:"LastName",notIf:"#Possessive",reason:"dr-john-foo",safe:!0},{match:"[(his|her) (majesty|honour|worship|excellency|honorable)] #Person",group:0,tag:"Honorific",reason:"his-excellency"},{match:"#Honorific #Actor",tag:"Honorific",reason:"Lieutenant colonel"},{match:"(first|second|third|1st|2nd|3rd) #Actor",tag:"Honorific",reason:"first lady"},{match:"#Person #RomanNumeral",tag:"Person",reason:"louis-IV"}],[{match:"#FirstName #Noun$",tag:". #LastName",notIf:"(#Possessive|#Organization|#Place|#Pronoun|@hasTitleCase)",reason:"firstname-noun"},{match:"%Person|Date% #Acronym? #ProperNoun",tag:"Person",reason:"jan-thierson"},{match:"%Person|Noun% #Acronym? #ProperNoun",tag:"Person",reason:"switch-person",safe:!0},{match:"%Person|Noun% #Organization",tag:"Organization",reason:"olive-garden"},{match:"%Person|Verb% #Acronym? #ProperNoun",tag:"Person",reason:"verb-propernoun",ifNo:"#Actor"},{match:"[%Person|Verb%] (will|had|has|said|says|told|did|learned|wants|wanted)",group:0,tag:"Person",reason:"person-said"},{match:"[%Person|Place%] (harbor|harbour|pier|town|city|place|dump|landfill)",group:0,tag:"Place",reason:"sydney-harbour"},{match:"(west|east|north|south) [%Person|Place%]",group:0,tag:"Place",reason:"east-sydney"},{match:"#Modal [%Person|Verb%]",group:0,tag:"Verb",reason:"would-mark"},{match:"#Adverb [%Person|Verb%]",group:0,tag:"Verb",reason:"really-mark"},{match:"[%Person|Verb%] (#Adverb|#Comparative)",group:0,tag:"Verb",reason:"drew-closer"},{match:"%Person|Verb% #Person",tag:"Person",reason:"rob-smith"},{match:"%Person|Verb% #Acronym #ProperNoun",tag:"Person",reason:"rob-a-smith"},{match:"[will] #Verb",group:0,tag:"Modal",reason:"will-verb"},{match:"(will && @isTitleCase) #ProperNoun",tag:"Person",reason:"will-name"},{match:"(#FirstName && !#Possessive) [#Singular] #Verb",group:0,safe:!0,tag:"LastName",reason:"jack-layton"},{match:"^[#Singular] #Person #Verb",group:0,safe:!0,tag:"Person",reason:"sherwood-anderson"},{match:"(a|an) [#Person]$",group:0,unTag:"Person",reason:"a-warhol"}],[{match:"#Copula (pretty|dead|full|well|sure) (#Adjective|#Noun)",tag:"#Copula #Adverb #Adjective",reason:"sometimes-adverb"},{match:"(#Pronoun|#Person) (had|#Adverb)? [better] #PresentTense",group:0,tag:"Modal",reason:"i-better"},{match:"(#Modal|i|they|we|do) not? [like]",group:0,tag:"PresentTense",reason:"modal-like"},{match:"#Noun #Adverb? [left]",group:0,tag:"PastTense",reason:"left-verb"},{match:"will #Adverb? not? #Adverb? [be] #Gerund",group:0,tag:"Copula",reason:"will-be-copula"},{match:"will #Adverb? not? #Adverb? [be] #Adjective",group:0,tag:"Copula",reason:"be-copula"},{match:"[march] (up|down|back|toward)",notIf:"#Date",group:0,tag:"Infinitive",reason:"march-to"},{match:"#Modal [march]",group:0,tag:"Infinitive",reason:"must-march"},{match:"[may] be",group:0,tag:"Verb",reason:"may-be"},{match:"[(subject|subjects|subjected)] to",group:0,tag:"Verb",reason:"subject to"},{match:"[home] to",group:0,tag:"PresentTense",reason:"home to"},{match:"[open] #Determiner",group:0,tag:"Infinitive",reason:"open-the"},{match:"(were|was) being [#PresentTense]",group:0,tag:"PastTense",reason:"was-being"},{match:"(had|has|have) [been /en$/]",group:0,tag:"Auxiliary Participle",reason:"had-been-broken"},{match:"(had|has|have) [been /ed$/]",group:0,tag:"Auxiliary PastTense",reason:"had-been-smoked"},{match:"(had|has) #Adverb? [been] #Adverb? #PastTense",group:0,tag:"Auxiliary",reason:"had-been-adj"},{match:"(had|has) to [#Noun] (#Determiner|#Possessive)",group:0,tag:"Infinitive",reason:"had-to-noun"},{match:"have [#PresentTense]",group:0,tag:"PastTense",notIf:"(come|gotten)",reason:"have-read"},{match:"(does|will|#Modal) that [work]",group:0,tag:"PastTense",reason:"does-that-work"},{match:"[(sound|sounds)] #Adjective",group:0,tag:"PresentTense",reason:"sounds-fun"},{match:"[(look|looks)] #Adjective",group:0,tag:"PresentTense",reason:"looks-good"},{match:"[(start|starts|stop|stops|begin|begins)] #Gerund",group:0,tag:"Verb",reason:"starts-thinking"},{match:"(have|had) read",tag:"Modal #PastTense",reason:"read-read"},{match:"(is|was|were) [(under|over) #PastTense]",group:0,tag:"Adverb Adjective",reason:"was-under-cooked"},{match:"[shit] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear1-verb"},{match:"[damn] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear2-verb"},{match:"[fuck] (#Determiner|#Possessive|them)",group:0,tag:"Verb",reason:"swear3-verb"},{match:"#Plural that %Noun|Verb%",tag:". #Preposition #Infinitive",reason:"jobs-that-work"},{match:"[works] for me",group:0,tag:"PresentTense",reason:"works-for-me"},{match:"as #Pronoun [please]",group:0,tag:"Infinitive",reason:"as-we-please"},{match:"[(co|mis|de|inter|intra|pre|re|un|out|under|over|counter)] #Verb",group:0,tag:["Verb","Prefix"],notIf:"(#Copula|#PhrasalVerb)",reason:"co-write"},{match:"#PastTense and [%Adj|Past%]",group:0,tag:"PastTense",reason:"dressed-and-left"},{match:"[%Adj|Past%] and #PastTense",group:0,tag:"PastTense",reason:"dressed-and-left"},{match:"#Copula #Pronoun [%Adj|Past%]",group:0,tag:"Adjective",reason:"is-he-stoked"},{match:"to [%Noun|Verb%] #Preposition",group:0,tag:"Infinitive",reason:"to-dream-of"}],[{match:"(slowly|quickly) [#Adjective]",group:0,tag:"Verb",reason:"slowly-adj"},{match:"does (#Adverb|not)? [#Adjective]",group:0,tag:"PresentTense",reason:"does-mean"},{match:"[(fine|okay|cool|ok)] by me",group:0,tag:"Adjective",reason:"okay-by-me"},{match:"i (#Adverb|do)? not? [mean]",group:0,tag:"PresentTense",reason:"i-mean"},{match:"will #Adjective",tag:"Auxiliary Infinitive",reason:"will-adj"},{match:"#Pronoun [#Adjective] #Determiner #Adjective? #Noun",group:0,tag:"Verb",reason:"he-adj-the"},{match:"#Copula [%Adj|Present%] to #Verb",group:0,tag:"Verb",reason:"adj-to"},{match:"#Copula [#Adjective] (well|badly|quickly|slowly)",group:0,tag:"Verb",reason:"done-well"},{match:"#Adjective and [#Gerund] !#Preposition?",group:0,tag:"Adjective",reason:"rude-and-x"},{match:"#Copula #Adverb? (over|under) [#PastTense]",group:0,tag:"Adjective",reason:"over-cooked"},{match:"#Copula #Adjective+ (and|or) [#PastTense]$",group:0,tag:"Adjective",reason:"bland-and-overcooked"},{match:"got #Adverb? [#PastTense] of",group:0,tag:"Adjective",reason:"got-tired-of"},{match:"(seem|seems|seemed|appear|appeared|appears|feel|feels|felt|sound|sounds|sounded) (#Adverb|#Adjective)? [#PastTense]",group:0,tag:"Adjective",reason:"felt-loved"},{match:"(seem|feel|seemed|felt) [#PastTense #Particle?]",group:0,tag:"Adjective",reason:"seem-confused"},{match:"a (bit|little|tad) [#PastTense #Particle?]",group:0,tag:"Adjective",reason:"a-bit-confused"},{match:"not be [%Adj|Past% #Particle?]",group:0,tag:"Adjective",reason:"do-not-be-confused"},{match:"#Copula just [%Adj|Past% #Particle?]",group:0,tag:"Adjective",reason:"is-just-right"},{match:"as [#Infinitive] as",group:0,tag:"Adjective",reason:"as-pale-as"},{match:"[%Adj|Past%] and #Adjective",group:0,tag:"Adjective",reason:"faled-and-oppressive"},{match:"or [#PastTense] #Noun",group:0,tag:"Adjective",notIf:"(#Copula|#Pronoun)",reason:"or-heightened-emotion"},{match:"(become|became|becoming|becomes) [#Verb]",group:0,tag:"Adjective",reason:"become-verb"},{match:"#Possessive [#PastTense] #Noun",group:0,tag:"Adjective",reason:"declared-intentions"},{match:"#Copula #Pronoun [%Adj|Present%]",group:0,tag:"Adjective",reason:"is-he-cool"},{match:"#Copula [%Adj|Past%] with",group:0,tag:"Adjective",notIf:"(associated|worn|baked|aged|armed|bound|fried|loaded|mixed|packed|pumped|filled|sealed)",reason:"is-crowded-with"},{match:"#Copula #Adverb? [%Adj|Present%]$",group:0,tag:"Adjective",reason:"was-empty$"}],[{match:"will (#Adverb|not)+? [have] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"will-have-vb"},{match:"[#Copula] (#Adverb|not)+? (#Gerund|#PastTense)",group:0,tag:"Auxiliary",reason:"copula-walking"},{match:"[(#Modal|did)+] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"modal-verb"},{match:"#Modal (#Adverb|not)+? [have] (#Adverb|not)+? [had] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"would-have"},{match:"[(has|had)] (#Adverb|not)+? #PastTense",group:0,tag:"Auxiliary",reason:"had-walked"},{match:"[(do|does|did|will|have|had|has|got)] (not|#Adverb)+? #Verb",group:0,tag:"Auxiliary",reason:"have-had"},{match:"[about to] #Adverb? #Verb",group:0,tag:["Auxiliary","Verb"],reason:"about-to"},{match:"#Modal (#Adverb|not)+? [be] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"would-be"},{match:"[(#Modal|had|has)] (#Adverb|not)+? [been] (#Adverb|not)+? #Verb",group:0,tag:"Auxiliary",reason:"had-been"},{match:"[(be|being|been)] #Participle",group:0,tag:"Auxiliary",reason:"being-driven"},{match:"[may] #Adverb? #Infinitive",group:0,tag:"Auxiliary",reason:"may-want"},{match:"#Copula (#Adverb|not)+? [(be|being|been)] #Adverb+? #PastTense",group:0,tag:"Auxiliary",reason:"being-walked"},{match:"will [be] #PastTense",group:0,tag:"Auxiliary",reason:"will-be-x"},{match:"[(be|been)] (#Adverb|not)+? #Gerund",group:0,tag:"Auxiliary",reason:"been-walking"},{match:"[used to] #PresentTense",group:0,tag:"Auxiliary",reason:"used-to-walk"},{match:"#Copula (#Adverb|not)+? [going to] #Adverb+? #PresentTense",group:0,tag:"Auxiliary",reason:"going-to-walk"},{match:"#Imperative [(me|him|her)]",group:0,tag:"Reflexive",reason:"tell-him"},{match:"(is|was) #Adverb? [no]",group:0,tag:"Negative",reason:"is-no"},{match:"[(been|had|became|came)] #PastTense",group:0,notIf:"#PhrasalVerb",tag:"Auxiliary",reason:"been-told"},{match:"[(being|having|getting)] #Verb",group:0,tag:"Auxiliary",reason:"being-born"},{match:"[be] #Gerund",group:0,tag:"Auxiliary",reason:"be-walking"},{match:"[better] #PresentTense",group:0,tag:"Modal",notIf:"(#Copula|#Gerund)",reason:"better-go"},{match:"even better",tag:"Adverb #Comparative",reason:"even-better"}],[{match:"(#Verb && @hasHyphen) up",tag:"PhrasalVerb",reason:"foo-up"},{match:"(#Verb && @hasHyphen) off",tag:"PhrasalVerb",reason:"foo-off"},{match:"(#Verb && @hasHyphen) over",tag:"PhrasalVerb",reason:"foo-over"},{match:"(#Verb && @hasHyphen) out",tag:"PhrasalVerb",reason:"foo-out"},{match:"[#Verb (in|out|up|down|off|back)] (on|in)",notIf:"#Copula",tag:"PhrasalVerb Particle",reason:"walk-in-on"},{match:"(lived|went|crept|go) [on] for",group:0,tag:"PhrasalVerb",reason:"went-on"},{match:"#Verb (up|down|in|on|for)$",tag:"PhrasalVerb #Particle",notIf:"#PhrasalVerb",reason:"come-down$"},{match:"help [(stop|end|make|start)]",group:0,tag:"Infinitive",reason:"help-stop"},{match:"#PhrasalVerb (in && #Particle) #Determiner",tag:"#Verb #Preposition #Determiner",unTag:"PhrasalVerb",reason:"work-in-the"},{match:"[(stop|start|finish|help)] #Gerund",group:0,tag:"Infinitive",reason:"start-listening"},{match:"#Verb (him|her|it|us|himself|herself|itself|everything|something) [(up|down)]",group:0,tag:"Adverb",reason:"phrasal-pronoun-advb"}],[{match:"^do not? [#Infinitive #Particle?]",notIf:Ol,group:0,tag:"Imperative",reason:"do-eat"},{match:"^please do? not? [#Infinitive #Particle?]",group:0,tag:"Imperative",reason:"please-go"},{match:"^just do? not? [#Infinitive #Particle?]",group:0,tag:"Imperative",reason:"just-go"},{match:"^[#Infinitive] it #Comparative",notIf:Ol,group:0,tag:"Imperative",reason:"do-it-better"},{match:"^[#Infinitive] it (please|now|again|plz)",notIf:Ol,group:0,tag:"Imperative",reason:"do-it-please"},{match:"^[#Infinitive] (#Adjective|#Adverb)$",group:0,tag:"Imperative",notIf:"(so|such|rather|enough)",reason:"go-quickly"},{match:"^[#Infinitive] (up|down|over) #Determiner",group:0,tag:"Imperative",reason:"turn-down"},{match:"^[#Infinitive] (your|my|the|a|an|any|each|every|some|more|with|on)",group:0,notIf:"like",tag:"Imperative",reason:"eat-my-shorts"},{match:"^[#Infinitive] (him|her|it|us|me|there)",group:0,tag:"Imperative",reason:"tell-him"},{match:"^[#Infinitive] #Adjective #Noun$",group:0,tag:"Imperative",reason:"avoid-loud-noises"},{match:"^[#Infinitive] (#Adjective|#Adverb)? and #Infinitive",group:0,tag:"Imperative",reason:"call-and-reserve"},{match:"^(go|stop|wait|hurry) please?$",tag:"Imperative",reason:"go"},{match:"^(somebody|everybody) [#Infinitive]",group:0,tag:"Imperative",reason:"somebody-call"},{match:"^let (us|me) [#Infinitive]",group:0,tag:"Imperative",reason:"lets-leave"},{match:"^[(shut|close|open|start|stop|end|keep)] #Determiner #Noun",group:0,tag:"Imperative",reason:"shut-the-door"},{match:"^[#PhrasalVerb #Particle] #Determiner #Noun",group:0,tag:"Imperative",reason:"turn-off-the-light"},{match:"^[go] to .",group:0,tag:"Imperative",reason:"go-to-toronto"},{match:"^#Modal you [#Infinitive]",group:0,tag:"Imperative",reason:"would-you-"},{match:"^never [#Infinitive]",group:0,tag:"Imperative",reason:"never-stop"},{match:"^come #Infinitive",tag:"Imperative",notIf:"on",reason:"come-have"},{match:"^come and? #Infinitive",tag:"Imperative . Imperative",notIf:"#PhrasalVerb",reason:"come-and-have"},{match:"^stay (out|away|back)",tag:"Imperative",reason:"stay-away"},{match:"^[(stay|be|keep)] #Adjective",group:0,tag:"Imperative",reason:"stay-cool"},{match:"^[keep it] #Adjective",group:0,tag:"Imperative",reason:"keep-it-cool"},{match:"^do not [#Infinitive]",group:0,tag:"Imperative",reason:"do-not-be"},{match:"[#Infinitive] (yourself|yourselves)",group:0,tag:"Imperative",reason:"allow-yourself"},{match:"[#Infinitive] what .",group:0,tag:"Imperative",reason:"look-what"},{match:"^[#Infinitive] #Gerund",group:0,tag:"Imperative",reason:"keep-playing"},{match:"^[#Infinitive] (to|for|into|toward|here|there)",group:0,tag:"Imperative",reason:"go-to"},{match:"^[#Infinitive] (and|or) #Infinitive",group:0,tag:"Imperative",reason:"inf-and-inf"},{match:"^[%Noun|Verb%] to",group:0,tag:"Imperative",reason:"commit-to"},{match:"^[#Infinitive] #Adjective? #Singular #Singular",group:0,tag:"Imperative",reason:"maintain-eye-contact"},{match:"do not (forget|omit|neglect) to [#Infinitive]",group:0,tag:"Imperative",reason:"do-not-forget"},{match:"^[(ask|wear|pay|look|help|show|watch|act|fix|kill|stop|start|turn|try|win)] #Noun",group:0,tag:"Imperative",reason:"pay-attention"}],[{match:"(that|which) were [%Adj|Gerund%]",group:0,tag:"Gerund",reason:"that-were-growing"},{match:"#Gerund [#Gerund] #Plural",group:0,tag:"Adjective",reason:"hard-working-fam"}],[{match:"u r",tag:"#Pronoun #Copula",reason:"u r"},{match:"#Noun [(who|whom)]",group:0,tag:"Determiner",reason:"captain-who"},{match:"[had] #Noun+ #PastTense",group:0,tag:"Condition",reason:"had-he"},{match:"[were] #Noun+ to #Infinitive",group:0,tag:"Condition",reason:"were-he"},{match:"some sort of",tag:"Adjective Noun Conjunction",reason:"some-sort-of"},{match:"of some sort",tag:"Conjunction Adjective Noun",reason:"of-some-sort"},{match:"[such] (a|an|is)? #Noun",group:0,tag:"Determiner",reason:"such-skill"},{match:"[right] (before|after|in|into|to|toward)",group:0,tag:"#Adverb",reason:"right-into"},{match:"#Preposition [about]",group:0,tag:"Adjective",reason:"at-about"},{match:"(are|#Modal|see|do|for) [ya]",group:0,tag:"Pronoun",reason:"are-ya"},{match:"[long live] .",group:0,tag:"#Adjective #Infinitive",reason:"long-live"},{match:"[plenty] of",group:0,tag:"#Uncountable",reason:"plenty-of"},{match:"(always|nearly|barely|practically) [there]",group:0,tag:"Adjective",reason:"always-there"},{match:"[there] (#Adverb|#Pronoun)? #Copula",group:0,tag:"There",reason:"there-is"},{match:"#Copula [there] .",group:0,tag:"There",reason:"is-there"},{match:"#Modal #Adverb? [there]",group:0,tag:"There",reason:"should-there"},{match:"^[do] (you|we|they)",group:0,tag:"QuestionWord",reason:"do-you"},{match:"^[does] (he|she|it|#ProperNoun)",group:0,tag:"QuestionWord",reason:"does-he"},{match:"#Determiner #Noun+ [who] #Verb",group:0,tag:"Preposition",reason:"the-x-who"},{match:"#Determiner #Noun+ [which] #Verb",group:0,tag:"Preposition",reason:"the-x-which"},{match:"a [while]",group:0,tag:"Noun",reason:"a-while"},{match:"guess who",tag:"#Infinitive #QuestionWord",reason:"guess-who"},{match:"[fucking] !#Verb",group:0,tag:"#Gerund",reason:"f-as-gerund"}],[{match:"university of #Place",tag:"Organization",reason:"university-of-Foo"},{match:"#Noun (&|n) #Noun",tag:"Organization",reason:"Noun-&-Noun"},{match:"#Organization of the? #ProperNoun",tag:"Organization",reason:"org-of-place",safe:!0},{match:"#Organization #Country",tag:"Organization",reason:"org-country"},{match:"#ProperNoun #Organization",tag:"Organization",notIf:"#FirstName",reason:"titlecase-org"},{match:"#ProperNoun (ltd|co|inc|dept|assn|bros)",tag:"Organization",reason:"org-abbrv"},{match:"the [#Acronym]",group:0,tag:"Organization",reason:"the-acronym",safe:!0},{match:"government of the? [#Place+]",tag:"Organization",reason:"government-of-x"},{match:"(health|school|commerce) board",tag:"Organization",reason:"school-board"},{match:"(nominating|special|conference|executive|steering|central|congressional) committee",tag:"Organization",reason:"special-comittee"},{match:"(world|global|international|national|#Demonym) #Organization",tag:"Organization",reason:"global-org"},{match:"#Noun+ (public|private) school",tag:"School",reason:"noun-public-school"},{match:"#Place+ #SportsTeam",tag:"SportsTeam",reason:"place-sportsteam"},{match:"(dc|atlanta|minnesota|manchester|newcastle|sheffield) united",tag:"SportsTeam",reason:"united-sportsteam"},{match:"#Place+ fc",tag:"SportsTeam",reason:"fc-sportsteam"},{match:"#Place+ #Noun{0,2} (club|society|group|team|committee|commission|association|guild|crew)",tag:"Organization",reason:"place-noun-society"}],[{match:"(west|north|south|east|western|northern|southern|eastern)+ #Place",tag:"Region",reason:"west-norfolk"},{match:"#City [(al|ak|az|ar|ca|ct|dc|fl|ga|id|il|nv|nh|nj|ny|oh|pa|sc|tn|tx|ut|vt|pr)]",group:0,tag:"Region",reason:"us-state"},{match:"portland [or]",group:0,tag:"Region",reason:"portland-or"},{match:"#ProperNoun+ (cliff|place|range|pit|place|point|room|grounds|ruins)",tag:"Place",reason:"foo-point"},{match:"in [#ProperNoun] #Place",group:0,tag:"Place",reason:"propernoun-place"},{match:"#Value #Noun (st|street|rd|road|crescent|cr|way|tr|terrace|avenue|ave)",tag:"Address",reason:"address-st"},{match:"(port|mount|mt) #ProperName",tag:"Place",reason:"port-name"}],[{match:"[so] #Noun",group:0,tag:"Conjunction",reason:"so-conj"},{match:"[(who|what|where|why|how|when)] #Noun #Copula #Adverb? (#Verb|#Adjective)",group:0,tag:"Conjunction",reason:"how-he-is-x"},{match:"#Copula [(who|what|where|why|how|when)] #Noun",group:0,tag:"Conjunction",reason:"when-he"},{match:"#Verb [that] #Pronoun",group:0,tag:"Conjunction",reason:"said-that-he"},{match:"#Noun [that] #Copula",group:0,tag:"Conjunction",reason:"that-are"},{match:"#Noun [that] #Verb #Adjective",group:0,tag:"Conjunction",reason:"that-seem"},{match:"#Noun #Copula not? [that] #Adjective",group:0,tag:"Adverb",reason:"that-adj"},{match:"#Verb #Adverb? #Noun [(that|which)]",group:0,tag:"Preposition",reason:"that-prep"},{match:"@hasComma [which] (#Pronoun|#Verb)",group:0,tag:"Preposition",reason:"which-copula"},{match:"#Noun [like] #Noun",group:0,tag:"Preposition",reason:"noun-like"},{match:"^[like] #Determiner",group:0,tag:"Preposition",reason:"like-the"},{match:"a #Noun [like] (#Noun|#Determiner)",group:0,tag:"Preposition",reason:"a-noun-like"},{match:"#Adverb [like]",group:0,tag:"Verb",reason:"really-like"},{match:"(not|nothing|never) [like]",group:0,tag:"Preposition",reason:"nothing-like"},{match:"#Infinitive #Pronoun [like]",group:0,tag:"Preposition",reason:"treat-them-like"},{match:"[#QuestionWord] (#Pronoun|#Determiner)",group:0,tag:"Preposition",reason:"how-he"},{match:"[#QuestionWord] #Participle",group:0,tag:"Preposition",reason:"when-stolen"},{match:"[how] (#Determiner|#Copula|#Modal|#PastTense)",group:0,tag:"QuestionWord",reason:"how-is"},{match:"#Plural [(who|which|when)] .",group:0,tag:"Preposition",reason:"people-who"}],[{match:"holy (shit|fuck|hell)",tag:"Expression",reason:"swears-expression"},{match:"^[(well|so|okay|now)] !#Adjective?",group:0,tag:"Expression",reason:"well-"},{match:"^come on",tag:"Expression",reason:"come-on"},{match:"(say|says|said) [sorry]",group:0,tag:"Expression",reason:"say-sorry"},{match:"^(ok|alright|shoot|hell|anyways)",tag:"Expression",reason:"ok-"},{match:"^(say && @hasComma)",tag:"Expression",reason:"say-"},{match:"^(like && @hasComma)",tag:"Expression",reason:"like-"},{match:"^[(dude|man|girl)] #Pronoun",group:0,tag:"Expression",reason:"dude-i"}]);let Vl=null;var Bl={postTagger:function(e){const{world:t}=e,{model:n,methods:a}=t;Vl=Vl||a.one.buildNet(n.two.matches,t);let r=a.two.quickSplit(e.document).map((e=>{let t=e[0];return[t.index[0],t.index[1],t.index[1]+e.length]})),o=e.update(r);return o.cache(),o.sweep(Vl),e.uncache(),e.unfreeze(),e},tagger:e=>e.compute(["freeze","lexicon","preTagger","postTagger","unfreeze"])};const Sl={api:function(e){e.prototype.confidence=function(){let e=0,t=0;return this.docs.forEach((n=>{n.forEach((n=>{t+=1,e+=n.confidence||1}))})),0===t?1:(e=>Math.round(100*e)/100)(e/t)},e.prototype.tagger=function(){return this.compute(["tagger"])}},compute:Bl,model:{two:{matches:zl}},hooks:["postTagger"]},Kl=function(e,t){let n=function(e){return Object.keys(e.hooks).filter((e=>!e.startsWith("#")&&!e.startsWith("%")))}(t);if(0===n.length)return e;e._cache||e.cache();let a=e._cache;return e.filter(((e,t)=>n.some((e=>a[t].has(e)))))};var $l={lib:{lazy:function(e,t){let n=t;"string"==typeof t&&(n=this.buildNet([{match:t}]));let a=this.tokenize(e),r=Kl(a,n);return r.found?(r.compute(["index","tagger"]),r.match(t)):a.none()}}};const Ll=function(e,t,n){let a=e.split(/ /g).map((e=>e.toLowerCase().trim()));a=a.filter((e=>e)),a=a.map((e=>`{${e}}`)).join(" ");let r=this.match(a);return n&&(r=r.if(n)),r.has("#Verb")?function(e,t){let n=t;return e.forEach((e=>{e.has("#Infinitive")||(n=function(e,t){let n=(0,e.methods.two.transform.verb.conjugate)(t,e.model);return e.has("#Gerund")?n.Gerund:e.has("#PastTense")?n.PastTense:e.has("#PresentTense")?n.PresentTense:e.has("#Gerund")?n.Gerund:t}(e,t)),e.replaceWith(n)})),e}(r,t):r.has("#Noun")?function(e,t){let n=t;e.has("#Plural")&&(n=(0,e.methods.two.transform.noun.toPlural)(t,e.model));e.replaceWith(n,{possessives:!0})}(r,t):r.has("#Adverb")?function(e,t){const{toAdverb:n}=e.methods.two.transform.adjective;let a=n(t);a&&e.replaceWith(a)}(r,t):r.has("#Adjective")?function(e,t){const{toComparative:n,toSuperlative:a}=e.methods.two.transform.adjective;let r=t;e.has("#Comparative")?r=n(r,e.model):e.has("#Superlative")&&(r=a(r,e.model)),r&&e.replaceWith(r)}(r,t):this};var Ml={api:function(e){e.prototype.swap=Ll}};d.plugin(ul),d.plugin(El),d.plugin(Sl),d.plugin($l),d.plugin(Ml);export{d as default};
